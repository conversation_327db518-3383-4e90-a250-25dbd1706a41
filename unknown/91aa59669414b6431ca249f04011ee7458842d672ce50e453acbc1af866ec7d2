package com.illumio.data.mapper;

import com.illumio.data.model.FindingResource;
import com.illumio.data.model.wiz.WizResource;
import lombok.experimental.UtilityClass;

import java.util.Optional;

import static com.illumio.data.util.DataSyncConstants.AZURE;
import static com.illumio.data.util.DataSyncConstants.EXTRACT_AZURE_ID_REGEX;

@UtilityClass
public class WizResourceMapper {

    public static FindingResource toFindingResource(WizResource resource) {
        String cloudProviderUniqueId = AZURE.equalsIgnoreCase(resource.getCloudPlatform())
                ? extractAzureResourceId(resource.getCloudProviderURL())
                : resource.getProviderUniqueId();

        return FindingResource.builder()
                              .name(resource.getName())
                              .type(resource.getType())
                              .cloudProviderUniqueId(cloudProviderUniqueId)
                              .subscriptionId(resource.getSubscriptionExternalId())
                              .cloudPlatform(resource.getCloudPlatform())
                              .build();
    }

    private static String extractAzureResourceId(String cloudProviderUrl) {
        cloudProviderUrl = Optional.ofNullable(cloudProviderUrl).orElse("");
        String[] parts = cloudProviderUrl.split(EXTRACT_AZURE_ID_REGEX, 2);
        return parts.length > 1 ? parts[1] : cloudProviderUrl;
    }

}