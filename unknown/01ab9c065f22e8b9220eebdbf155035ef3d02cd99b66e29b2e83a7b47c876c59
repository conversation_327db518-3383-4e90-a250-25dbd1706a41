apiVersion: v1
kind: Service
metadata:
  name: {{ include "FindingsApi.name" . }}
  labels:
    {{- include "FindingsApi.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "FindingsApi.selectorLabels" . | nindent 4 }}
