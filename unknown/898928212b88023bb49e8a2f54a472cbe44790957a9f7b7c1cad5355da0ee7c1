apiVersion: v1
kind: Secret
metadata:
  name: {{ include "FindingsPersistence.fullname" . }}-env-secrets
  labels:
    {{- include "FindingsPersistence.labels" . | nindent 4 }}
type: Opaque
stringData:
  SPRING_R2DBC_PASSWORD: {{ .Values.spring.r2dbc.password }}
  FINDINGSPERSISTENCE_KAFKACONSUMERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.findingsPersistence.kafkaConsumerConfig.connectionString }}";
  FINDINGSPERSISTENCE_KAFKAPRODUCERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.findingsPersistence.kafkaProducerConfig.connectionString }}";