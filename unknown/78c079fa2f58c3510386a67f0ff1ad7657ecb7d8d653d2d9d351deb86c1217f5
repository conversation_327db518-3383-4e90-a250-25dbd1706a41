{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "IntegrationsManager.fullname" . }}
  labels:
    {{- include "IntegrationsManager.labels" . | nindent 4 }}
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    {{- if .Values.ingress.certManager.enabled }}
    cert-manager.io/cluster-issuer: {{ .Values.ingress.certManager.clusterIssuer }}
    {{- end }}
    {{- with .Values.ingress.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
    - host: {{ .Values.ingress.fqdn }}
      http:
        paths:
          {{- range .Values.ingress.pathPrefixes }}
          - path: {{ . }}
            pathType: Prefix
            backend:
              service:
                name: {{ include "IntegrationsManager.name" $ }}
                port:
                  number: 8080
          {{- end }}
  tls:
    - hosts:
        - {{ .Values.ingress.fqdn }}
      secretName: {{ .Values.ingress.tlsSecretName }}
{{- end }}
