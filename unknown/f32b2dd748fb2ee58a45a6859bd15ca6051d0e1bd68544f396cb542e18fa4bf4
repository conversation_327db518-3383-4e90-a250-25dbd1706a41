package com.illumio.data.components.controller;

import com.illumio.data.FindingsServiceContext;
import com.illumio.data.api.FindingsApiDelegate;
import com.illumio.data.AuthValidationService;
import com.illumio.data.components.model.FindingsRequest;
import com.illumio.data.model.FindingsResponse;
import com.illumio.data.model.FindingsSearchRequest;
import com.illumio.data.model.GetColumnsMetadata200Response;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@RequiredArgsConstructor
@RestController
@Slf4j
public class FindingsApiDelegateImpl implements FindingsApiDelegate {

    private final AuthValidationService authValidationService;
    private final FindingsServiceContext findingsServiceContext;

    @Override
    public Mono<ResponseEntity<FindingsResponse>> getFindingsDataByWorkloadId(
            String cspId,
            String subscriptionId,
            String findingsType,
            String cloudProviderName,
            String serviceType,
            Integer page,
            Integer limit,
            String sortBy,
            String sortOrder,
            ServerWebExchange exchange) {

        log.debug(
                "Received request to get findings data by cspId: {}, subscriptionId: {}, findingsType: {}",
                cspId,
                subscriptionId,
                findingsType);

        return authValidationService
                .extractAndCheckAuthorization(exchange)
                .doOnError(error ->
                        log.error("Authorization check failed for get findings data by cspId: {}, subscriptionId: {}, findingsType: {}:",
                                cspId,
                                subscriptionId,
                                findingsType,
                                error))
                .then(
                        Mono.defer(
                                () -> {
                                    FindingsRequest findingsRequest =
                                            FindingsRequest.builder()
                                                           .cspId(cspId)
                                                           .subscriptionId(subscriptionId)
                                                           .cloudProviderName(cloudProviderName)
                                                           .findingsType(findingsType)
                                                           .serviceType(serviceType)
                                                           .page(page)
                                                           .limit(limit)
                                                           .sortBy(sortBy)
                                                           .sortOrder(sortOrder)
                                                           .build();

                                    return findingsServiceContext
                                            .getService(findingsType)
                                            .getFindingsDataByWorkloadId(findingsRequest)
                                            .doOnNext(
                                                    response ->
                                                            log.debug(
                                                                    "Successfully retrieved findings data for cspId ID: {}, subscriptionId: {}, findingsType: {}}",
                                                                    cspId, subscriptionId, findingsType))
                                            .doOnError(
                                                    error ->
                                                            log.error(
                                                                    "Error occurred while retrieving findings data for cspId ID: {}, subscriptionId: {}, findingsType: {}:",
                                                                    cspId,
                                                                    subscriptionId,
                                                                    findingsType,
                                                                    error))
                                            .map(ResponseEntity::ok);
                                }));
    }

    @Override
    public Mono<ResponseEntity<FindingsResponse>> searchFindingsData(
            String cspId,
            String subscriptionId,
            String findingsType,
            String cloudProviderName,
            Mono<FindingsSearchRequest> findingsSearchRequest,
            String serviceType,
            ServerWebExchange exchange) {
        log.debug(
                "Received request to search findings data by cspId: {}, subscriptionId: {}, findingsType: {}",
                cspId,
                subscriptionId,
                findingsType);
        return authValidationService.extractAndCheckAuthorization(exchange)
                                    .doOnError(error ->
                                            log.error("Authorization check failed for search findings data by cspId: {}, subscriptionId: {}, findingsType: {}:",
                                                    cspId,
                                                    subscriptionId,
                                                    findingsType,
                                                    error))
                                    .then(findingsSearchRequest)
                                    .flatMap(searchRequest -> {
                                        FindingsRequest findingsRequest = FindingsRequest.builder()
                                                                                         .cspId(cspId)
                                                                                         .subscriptionId(subscriptionId)
                                                                                         .findingsType(findingsType)
                                                                                         .cloudProviderName(cloudProviderName)
                                                                                         .serviceType(serviceType)
                                                                                         .findingsSearchRequest(searchRequest)
                                                                                         .build();

                                        return findingsServiceContext.getService(findingsType)
                                                                     .searchFindingsDataByWorkloadId(findingsRequest)
                                                                     .doOnNext(response -> log.debug("Successfully searched findings data for cspId ID: {}, subscriptionId: {}, findingsType: {}", cspId, subscriptionId, findingsType))
                                                                     .doOnError(error -> log.error("Error occurred while searching findings data for cspId ID: {}, subscriptionId: {}, findingsType: {}", cspId, subscriptionId, findingsType, error))
                                                                     .map(ResponseEntity::ok);
                                    });
    }

    @Override
    public Mono<ResponseEntity<GetColumnsMetadata200Response>> getColumnsMetadata(
            String findingsType, ServerWebExchange exchange) {
        log.debug("Received request to fetch column data for findings type: {}",
                findingsType);
        return authValidationService
                .extractAndCheckAuthorization(exchange)
                .doOnError(error ->
                        log.error("Authorization check failed for get column data for findingsType: {}:",
                                findingsType,
                                error))
                .then(findingsServiceContext.getService(findingsType)
                                            .getColumnsMetadata()
                                            .doOnNext(
                                                    response -> log.debug("Successfully retrieved columns metadata for findingsType: {}", findingsType))
                                            .doOnError(
                                                    error -> log.error("Failed to retrieve columns metadata for findingsType: {}", findingsType, error))
                                            .map(ResponseEntity::ok));
    }

}
