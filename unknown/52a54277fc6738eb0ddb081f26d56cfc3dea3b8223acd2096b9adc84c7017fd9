package com.illumio.data.mapper;

import com.illumio.data.model.Integration;
import com.illumio.data.model.Vulnerability;
import com.illumio.data.model.VulnerabilityMetadata;
import com.illumio.data.model.wiz.WizVulnerabilityFinding;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WizVulnerabilityMapper {

    public static Vulnerability toVulnerability(WizVulnerabilityFinding wizVulnerabilityFinding, String tenantId, Integration integration) {
        return Vulnerability.builder()
                            .status(wizVulnerabilityFinding.getStatus())
                            .tenantId(tenantId)
                            .integration(integration)
                            .resource(WizResourceMapper.toFindingResource(wizVulnerabilityFinding.getResource()))
                            .metadata(mapVulnerabilityMetadata(wizVulnerabilityFinding))
                            .build();
    }

    private static VulnerabilityMetadata mapVulnerabilityMetadata(WizVulnerabilityFinding wizVulnerabilityFinding) {
        return VulnerabilityMetadata.builder()
                                    .cveName(wizVulnerabilityFinding.getCveName())
                                    .cveDescription(wizVulnerabilityFinding.getCveDescription())
                                    .severity(wizVulnerabilityFinding.getSeverity())
                                    .cvssSeverity(wizVulnerabilityFinding.getCvssSeverity())
                                    .score(wizVulnerabilityFinding.getScore())
                                    .impactScore(wizVulnerabilityFinding.getImpactScore())
                                    .exploitabilityScore(wizVulnerabilityFinding.getExploitabilityScore())
                                    .hasExploit(wizVulnerabilityFinding.getHasExploit())
                                    .hasCisaKevExploit(wizVulnerabilityFinding.getHasCisaKevExploit())
                                    .vulnerableComponentName(wizVulnerabilityFinding.getVulnerableComponentName())
                                    .firstDetectedAt(wizVulnerabilityFinding.getFirstDetectedAt())
                                    .lastDetectedAt(wizVulnerabilityFinding.getLastDetectedAt())
                                    .build();
    }
}