package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaConsumerConfig {
    private final FindingsPersistenceConfiguration findingsPersistenceConfiguration;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiverOptions() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                findingsPersistenceConfiguration.getKafkaConsumerConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                findingsPersistenceConfiguration.getKafkaConsumerConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        if (Optional.of(findingsPersistenceConfiguration)
                    .map(FindingsPersistenceConfiguration::getKafkaConsumerConfig)
                    .map(FindingsPersistenceConfiguration.KafkaConsumerConfig::getIsConnectionString)
                    .orElse(Boolean.FALSE)) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    findingsPersistenceConfiguration.getKafkaConsumerConfig().getSaslJaasConfig());
        }

        if (Optional.of(findingsPersistenceConfiguration)
                    .map(FindingsPersistenceConfiguration::getKafkaConsumerConfig)
                    .map(FindingsPersistenceConfiguration.KafkaConsumerConfig::getAutoOffsetReset)
                    .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                    findingsPersistenceConfiguration.getKafkaConsumerConfig().getAutoOffsetReset());
        }

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                               .subscription(
                                       Collections.singleton(
                                               findingsPersistenceConfiguration.getKafkaConsumerConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}