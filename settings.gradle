rootProject.name = 'integrations'
include 'commons:auth-integrations'
include 'commons:data-commons'
include 'commons:launchdarkly-commons'
include 'services:integrations-manager'
include 'services:integration-data-sync'
include 'services:findings-persistence'
include 'commons:auth-ui'
include 'services:task-manager'
include 'commons:utility-commons'
include 'services:findings-api'
include 'services:integration-data-translator'