#!/bin/bash

# Set the timeout for Docker Compose to wait for services to stop
TIMEOUT=60

# Set the path to the Docker Compose file
DOCKER_COMPOSE_FILE="../docker/docker-compose.yml"

# Check if the Docker Compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  echo "Error: Docker Compose file '$DOCKER_COMPOSE_FILE' not found."
  exit 1
fi

# Change to the directory containing the Docker Compose file
cd "$(dirname "$DOCKER_COMPOSE_FILE")"

# Get a list of running containers before shutdown
RUNNING_CONTAINERS=$(docker-compose ps -q)

if [ -z "$RUNNING_CONTAINERS" ]; then
  echo "No containers are currently running."
  exit 0
fi

echo "Stopping all containers..."

# Stop and remove containers, networks, and volumes
docker-compose down --timeout $TIMEOUT

# Verify all containers have been stopped
sleep 5

# Check if any containers from our compose file are still running
REMAINING_CONTAINERS=$(docker-compose ps -q)

if [ -n "$REMAINING_CONTAINERS" ]; then
  echo "Error: Some containers failed to stop properly."

  # Force kill any remaining containers
  echo "Attempting to force stop remaining containers..."
  docker-compose kill

  # Remove containers
  docker-compose rm -f

  echo "All containers have been forcefully stopped and removed."
else
  echo "All services stopped successfully."
fi
