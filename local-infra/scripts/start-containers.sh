#!/bin/bash

# Set the timeout for Docker Compose to wait for services to start
TIMEOUT=60

# Set the path to the Docker Compose file
DOCKER_COMPOSE_FILE="../docker/docker-compose.yml"

# Check if the Docker Compose file exists
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  echo "Error: Docker Compose file '$DOCKER_COMPOSE_FILE' not found."
  exit 1
fi


cd "$(dirname "$DOCKER_COMPOSE_FILE")"


docker-compose up -d --timeout $TIMEOUT


sleep 5


CONTAINERS=$(docker-compose ps -q)

# Check the status of each container
for CONTAINER in $CONTAINERS; do
  CONTAINER_NAME=$(docker inspect -f '{{.Name}}' $CONTAINER)

  SERVICE=${CONTAINER_NAME#*_}

  RUNNING=$(docker inspect -f '{{.State.Running}}' $CONTAINER)
  if [ "$RUNNING" != "true" ]; then
    echo "Error: $SERVICE is not running."
    exit 1
  fi

  HEALTH_STATUS=$(docker inspect --format='{{json .State.Health}}' $CONTAINER 2>/dev/null)
  if [ "$HEALTH_STATUS" != "" ]; then
    HEALTH=$(docker inspect -f '{{.State.Health.Status}}' $CONTAINER 2>/dev/null)
    if [ "$HEALTH" != "healthy" ] && [ "$HEALTH" != "" ]; then
      echo "Error: $SERVICE health check failed with status: $HEALTH"
      exit 1
    else
      echo "Info: $SERVICE is healthy."
    fi
  else
    echo "Info: $SERVICE does not have health checks defined."
  fi
done

echo "All services started successfully."
