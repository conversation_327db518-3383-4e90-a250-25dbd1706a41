#!/bin/bash

# Define the directories and file paths
directories=(
  "../../services/task-manager/src/main/resources"
  "../../services/integration-data-sync/src/main/resources"
  "../../services/findings-api/src/main/resources"
  "../../services/integrations-manager/src/main/resources"
  "../../services/findings-persistence/src/main/resources"
  "../../services/integration-data-translator/src/main/resources"
)

# Define the content for each application-local-docker.yml file
declare -a contents
contents[0]="
logging:
  level:
    ROOT: INFO
spring:
  application:
    name: task-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

  #  Postgres db config
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/integrations
    username: admin
    password: admin
  data:
    r2dbc:
      repositories:
        enabled: true

  # Cosmos no SQL Database Config
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: <db_key>
        database: integrations

server:
  port: 8081

launch-darkly:
  sdk-key: <ld_sdk_key>

task-manager:
  task-purge-config:
    slow-task-purge-time: 300000
    fast-task-purge-time: 60000
  node-config:
    fast-node: true

  kafka-consumer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    topic: task-status-update-queue
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  kafka-light-task-producer-config:
    bootstrapServers: localhost:29092
    topic: light-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false

  kafka-heavy-task-producer-config:
    bootstrapServers: localhost:29092
    topic: heavy-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false

  long-running-task-config:
    purge-check-schedule-duration: 1d
    delta-long-running-window: 50m
    green-field-long-running-window: 2d
"

contents[1]="
logging:
  level:
    ROOT: INFO

spring:
  application:
    name: integration-data-sync
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
vault:
  uri: \"http://127.0.0.1:8200\"
  token: \"myroot\"
  namespace: admin

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

web-client-config:
  max-in-memory-size: 6000000

integration-data-sync:
  task-consumer-mode: HEAVY
  integrations-config:
    wiz:
      projects-api-max-page-size: 500
      findings-apis-max-page-size: 50
      sync-data-window-length-fallback: 1d
      min-time-between-report-creation-calls: 1s

  kafka-task-status-producer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    topic: task-status-update-queue

  kafka-findings-producer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    topic: vulnerabilities-queue

  kafka-light-task-consumer-config:
    topic: light-task-queue
    isConnectionString: false
    bootstrapServers: localhost:29092
    groupId: integration-data-sync
    autoOffsetReset: earliest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10

  kafka-heavy-task-consumer-config:
    topic: heavy-task-queue
    isConnectionString: false
    bootstrapServers: localhost:29092
    groupId: integration-data-sync
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10
"

contents[2]="
logging:
  level:
    ROOT: INFO
spring:
  application:
    name: findings-api
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/integrations
    username: admin
    password: admin
  springdoc:
    swagger-ui:
      path: /swagger-ui.html
server:
  port: 8084
census:
  auth:
    enabled: true
grpc:
  channel:
    host: dev-census-grpc.console.ilabs.io
    port: 443
    enableMtls: false
    caCert: ""
    mtlsKey: ""
    mtlsCert: ""
  server:
    port: 9090


"

contents[3]="
logging:
  level:
    ROOT: INFO
spring:
  ssl:
    disable-certificate-verification: true

  application:
    name: integrations-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: <db_key>
        database: integrations

  r2dbc:
    url: r2dbc:postgresql://localhost:5432/integrations
    username: admin
    password: admin

launch-darkly:
  sdk-key: <ld_sdk_key>

census:
  auth:
    enabled: true

grpc:
  channel:
    host: dev-census-grpc.console.ilabs.io
    port: 443
    enableMtls: false
    caCert: ""
    mtlsKey: ""
    mtlsCert: ""
  server:
    port: 9091

server:
  port: 80

vault:
  uri: \"http://127.0.0.1:8200\"
  token: \"myroot\"
  namespace: admin

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

integrations-manager:
  integrations-config:
    wiz:
      name: Wiz
      description: Ingest cloud security issues and vulnerabilities from Wiz
      api-host: wiz.io
    armis:
      name: Armis
      description: Import Armis IoT/OT inventory and asset info to Illumio
  kafka-light-task-producer-config:
    bootstrapServers: localhost:29092
    topic: light-task-queue
    isConnectionString: false

  kafka-heavy-task-producer-config:
    bootstrapServers: localhost:29092
    topic: heavy-task-queue
    isConnectionString: false
"

contents[4]="
logging:
  level:
    ROOT: INFO
server:
  port: 8080
spring:
  application:
    name: findings-persistence
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

  r2dbc:
    url: r2dbc:postgresql://localhost:5432/integrations
    username: admin
    password: admin

findings-persistence:
  kafka-consumer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    topic: vulnerabilities-queue
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  "
contents[5]="
logging:
  level:
    ROOT: INFO
server:
  port: 8088
spring:
  application:
    name: integration-data-translator
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

integration-data-translator:
  mapping:
    rules-path: \${user.dir}/local-dev/mapping-rules

  kafka-consumer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    groupId: integration-data-translator
    topic: json-input-queue
  kafka-producer-config:
    bootstrapServers: localhost:29092
    isConnectionString: false
    topic: mapped-data-queue
"

# Check if each directory exists and create the application-local-docker.yml file if it doesn't exist
for ((i=0; i<${#directories[@]}; i++)); do
  directory=${directories[$i]}
  service_name=$(basename $(dirname $(dirname "$directory")))

  case $service_name in
    task-manager) service_index=0 ;;
    integration-data-sync) service_index=1 ;;
    findings-api) service_index=2 ;;
    integrations-manager) service_index=3 ;;
    findings-persistence) service_index=4 ;;
    integration-data-translator) service_index=5 ;;
  esac


  if [ ! -d "$directory" ]; then
    echo "Error: Directory '$directory' does not exist."
    exit 1
  fi


  file_path="$directory/application-local-docker.yml"


  echo "${contents[$i]}" >| "$file_path"
  echo "File created/overwritten successfully: $file_path"
done

# Create local-dev/mapping-rules directory structure for integration-data-translator
TRANSLATOR_BASE_DIR="../../services/integration-data-translator"
MAPPING_RULES_DIR="$TRANSLATOR_BASE_DIR/local-dev/mapping-rules"

echo ""
echo "Setting up mapping rules for integration-data-translator..."

# Create the directory structure
mkdir -p "$MAPPING_RULES_DIR"

# Create boundary-data.json
cat > "$MAPPING_RULES_DIR/boundary-data.json" << 'EOF'
{
  "name": "BoundaryDataMapping",
  "description": "Mapping configuration for boundary meta data",
  "fieldMappings": {
    "boundaryId": "data.boundaries[*].id",
    "name": "data.boundaries[*].name",
    "affectedSites": "data.boundaries[*].affectedSites"
  }
}
EOF


# Create device-data.json
cat > "$MAPPING_RULES_DIR/device-data.json" << 'EOF'
{
  "name": "DeviceDataMapping",
  "description": "Mapping configuration for device meta data",
  "fieldMappings": {
    "deviceId": "data.results[*].id",
    "displayTitle": "data.results[*].displayTitle",
    "name": "data.results[*].name",
    "names": "data.results[*].names",
    "manufacturer": "data.results[*].manufacturer",
    "type": "data.results[*].type",
    "model": "data.results[*].model",
    "firstSeen": "data.results[*].firstSeen",
    "lastSeen": "data.results[*].lastSeen",
    "macAddress": "data.results[*].macAddress",
    "ipv4": "data.results[*].ipAddress",
    "ipv6": "data.results[*].ipv6[@collect]",
    "accessSwitch": "data.results[*].accessSwitch",
    "tags": "data.results[*].tags[@collect]",
    "purdueLevel": "data.results[*].purdueLevel",
    "riskScore": "data.results[*].riskLevel",
    "operatingSystem": "data.results[*].operatingSystem",
    "protections": "data.results[*].protections[@object]",
    "sensor": "data.results[*].sensor",
    "tier": "data.results[*].tier",
    "typeEnum": "data.results[*].typeEnum",
    "visibility": "data.results[*].visibility",
    "siteId": "data.results[*].site.id",
    "categoryId": "data.results[*].category.id",
    "boundaryId": "data.results[*].boundaries.id",
    "dataSources": "data.results[*].dataSources[@object]"
  }
}
EOF

# Create issue-data.json
cat > "$MAPPING_RULES_DIR/issue-data.json" << 'EOF'
{
  "name": "IssueDataMapping",
  "description": "Mapping configuration for issue meta data",
  "fieldMappings": {
    "name": "data.issues.nodes[*].sourceRules[0].name",
    "description": "data.issues.nodes[*].sourceRules[0].controlDescription",
    "subscriptionId": "data.issues.nodes[*].entitySnapshot.subscriptionExternalId",
    "severity": "data.issues.nodes[*].severity",
    "status": "data.issues.nodes[*].status",
    "risks": "data.issues.nodes[*].sourceRules[0].risks[@collect]"
  }
}
EOF

# Create site-data.json
cat > "$MAPPING_RULES_DIR/site-data.json" << 'EOF'
{
  "name": "SiteDataMapping",
  "description": "Mapping configuration for site data",
  "fieldMappings": {
    "siteId": "data.sites[*].id",
    "name": "data.sites[*].name",
    "location": "data.sites[*].location",
    "lat": "data.sites[*].lat",
    "lng": "data.sites[*].lng",
    "parentId": "data.sites[*].parentId",
    "networkEquipmentDeviceIds": "data.sites[*].networkEquipmentDeviceIds[@collect]"
  }
}
EOF

# Create vulnerability-data.json
cat > "$MAPPING_RULES_DIR/vulnerability-data.json" << 'EOF'
 {
   "name": "VulnerabilityDataMapping",
   "description": "Mapping configuration for vulnerability meta data",
   "fieldMappings": {
     "cveId": "data.vulnerabilityFindings.nodes[*].name",
     "description": "data.vulnerabilityFindings.nodes[*].CVEDescription",
     "severity": "data.vulnerabilityFindings.nodes[*].severity",
     "cvssScore": "data.vulnerabilityFindings.nodes[*].score",
     "hasExploit": "data.vulnerabilityFindings.nodes[*].hasExploit",
     "hasCisaKevExploit": "data.vulnerabilityFindings.nodes[*].hasCisaKevExploit",
     "status": "data.vulnerabilityFindings.nodes[*].status",
     "firstDetectedAt": "data.vulnerabilityFindings.nodes[*].firstDetectedAt",
     "lastDetectedAt": "data.vulnerabilityFindings.nodes[*].lastDetectedAt",
     "workloadId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.providerUniqueId",
     "workloadName": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.name",
     "cloudPlatform": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.cloudPlatform",
     "region": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.region",
     "subscriptionId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.subscriptionExternalId",
     "operatingSystem": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.operatingSystem",
     "remediation": "data.vulnerabilityFindings.nodes[*].remediation",
     "detectionMethod": "data.vulnerabilityFindings.nodes[*].detectionMethod",
     "packageName": "data.vulnerabilityFindings.nodes[*].detailedName",
     "packageVersion": "data.vulnerabilityFindings.nodes[*].version",
     "fixedVersion": "data.vulnerabilityFindings.nodes[*].fixedVersion"
   }
 }

EOF


echo "Mapping rules created successfully in: $MAPPING_RULES_DIR"
echo "Created mapping files:"
ls -la "$MAPPING_RULES_DIR"/*.json

echo ""
echo "All configuration files have been generated or overwritten."
echo ""