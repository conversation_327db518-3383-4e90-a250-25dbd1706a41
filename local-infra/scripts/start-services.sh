#!/bin/bash

base_path=$(pwd)/../../

services=(
  "integrations-manager"
  "integration-data-sync"
  "findings-persistence"
  "task-manager"
  "findings-api"
  "integration-data-translator"
)

# Function to terminate a service by name
terminate_service() {
  local service_name=$1
  local pid=$(pgrep -f "$service_name")
  if [ -n "$pid" ]; then
    echo "Terminating $service_name..."
    kill $pid
    sleep 2
  fi
}

# Terminate all running services
for service in "${services[@]}"; do
  terminate_service "$service"
done

# Close all Terminal windows that are running these services
echo "Closing all terminal windows running services..."
osascript <<EOF
tell application "Terminal"
  set windowCount to count of windows
  repeat with i from windowCount to 1 by -1
    set currentWindow to window i
    set windowContents to (do script "echo \$BASH_COMMAND" in currentWindow)

    repeat with serviceName in {"integrations-manager", "integration-data-sync", "findings-persistence", "task-manager", "findings-api"}
      if windowContents contains serviceName then
        close currentWindow
        exit repeat
      end if
    end repeat
  end repeat
end tell
EOF

# Alternative approach to close all Terminal windows
echo "Closing any remaining terminal windows..."
osascript <<EOF
tell application "Terminal"
  if (count of windows) > 0 then
    display dialog "Do you want to close all Terminal windows?" buttons {"Yes", "No"} default button "Yes"
    if button returned of result is "Yes" then
      close every window
    end if
  end if
end tell
EOF

echo "All services have been terminated and terminals closed."


build_and_run_service() {
  local service_name=$1
  local config_path="$base_path/services/$service_name/src/main/resources/application-local-docker.yml"

  terminate_service "$service_name"

  echo "Building and running $service_name..."

  osascript -e 'tell application "Terminal" to do script "cd '"$base_path"'; export SPRING_CONFIG_LOCATION='"$config_path"'; ./gradlew :service:'"$service_name"':clean; ./gradlew :service:'"$service_name"':build; ./gradlew :service:'"$service_name"':test; ./gradlew :service:'"$service_name"':bootRun"'
}

# Build and run each service in a new terminal
for service in "${services[@]}"; do
  build_and_run_service "$service"
done
