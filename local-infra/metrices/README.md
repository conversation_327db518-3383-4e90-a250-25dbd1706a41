### Steps to setup and view observability metrics in local

#### Requirements:
* Java 17 SDK
* otel exporter jar file

#### Setup docker services
1. ```cd local-infra/scripts```
2. ```bash start-containers.sh```

This will start the following services in your local machine
* postgres
* kafka
* vault
* pgadmin
* kafka-ui
* zookepper
* prometheus
* grafana
  you can verify all service by executing ```docker ps```

**Note:** kafka container might exit in first attempt as zookeeper sometimes takes longer than expected to start. If you can't see kafka container up and running (docker ps) then re-run the script and now kafka should be up and running

#### Generating config files:
1.  ```cd local-infra/metrices```
2. ``` bash generate-jib-configs.sh```

This will generate **application-local-docker-jib.yml** file in resource directory of respective services

**Note**: As we are using cloud cosmos and LaunchDarkly. You will need to pass correct cosmos and LD key in the config for integration-manager and task-manager application-docker-local-jib.yml file after the configs have been generated

#### Starting services:
1. Make sure that build.gradle file of each service is pointing towards  ``application-local-docker-jib.yml`` file instead of ``application.yml`` file

2. Build each service to generate the container image.
3. ``./gradlew :services:<service_name>:jibDockerBuild``
4. Once the container is successful build you will see the container image-id in the console output or you can fetch it using the command ``docker images``
5. Start the container using
 ``` docker run -p <service_port>:<service_port> -p 9464:9464 \
--network docker_my_network \
-v <base_path>/integrations/services/integrations-manager/src/main/resources/application-local-docker-jib.yml:/var/resources/application-local-docker-jib.yml \
illum.azurecr.io/<service_image> 
```
6. Make sure that the container is up and running by using ``docker ps`` command.
7. You should be able to see metrics getting exposed at ``localhost:9464/metrices``
8. Stop and remove any already running prometheus container using ``docker stop <container_id>`` and ``docker container prune``
9. Set the target in prometheus.yml to match the name of container
10. Restart the prometheus container
11. You should be able to access Prometheus UI on ``localhost:9090``.
12. Make sure the target health is showing as up and running and pointing towards the container.
13. Access Grafana dashboard at ```localhost:3000```
14. Add Prometheus as data source in Grafana
15. Create a new dashboard and add panels to visualize different metrics.

#### PromQl for visualizing different metrics in Grafana
Grafana is an observability frontend which can use any time series data source for creating visualization. For our use case we will be using Prometheus as data source. We would be mainly focusing on visualization of the following metrics:
1. **API latency metrics**
    * Average api latency over a period of x minutes : ``sum(rate(http_server_request_duration_seconds_count{http_route="<api_end_point>", http_request_method="<method>"}[60m]))``
2. ** Total number of success response from an API**
    * `` floor(increase(http_server_request_duration_seconds_count{http_route="<api_end_point>", http_request_method="POST", http_response_status_code="200"}[60m]))``
3. Total number of failure response from an API
    * ``floor(increase(http_server_request_duration_seconds_count{http_route="/api/v1/tenants/status", http_request_method="POST", http_response_status_code!="200"}[60m]))``
4. Kafka producer throughput metrics
    * 1. Record Send Rate: This measures how quickly producers are sending records to Kafka broker
         ``rate(kafka_producer_record_send_total{client_id=~".*"}[1m])``
    * 2. Request Rate: The average number of requests sent per second
         ``rate(kafka_producer_request_rate{client_id=~".*"}[1m])``
    * 3. Outgoing Byte Rate: Average number of bytes sent per second, which directly measures throughput: ``rate(kafka_producer_outgoing_byte_rate{client_id=~".*"}[1m])``

5. Kafka Consumer throughput metrics
    * 1. Bytes Consumed Rate: Measures how quickly consumers are processing data:
         ``rate(kafka_consumer_fetch_manager_bytes_consumed_rate{client_id=~".*"}[1m])``
    *  2. Records Consumed Rate: ``rate(kafka_consumer_records_consumed_rate{client_id=~".*"}[1m])``
    * 3. Records Fetch Rate:
         `` rate(kafka_consumer_fetch_rate{client_id=~".*"}[1m])``


