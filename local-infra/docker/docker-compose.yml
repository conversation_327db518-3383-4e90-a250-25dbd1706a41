services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      Z<PERSON><PERSON>EEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - 22181:2181
    healthcheck:
      test: [ "CMD", "nc", "-z", "localhost", "2181" ]
      interval: 10s
      timeout: 5s
      retries: 10
    networks:
      - my_network

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9093:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    restart: unless-stopped
    networks:
      - my_network


  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - 3000:3000
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - my_network

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - 29092:29092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    healthcheck:
      test: [ "CMD", "nc", "-z", "localhost", "9092" ]
      interval: 10s
      timeout: 5s
      retries: 10
    networks:
      - my_network

  kafka-ui:
    image: provectuslabs/kafka-ui
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:9092
    networks:
      - my_network

  postgres:
    image: postgres:alpine
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_DB: integrations
    logging:
      options:
        max-size: 10m
        max-file: "3"
    ports:
      - "5432:5432"
    networks:
      - my_network

  pgadmin:
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - my_network

  vault:
    image: "hashicorp/vault:latest"
    container_name: localsetup-vault
    ports:
      - "8200:8200"
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=myroot
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
      - VAULT_ADDR=http://127.0.0.1:8200
    command: [ "vault", "server", "-dev" ]
    cap_add:
      - IPC_LOCK
    networks:
      - my_network

networks:
  my_network:
    driver: bridge

configs:
  vault_config:
    content: |
      storage "file" {
        path = "/vault/data"
      }
      listener "tcp" {
        address     = "0.0.0.0:8200"
        tls_disable = 1
      }
      ui = true

volumes:
  postgres_data:
  vault-data:
  cosmosdb-data:
  eh-emulator:
  pgadmin-data:
  grafana_data:
