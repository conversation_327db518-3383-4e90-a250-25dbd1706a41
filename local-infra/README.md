### Steps to setup the project in local!

#### Requirements:
* Java 17 SDK

#### Setup docker services
1. ```cd local-infra/scripts```
2. ```bash start-containers.sh```

This will start the following services in your local machine
* postgres
* kafka
* vault
* pgadmin
* kafka-ui
* zookepper
  you can verify all service by executing ```docker ps```

**Note:** kafka container might exit in first attempt as zookeeper sometimes takes longer than expected to start. If you can't see kafka container up and running (docker ps) then re-run the script and now kafka should be up and running

* You can access kafka-ui on localhost:8080
* Make sure to copy and save the vault root token and vault unseal key once the services are up and running
* You will need root token to connect to the vault from your code base and also to login into the vault ui
* Unseal key will be required to unseal the vault if it gets sealed.
* You can find these in container logs by running the command ```docker logs <container_id>```.
* This is the sample of the message you need to look in the logs
      ```The unseal key and root token are displayed below in case you want to
      seal/unseal the Vault or re-authenticate.

    Unseal Key: KJa4i6KqYVi7zKklceYtWG2XpHdE2rMbLaqzlRg4hXg=
    Root Token: myroot```

* Once all services are up and running access **pgadmin** on **localhost:5050** using default credentials **(<EMAIL>, admin)** and connect to the database using these credentials (hostname: docker-postgres-1, port:5432, db_name: integrations, user: admin, password: admin)

#### Generating config files:
1.  ```cd local-infra/scripts```
2. ``` bash generate_configs.sh```

This will generate **application-local-docker.yml** file in resource directory of respective services

**Note**: As we are using cloud cosmos and LaunchDarkly. You will need to pass correct cosmos and LD key in the config for integration-manager and task-manager application-docker-local.yml file after the configs have been generated

#### Starting services:
1.  To start all the services make sure to execute these command from physical terminal of your machine outside intellij editor's terminal
    * ```cd local-infra/scripts```
    *  ```bash start-services.sh```

This will terminate any existing running services,  clean, build and bootrun all services in different terminals

Note: If you want to start individual services manually you can use the following command. Make sure you are in root integrations directory while executing these command
```
export SPRING_CONFIG_LOCATION=<your_base_directory_path>/integrations/services/<service_name>/src/main/resources/application-local-docker.yml

./gradlew :service:<service_name>:clean  
  

export SPRING_CONFIG_LOCATION=<your_base_directory_path>/integrations/services/<service_name>/src/main/resources/application-local-docker.yml

./gradlew :service:<service_name>:build

  

export SPRING_CONFIG_LOCATION=<your_base_directory_path>/integrations/services/<service_name>/src/main/resources/application-local-docker.yml

./gradlew :service:<service_name>:bootRun
```

**Note**: integrations-data sync by default always starts listening on heavy task queue. If you want to make it listen to light task queue, then make sure to change the config from HEAVY to LIGHT in the config

    integration-data-sync:
    	task-consumer-mode: HEAVY

#### Test the service and consume vulnerability
1. Onboard a tenant using the following curl


   ``` curl --location 'http://localhost:80/api/v1/tenants/onboard' \
--header 'Content-Type: application/json' \
--header 'x-cs-id: <x-cs-id>' \
--data '{
"tenantId": "aa21",
"integration": "WIZ",
"credentials": {
"secret": "<secret>",
"clientId": "<clent_id>"

},
 "configurations": {
    "apiUrl": "<api_url>",
    "authUrl": "<auth_url>"
  }
}

'

  
2. If onboarding is successful you should be able to see a successful task for the tenant in the task_status table and vulnerabilities added in the vulnerability and workload vulnerability table