logging:
  level:
    ROOT: DEBUG
spring:
  application:
    name: task-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations?sslmode=require
    username: citus
    password: DO_NOT_COMMIT
    properties:
      sslMode: require
  data:
    r2dbc:
      repositories:
        enabled: true
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: DO_NOT_COMMIT
        database: integrations
server:
  port: 8082

launch-darkly:
  sdk-key: DO_NOT_COMMIT

retry-config:
  min-backoff: 30s
  max-retries: 2

task-manager:
  kafka-consumer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true
    topic: test-task-status-message
    groupId: task-manager
    autoOffsetReset: earliest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  kafka-light-task-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: task_queue
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true

  kafka-heavy-task-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: test-integrations-heavy-tasks
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true

  long-running-task-config:
    purge-check-schedule-duration: 1d
    delta-long-running-window: 50m
    green-field-long-running-window: 2d
  schedule-tasks-config:
    delta-sync-schedule-duration: 1d
    async-task-readiness-check-delay: 5m