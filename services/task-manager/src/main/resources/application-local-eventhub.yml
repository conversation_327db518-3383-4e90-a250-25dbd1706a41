logging:
  level:
    ROOT: INFO
spring:
  application:
    name: task-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: DO_NOT_COMMIT
        database: integrations
  data:
    r2dbc:
      repositories:
        enabled: true

  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations
    username: citus
    password: DO_NOT_COMMIT
    properties:
      # refer to https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-PROTECTION for sslMode types
      sslMode: require # recommendation: "allow" if connecting to local postgres instance; "require" for Azure deployed postgres instances
  datasource:
    driver-class-name: org.postgresql.Driver
server:
  port: 8080

launch-darkly:
  sdk-key: DO_NOT_COMMIT

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

task-manager:
  kafka-consumer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
    isConnectionString: true
    topic: task-status-update-queue
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  kafka-light-task-producer-config:
    bootstrapServers: localhost:29092
    topic: light-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false


  kafka-heavy-task-producer-config:
    bootstrapServers: localhost:29092
    topic: heavy-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: false