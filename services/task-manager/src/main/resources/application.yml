logging:
  level:
    ROOT: INFO
spring:
  application:
    name: task-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

#  Postgres db config
  r2dbc:
    url: <url>
    username: <username>
    password: <password>
    properties:
      sslMode: require
  datasource:
    driver-class-name: org.postgresql.Driver

# Cosmos no SQL Database Config
  cloud:
    azure:
      cosmos:
        endpoint: https://integrationstest.documents.azure.com:443/
        key: DO_NOT_COMMIT
        database: integrations

launch-darkly:
  sdk-key: DO_NOT_COMMIT

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2


server:
  port: 8080


task-manager:
  kafka-consumer-config:
    bootstrapServers: <server>
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
    isConnectionString: true
    topic: test-status-event
    groupId: task-manager
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  kafka-light-task-producer-config:
    bootstrapServers: <server>
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
    isConnectionString: true
    topic: light-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer

  kafka-heavy-task-producer-config:
    bootstrapServers: <server>
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
    topic: heavy-task-queue
    keySerializer: org.apache.kafka.common.serialization.StringSerializer
    valueSerializer: org.springframework.kafka.support.serializer.JsonSerializer
    isConnectionString: true

  long-running-task-config:
    purge-check-schedule-duration: 5h
    delta-long-running-window: 50m
    green-field-long-running-window: 2d

  schedule-tasks-config:
    delta-sync-schedule-duration: 10m