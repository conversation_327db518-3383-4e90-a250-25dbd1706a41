package com.illumio.data.components.scripts;

import com.illumio.data.scripts.DatabaseInitializer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import static com.illumio.data.util.Constants.TASK_SCHEMA;

@Slf4j
@Component
@AllArgsConstructor
public class DbInitializer implements CommandLineRunner {
    public final DatabaseInitializer databaseInitializer;
    @Override
    public void run(String... args) {
        databaseInitializer.createSchema(TASK_SCHEMA);
    }
}


