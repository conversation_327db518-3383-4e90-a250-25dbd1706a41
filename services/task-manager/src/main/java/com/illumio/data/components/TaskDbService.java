package com.illumio.data.components;

import com.illumio.data.components.mapper.TaskStatusMapper;
import com.illumio.data.entities.RetryMessageEntity;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.TaskStatus;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import com.illumio.data.model.TenantOnboarding;
import com.illumio.data.repositories.RetryMessagesRepository;
import com.illumio.data.repositories.TaskStatusRepository;
import com.illumio.data.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskDbService {
    private final TaskStatusRepository taskStatusRepository;
    private final RetryMessagesRepository retryMessagesRepository;

    public Mono<TaskStatus> findLatestTask(TenantOnboarding onboarding) {
        return taskStatusRepository.findLatestByTenantIdAndIntegration(
                                           onboarding.getTenantId(), onboarding.getIntegration())
                                   .map(TaskStatusMapper::mapToTaskStatus)
                                   .doOnError(error ->
                                           log.error("Error while checking tasks for tenant: {} and integration: {}",
                                                   onboarding.getTenantId(),
                                                   onboarding.getIntegration(),
                                                   error));
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<TaskType> determineTaskType(TenantOnboarding onboarding) {
        return taskStatusRepository.findLatestByTenantIdAndIntegration(
                                           onboarding.getTenantId(),
                                           onboarding.getIntegration())
                                   .map(latestTask -> {
                                       if (TaskType.GREENFIELD.equals(latestTask.getTaskType()) &&
                                               TaskStatusType.FAIL.toString().equals(latestTask.getStatus().toString())) {
                                           log.debug("Last GREENFIELD task failed for tenant {} and integration {}. Scheduling another GREENFIELD task.",
                                                   onboarding.getTenantId(), onboarding.getIntegration());

                                           return TaskType.GREENFIELD;
                                       } else {
                                           return TaskType.DELTA;
                                       }
                                   });
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<TaskStatusEntity> findLastSuccessfulTask(TenantOnboarding onboarding) {
        return taskStatusRepository.findLastSuccessfulTask(
                                           onboarding.getTenantId(),
                                           onboarding.getIntegration())
                                   .doOnNext(task ->
                                           log.debug("Found last successful task in DB: {}", task))
                                   .switchIfEmpty(Mono.defer(() -> {
                                       log.debug("No successful task found in DB for tenant: {} and integration: {}",
                                               onboarding.getTenantId(),
                                               onboarding.getIntegration());
                                       return Mono.empty();
                                   }))
                                   .doOnError(error ->
                                           log.error("Error while fetching last successful task for tenant: {} and integration: {}",
                                                   onboarding.getTenantId(),
                                                   onboarding.getIntegration(),
                                                   error));
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<TaskStatus> updateTaskStatus(TaskStatus taskStatus) {
        TaskStatusEntity newTaskStatusEntity = TaskStatusMapper.mapToTaskStatusEntity(taskStatus);

        return taskStatusRepository.upsertTaskStatusEntity(newTaskStatusEntity)
                                   .map(taskStatusEntity -> taskStatus)
                                   .doOnSuccess(ts -> {
                                       log.debug("Successfully updated task status for tenantId: {}, integration: {}, status: {}",
                                               ts.getTenantId(), ts.getIntegration(), ts.getStatusType());

                                       saveRetryMessage(taskStatus)
                                               .subscribe();
                                   })
                                   .doOnError(error -> {
                                       log.error("Failed to update task status for tenantId: {}, integration: {}, status: {}",
                                               newTaskStatusEntity.getTenantId(), newTaskStatusEntity.getIntegration(), newTaskStatusEntity.getStatus(), error);
                                   });
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    private Mono<RetryMessageEntity> saveRetryMessage(TaskStatus taskStatus) {
        if (!taskStatus.getStatusType().equals(TaskStatusType.RETRY)) {
            return Mono.empty();
        }

        return retryMessagesRepository.upsertRetryMessages(RetryMessageEntity.builder()
                                                                             .taskId(taskStatus.getTaskId())
                                                                             .message(taskStatus.getStatusMessage())
                                                                             .endTime(DateTimeUtil.toOffsetDateTime(taskStatus.getStatusUpdateTime()).orElse(null))
                                                                             .build())
                                      .doOnSuccess(retryMessage -> {
                                          log.debug("Successfully saved retry message for tenantId: {}, integration: {}, taskId: {}",
                                                  taskStatus.getTenantId(), taskStatus.getIntegration(), taskStatus.getTaskId());
                                      })
                                      .doOnError(error -> {
                                          log.error("Failed to save retry message for tenantId: {}, integration: {}, taskId: {}",
                                                  taskStatus.getTenantId(), taskStatus.getIntegration(), taskStatus.getTaskId(), error);
                                      });
    }
}