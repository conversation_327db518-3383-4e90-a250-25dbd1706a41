package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Service
@Slf4j
public class TaskSenderService {
    private final KafkaSender<String, String> kafkaLightTaskSender;
    private final KafkaSender<String, String> kafkaHeavyTaskSender;
    private final TaskManagerConfiguration taskManagerConfiguration;
    private final ObjectMapper objectMapper;

    public TaskSenderService(@Qualifier("kafkaLightTaskSender") final KafkaSender<String, String> kafkaLightTaskSender,
                             @Qualifier("kafkaHeavyTaskSender") final KafkaSender<String, String> kafkaHeavyTaskSender,
                             final TaskManagerConfiguration taskManagerConfiguration,
                             final ObjectMapper objectMapper) {
        this.kafkaLightTaskSender = kafkaLightTaskSender;
        this.kafkaHeavyTaskSender = kafkaHeavyTaskSender;
        this.taskManagerConfiguration = taskManagerConfiguration;
        this.objectMapper = objectMapper;
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendTask(SyncTask task) {
        return Mono.just(createSenderRecord(task))
                .flatMapMany(senderRecord -> getSender(task).send(Mono.just(senderRecord)))
                .doOnComplete(() -> log.debug("Successfully pushed syncTask={} to task queue", task))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createSenderRecord(SyncTask task) {
        final String topic = getTopic(task);
        final String message = objectMapper.writeValueAsString(task);
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, message);
        return SenderRecord.create(producerRecord, null);
    }

    private KafkaSender<String, String> getSender(final SyncTask task) {
        return isHeavyTask(task)
                ? kafkaHeavyTaskSender
                : kafkaLightTaskSender;
    }

    private String getTopic(final SyncTask task) {
        return isHeavyTask(task)
                ? taskManagerConfiguration.getKafkaHeavyTaskProducerConfig().getTopic()
                : taskManagerConfiguration.getKafkaLightTaskProducerConfig().getTopic();
    }

    private boolean isHeavyTask(final SyncTask task) {
        return task.getTaskType().equals(TaskType.GREENFIELD)
                && task.getTaskStatus().equals(TaskStatusType.SCHEDULED);
    }

}
