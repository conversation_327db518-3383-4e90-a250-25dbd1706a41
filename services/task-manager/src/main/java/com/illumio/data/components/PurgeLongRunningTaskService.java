package com.illumio.data.components;

import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import com.illumio.data.repositories.TaskStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.OffsetDateTime;

import static com.illumio.data.util.TaskManagerConstants.PURGE_STATUS_MESSAGE;

@Slf4j
@Service
@RequiredArgsConstructor
public class PurgeLongRunningTaskService {

    private final TaskStatusRepository taskStatusRepository;
    private final TaskManagerConfiguration taskManagerConfiguration;

    @Scheduled(fixedRateString = "#{@taskManagerConfiguration.getLongRunningTaskConfig().getPurgeCheckScheduleDuration().toMillis()}")
    @LogReactiveExecutionTime
    public void checkAndPurgeLongRunningTasks() {
        Flux<TaskStatusEntity> deltaPurge =
                taskStatusRepository.findLongRunningTasks(TaskStatusType.SCHEDULED.name(),  TaskType.DELTA.name(),
                        taskManagerConfiguration.getLongRunningTaskConfig().getDeltaLongRunningWindow());

        Flux<TaskStatusEntity> greenfieldPurge =
                taskStatusRepository.findLongRunningTasks(TaskStatusType.STARTED.name(), TaskType.GREENFIELD.name(),
                        taskManagerConfiguration.getLongRunningTaskConfig().getGreenFieldLongRunningWindow());

        Flux<TaskStatusEntity> combinedFlux = Flux.merge(deltaPurge, greenfieldPurge);

        combinedFlux
                .flatMap(taskStatusEntity -> {
                    taskStatusEntity.setStatus(TaskStatusType.FAIL);
                    taskStatusEntity.setEndTime(OffsetDateTime.now());
                    taskStatusEntity.setStatusMessage(PURGE_STATUS_MESSAGE);
                    return taskStatusRepository.upsertTaskStatusEntity(taskStatusEntity);
                })
                .subscribe();
    }
}