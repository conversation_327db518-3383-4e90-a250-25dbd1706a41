package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.exception.BadRequestException;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.model.TaskStatus;
import com.illumio.data.util.CommonUtil;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskStatusReceiverService {

    private final KafkaReceiver<String, String> kafkaReceiver;
    private final TaskDbService taskDbService;
    private final TaskSchedulerService taskSchedulerService;
    private final ObjectMapper objectMapper;

    @EventListener(ApplicationReadyEvent.class)
    public void consumeTaskStatusMessage() {
        taskStatusMessageConsumer().subscribe();
    }

    @RetryReactiveOnError
    private Flux<Void> taskStatusMessageConsumer() {
        return kafkaReceiver.receive()
                            .doOnNext(taskStatusRecord -> log.debug("Received task status message: key={}, value={}, offset={}",
                                    taskStatusRecord.key(), taskStatusRecord.value(), taskStatusRecord.offset()))
                            .flatMap(this::saveTaskStatus)
                            .flatMap(taskSchedulerService::progressTask)
                            .onErrorResume(error -> {
                                log.error("Resuming consumer after failing to process task status update message...", error);
                                return Mono.empty();
                            });
    }


    private Mono<TaskStatus> saveTaskStatus(ReceiverRecord<String, String> taskStatusRecord) {
        TaskStatus taskStatusMessage = convertToTaskStatusMessage(taskStatusRecord);

        return taskDbService.updateTaskStatus(taskStatusMessage)
                                        .doFinally(signalType -> {
                                            // Acknowledge task message after retries has been exhausted
                                            CommonUtil.acknowledgeRecord(taskStatusRecord,signalType);
                                        });
    }

    private TaskStatus convertToTaskStatusMessage(ConsumerRecord<String, String> taskStatusRecord) {
        try {
            return objectMapper.readValue(taskStatusRecord.value(), TaskStatus.class);
        } catch (Exception e) {
            log.error("Error parsing task status message: {}", taskStatusRecord.value(), e);
            throw new BadRequestException(e.getMessage());
        }
    }
}