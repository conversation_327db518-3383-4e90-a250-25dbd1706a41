package com.illumio.data.components.mapper;

import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.model.TaskStatus;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.util.DateTimeUtil;
import lombok.experimental.UtilityClass;


import java.util.EnumMap;
import java.util.Map;
import java.util.function.BiConsumer;

@UtilityClass
public class TaskStatusMapper {

    private static final Map<TaskStatusType, BiConsumer<TaskStatus, TaskStatusEntity.TaskStatusEntityBuilder>> statusHandlers =
            new EnumMap<>(TaskStatusType.class);

    static {
        statusHandlers.put(TaskStatusType.SUCCESS, (msg, builder) ->
                DateTimeUtil.toOffsetDateTime(msg.getStatusUpdateTime())
                            .ifPresent(builder::endTime)
        );
        statusHandlers.put(TaskStatusType.RETRY, (msg, builder) ->
                DateTimeUtil.toOffsetDateTime(msg.getStatusUpdateTime())
                            .ifPresent(builder::endTime)
        );
        statusHandlers.put(TaskStatusType.FAIL, (msg, builder) ->
                DateTimeUtil.toOffsetDateTime(msg.getStatusUpdateTime())
                            .ifPresent(builder::endTime)
        );
        statusHandlers.put(TaskStatusType.STARTED, (msg, builder) ->
                DateTimeUtil.toOffsetDateTime(msg.getStatusUpdateTime())
                            .ifPresent(builder::startTime)
        );
        statusHandlers.put(TaskStatusType.SCHEDULED, (msg, builder) ->
                DateTimeUtil.toOffsetDateTime(msg.getStatusUpdateTime())
                            .ifPresent(builder::scheduleTime)
        );
    }

    public static TaskStatusEntity mapToTaskStatusEntity(TaskStatus taskStatus) {
        TaskStatusEntity.TaskStatusEntityBuilder builder = TaskStatusEntity.builder()
                .taskId(taskStatus.getTaskId())
                .integration(taskStatus.getIntegration())
                .tenantId(taskStatus.getTenantId())
                .status(taskStatus.getStatusType())
                .statusMessage(taskStatus.getStatusMessage())
                .taskType(taskStatus.getTaskType());

        BiConsumer<TaskStatus, TaskStatusEntity.TaskStatusEntityBuilder> handler = statusHandlers.get(taskStatus.getStatusType());
        if (handler != null) {
            handler.accept(taskStatus, builder);
        }

        return builder.build();
    }
}

