package com.illumio.data.configuration;


import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final TaskManagerConfiguration taskManagerConfiguration;

    @Bean
    @Qualifier("kafkaLightTaskSender")
    public KafkaSender<String, String> kafkaLightTaskSender() {
        return kafkaSender(taskManagerConfiguration.getKafkaLightTaskProducerConfig());
    }

    @Bean
    @Qualifier("kafkaHeavyTaskSender")
    public KafkaSender<String, String> kafkaHeavyTaskSender() {
        return kafkaSender(taskManagerConfiguration.getKafkaHeavyTaskProducerConfig());
    }

    private KafkaSender<String, String> kafkaSender(TaskManagerConfiguration.KafkaProducerConfig kafkaProducerConfig) {
        Map<String, Object> producerProps = producerOptions(kafkaProducerConfig);

        SenderOptions<String, String> senderOptions = SenderOptions.<String, String>create(producerProps)
                                                                   .maxInFlight(1024);
        return KafkaSender.create(senderOptions);
    }

    private Map<String, Object> producerOptions(TaskManagerConfiguration.KafkaProducerConfig kafkaProducerConfig) {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                kafkaProducerConfig.getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (Optional.of(kafkaProducerConfig)
                    .map(TaskManagerConfiguration.KafkaProducerConfig::getIsConnectionString)
                    .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    kafkaProducerConfig.getSaslJaasConfig());
        }
        return producerProps;
    }
}
