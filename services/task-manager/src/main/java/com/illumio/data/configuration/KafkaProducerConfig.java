package com.illumio.data.configuration;


import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final TaskManagerConfiguration taskManagerConfiguration;

    @Bean
    public SenderOptions<String, String> kafkaSenderOptions() {
        Map<String, Object> producerProps = producerOptions();

        return SenderOptions.<String, String>create(producerProps)
                // Non-blocking back-pressure
                .maxInFlight(1024);
    }

    @Bean
    public KafkaSender<String, String> reactiveKafkaSender(
            SenderOptions<String, String> senderOptions) {
        return KafkaSender.create(senderOptions);
    }

    //    @Bean
    public KafkaProducer<String, String> kafkaProducer() {
        Map<String, Object> producerProps = producerOptions();
        return new KafkaProducer<>(producerProps);
    }

    private Map<String, Object> producerOptions() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                taskManagerConfiguration.getKafkaLightTaskProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (Optional.of(taskManagerConfiguration)
                .map(TaskManagerConfiguration::getKafkaLightTaskProducerConfig)
                .map(TaskManagerConfiguration.KafkaProducerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    taskManagerConfiguration.getKafkaLightTaskProducerConfig().getSaslJaasConfig());
        }
        return producerProps;
    }
}
