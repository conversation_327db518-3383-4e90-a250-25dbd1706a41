package com.illumio.data.util;

import com.illumio.data.model.TaskStatusType;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

@UtilityClass
public class TaskManagerConstants {

    public static final String PURGE_STATUS_MESSAGE = "Data sync task took too long. Restarting sync...";

    public static final List<String> COMPLETED_TASK_STATUSES = Arrays.asList(
            TaskStatusType.SUCCESS.name(),
            TaskStatusType.FAIL.name()
    );
}
