package com.illumio.data.util;

import com.illumio.data.model.TaskStatusType;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

@UtilityClass
public class TaskManagerConstants {
    public static final List<String> ACTIVE_TASK_STATUSES = Arrays.asList(
            TaskStatusType.SCHEDULED.name(),
            TaskStatusType.STARTED.name(),
            TaskStatusType.RETRY.name()
    );

    public static final List<String> COMPLETED_TASK_STATUSES = Arrays.asList(
            TaskStatusType.SUCCESS.name(),
            TaskStatusType.FAIL.name()
    );
}
