package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "task-manager")
@Getter
@Setter
public class TaskManagerConfiguration {

    private final KafkaConsumerConfig kafkaConsumerConfig = new KafkaConsumerConfig();
    private final RetryConfig retryConfig = new RetryConfig();
    private final KafkaProducerConfig kafkaLightTaskProducerConfig = new KafkaProducerConfig();
    private final KafkaProducerConfig kafkaHeavyTaskProducerConfig = new KafkaProducerConfig();
    private final LongRunningTaskConfig longRunningTaskConfig = new LongRunningTaskConfig();
    private final ScheduleTasksConfig scheduleTasksConfig = new ScheduleTasksConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs = 30000;
        private Integer maxPollRecords = 500;
        private Integer maxPartitionFetchBytes;
        private Integer receiverMaxAttempts = 10;
        private Duration receiverBackoff = Duration.ofSeconds(5);
        private Integer backPressureEvents;
        private Integer prefetchCount;
    }


    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
    }


    @Getter
    @Setter
    public static class RetryConfig {
        private Duration tryTimeoutSeconds = Duration.ofSeconds(10);
        private Duration delaySeconds = Duration.ofSeconds(10);
        private Integer maxRetries = 50;
    }

    @Getter
    @Setter
    public static class LongRunningTaskConfig {
        private Duration deltaLongRunningWindow = Duration.ofMinutes(50);
        private Duration greenFieldLongRunningWindow = Duration.ofDays(2);
        private Duration purgeCheckScheduleDuration = Duration.ofDays(1);
    }

    @Getter
    @Setter
    public static class ScheduleTasksConfig{
        private Duration deltaSyncScheduleDuration = Duration.ofMinutes(10);
    }

}
