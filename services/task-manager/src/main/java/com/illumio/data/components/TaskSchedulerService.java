package com.illumio.data.components;

import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import com.illumio.data.repositories.OnboardingRepository;
import com.illumio.data.util.CommonUtil;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Instant;


@Slf4j
@Service
@RequiredArgsConstructor
public class TaskSchedulerService {
    private final OnboardingRepository onboardingRepository;
    private final TaskDbService taskDbService;
    private final TaskMessagePublisherService taskMessagePublisherService;
    private final LDService ldService;
    private final TaskManagerConfiguration taskManagerConfiguration;

    @Scheduled(fixedRateString = "#{@taskManagerConfiguration.getScheduleTasksConfig().getDeltaSyncScheduleDuration().toMillis()}")
    public Mono<Void> scheduleTask() {
        log.info("Sync Task triggered at: {}", Instant.now());
        return onboardingRepository.findAll()
                                   .flatMap(onboarding -> checkAndCreateTask(onboarding)
                                           .onErrorResume(e -> {
                                               log.error("Error creating task for tenantId and integration: {}, {}", onboarding.getTenantId(), onboarding.getIntegration(), e);
                                               return Mono.empty();
                                           }))
                                   .then();
    }


    private Mono<Void> checkAndCreateTask(TenantOnboardingEntity onboarding) {
        return taskDbService.canScheduleSyncTask(onboarding)
                        .zipWith(ldService.integrationIsEnabledForTenant(onboarding.getTenantId(), onboarding.getIntegration()),
                                (noConflictingTasks, integrationEnabledForTenant)
                                        -> noConflictingTasks && integrationEnabledForTenant)
                        .flatMap(canCreateTask -> {
                            if (canCreateTask) {
                                log.debug("Creating new task for tenant {} and integration {}.",
                                        onboarding.getTenantId(),
                                        onboarding.getIntegration());
                                return createAndSaveTask(onboarding);
                            } else {
                                log.debug("Waiting for sync task to complete for tenant {} and integration {}.",
                                        onboarding.getTenantId(),
                                        onboarding.getIntegration());
                                return Mono.empty();
                            }
                        })
                        .then();
    }

    private Mono<Void> createAndSaveTask(TenantOnboardingEntity onboarding) {
        return taskDbService.determineTaskType(onboarding)
                            .flatMap(taskType -> createNewTaskStatus(onboarding, taskType))
                            .flatMap(unsavedStatus ->
                                    createSyncTask(unsavedStatus, onboarding)
                                            .flatMap(task -> taskMessagePublisherService.sendTask(task, onboarding))
                                            .thenReturn(unsavedStatus)
                            )
                            .flatMap(taskDbService::updateTaskStatus)
                            .then();
    }


    private Mono<TaskStatus> createNewTaskStatus(TenantOnboardingEntity onboarding, TaskType taskType) {
        final String scheduledTime = DateTimeUtil.generateCurrentTimestamp();
        final Integration integration = onboarding.getIntegration();
        final String tenantId = onboarding.getTenantId();
        return Mono.just(TaskStatus.builder()
                                   .taskId(Util.generateNewTaskId(tenantId, integration, scheduledTime))
                                   .integration(integration)
                                   .tenantId(tenantId)
                                   .statusUpdateTime(scheduledTime)
                                   .statusType(TaskStatusType.SCHEDULED)
                                   .taskType(taskType)
                                   .build());
    }

    private Mono<SyncTask> createSyncTask(TaskStatus newTaskStatus, TenantOnboardingEntity onboarding) {
        return taskDbService.fetchLastSuccessfulTask(onboarding)
                .map(lastSuccessfulTask -> buildTask(newTaskStatus, onboarding, lastSuccessfulTask))
                .defaultIfEmpty(buildTask(newTaskStatus, onboarding, null));
    }

    private SyncTask buildTask(TaskStatus taskStatus,
                               TenantOnboardingEntity onboarding,
                               TaskStatusEntity lastSuccessfulTask) {
        return SyncTask.builder()
                .taskId(taskStatus.getTaskId())
                .integration(onboarding.getIntegration())
                .tenantId(onboarding.getTenantId())
                .scheduleTime(taskStatus.getStatusUpdateTime())
                .credentialsVaultPath(onboarding.getCredentialsVaultPath())
                .priorSuccessfulSync(buildTaskMetadata(lastSuccessfulTask))
                .tenantConfigurations(onboarding.getConfigurations())
                .taskType(taskStatus.getTaskType())
                .build();
    }

    private TaskBasicMetadata buildTaskMetadata(TaskStatusEntity lastSuccessfulTask) {
        if (lastSuccessfulTask == null) {
            return null;
        }
        return TaskBasicMetadata.builder()
                .startTime(DateTimeUtil.toString(lastSuccessfulTask.getStartTime()).orElse(null))
                .endTime(DateTimeUtil.toString(lastSuccessfulTask.getEndTime()).orElse(null))
                .endMessage(lastSuccessfulTask.getStatusMessage())
                .build();
    }
}