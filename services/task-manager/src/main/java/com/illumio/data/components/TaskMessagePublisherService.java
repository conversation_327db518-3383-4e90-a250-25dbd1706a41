package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskMessagePublisherService {
    private final KafkaSender<String, String> kafkaSender;
    private final ObjectMapper objectMapper;
    private final TaskManagerConfiguration taskManagerConfiguration;

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendTask(SyncTask task, TenantOnboardingEntity onboarding) {
        return Mono.just(createTask(task))
                .flatMapMany(senderRecord -> kafkaSender.send(Mono.just(senderRecord)))
                .doOnComplete(() ->
                        log.debug("Successfully pushed Task to queue for tenant: {}",
                                onboarding.getTenantId()))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createTask(SyncTask task) {
        final String topic = TaskType.GREENFIELD.equals(task.getTaskType())
                ? taskManagerConfiguration.getKafkaHeavyTaskProducerConfig().getTopic()
                : taskManagerConfiguration.getKafkaLightTaskProducerConfig().getTopic();

        final String message = objectMapper.writeValueAsString(task);
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, message);
        return SenderRecord.create(producerRecord, null);
    }

}
