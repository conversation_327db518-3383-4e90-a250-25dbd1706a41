package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.Integration;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskMessagePublisherServiceTest {

    @Mock
    private KafkaSender<String, String> kafkaSender;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private TaskManagerConfiguration taskManagerConfiguration;
    @Mock private TaskManagerConfiguration.KafkaProducerConfig kafkaHeavyTaskProducerConfig;
    @Mock private TaskManagerConfiguration.KafkaProducerConfig kafkaLightTaskProducerConfig;


    @InjectMocks
    private TaskMessagePublisherService taskMessagePublisherService;

    @Captor
    private ArgumentCaptor<Mono<SenderRecord<String, String, String>>> senderRecordCaptor;

    private static final String HEAVY_TOPIC = "heavy-topic";
    private static final String LIGHT_TOPIC = "light-topic";
    private static final String TASK_JSON = "{\"taskId\":\"123\"}";

    @BeforeEach
    void setUp() {
        when(kafkaSender.send(any())).thenReturn(Flux.empty());
    }

    @Test
    void sendTask_shouldSendGreenfieldTaskToHeavyQueue() throws JsonProcessingException {
        SyncTask task = SyncTask.builder()
                                .taskId("123")
                                .tenantId("tenant-1")
                                .taskType(TaskType.GREENFIELD)
                                .build();

        when(kafkaHeavyTaskProducerConfig.getTopic()).thenReturn(HEAVY_TOPIC);
        when(taskManagerConfiguration.getKafkaHeavyTaskProducerConfig()).thenReturn(kafkaHeavyTaskProducerConfig);

        TenantOnboardingEntity onboarding = new TenantOnboardingEntity(
                "tenant-1",
                Integration.WIZ,
                null,
                null,
                null
        );

        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);

        StepVerifier.create(taskMessagePublisherService.sendTask(task, onboarding))
                    .verifyComplete();


        verify(kafkaSender).send(senderRecordCaptor.capture());
        verify(taskManagerConfiguration).getKafkaHeavyTaskProducerConfig();
        verify(kafkaHeavyTaskProducerConfig).getTopic();
        verify(objectMapper).writeValueAsString(task);
        Mono<SenderRecord<String, String, String>> capturedMono = senderRecordCaptor.getValue();
        SenderRecord<String, String, String> senderRecord = capturedMono.block();
        assertEquals(HEAVY_TOPIC, senderRecord.topic());
        assertEquals(TASK_JSON, senderRecord.value());

    }

    @Test
    void sendTask_shouldSendDeltaTaskToLightQueue() throws JsonProcessingException {

        SyncTask task = SyncTask.builder()
                                .taskId("456")
                                .tenantId("tenant-2")
                                .taskType(TaskType.DELTA)
                                .build();

        when(kafkaLightTaskProducerConfig.getTopic()).thenReturn(LIGHT_TOPIC);
        when(taskManagerConfiguration.getKafkaLightTaskProducerConfig()).thenReturn(kafkaLightTaskProducerConfig);

        TenantOnboardingEntity onboarding = new TenantOnboardingEntity(
                "tenant-2",
                Integration.WIZ,
                null,
                null,
                null
        );

        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);


        StepVerifier.create(taskMessagePublisherService.sendTask(task, onboarding))
                    .verifyComplete();


        verify(kafkaSender).send(senderRecordCaptor.capture());
        verify(taskManagerConfiguration).getKafkaLightTaskProducerConfig();
        verify(kafkaLightTaskProducerConfig).getTopic();
        verify(objectMapper).writeValueAsString(task);


        Mono<SenderRecord<String, String, String>> capturedMono = senderRecordCaptor.getValue();
        SenderRecord<String, String, String> senderRecord = capturedMono.block();
        assertEquals(LIGHT_TOPIC, senderRecord.topic());
        assertEquals(TASK_JSON, senderRecord.value());

    }


    @Test
    void sendTask_shouldHandleKafkaSendFailure() throws JsonProcessingException {
        SyncTask task = SyncTask.builder()
                                .taskId("101")
                                .tenantId("tenant-4")
                                .taskType(TaskType.GREENFIELD)
                                .build();

        when(kafkaHeavyTaskProducerConfig.getTopic()).thenReturn(HEAVY_TOPIC);
        when(taskManagerConfiguration.getKafkaHeavyTaskProducerConfig()).thenReturn(kafkaHeavyTaskProducerConfig);

        TenantOnboardingEntity onboarding = new TenantOnboardingEntity(
                "tenant-4",
                Integration.WIZ,
                null,
                null,
                null
        );

        RuntimeException kafkaException = new RuntimeException("Kafka send error");
        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);
        when(kafkaSender.send(any())).thenReturn(Flux.error(kafkaException));

        StepVerifier.create(taskMessagePublisherService.sendTask(task, onboarding))
                    .expectErrorMatches(throwable -> throwable == kafkaException)
                    .verify();

        verify(kafkaSender).send(any());
        verify(objectMapper).writeValueAsString(task);
    }
}


