package com.illumio.data.components;

import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import com.illumio.data.repositories.TaskStatusRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import static org.assertj.core.api.Assertions.assertThat;

import java.time.Duration;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PurgeLongRunningTaskServiceTest {

  @Mock
  private TaskStatusRepository taskStatusRepository;

  @Mock private TaskManagerConfiguration taskManagerConfiguration;

  @InjectMocks
  private PurgeLongRunningTaskService purgeLongRunningTaskService;

  @Captor
  private ArgumentCaptor<TaskStatusEntity> taskStatusEntityCaptor;

  @BeforeEach
  void setUp() {
    TaskManagerConfiguration.LongRunningTaskConfig longRunningTaskConfig =
        mock(TaskManagerConfiguration.LongRunningTaskConfig.class);
    when(taskManagerConfiguration.getLongRunningTaskConfig()).thenReturn(longRunningTaskConfig);
    when(longRunningTaskConfig.getDeltaLongRunningWindow())
        .thenReturn(Duration.ofMinutes(30));
    when(longRunningTaskConfig.getGreenFieldLongRunningWindow())
        .thenReturn(Duration.ofDays(1));
  }

  @Test
  void testCheckAndPurgeLongRunningTasks() {
    TaskStatusEntity deltaTask = new TaskStatusEntity();
    deltaTask.setStatus(TaskStatusType.SCHEDULED);
    deltaTask.setTaskType(TaskType.DELTA);

    TaskStatusEntity greenfieldTask = new TaskStatusEntity();
    greenfieldTask.setStatus(TaskStatusType.STARTED);
    greenfieldTask.setTaskType(TaskType.GREENFIELD);

    when(taskStatusRepository.findLongRunningTasks(eq(TaskStatusType.SCHEDULED.name()), eq(TaskType.DELTA.name()), any()))
            .thenReturn(Flux.just(deltaTask));
    when(taskStatusRepository.findLongRunningTasks(eq(TaskStatusType.STARTED.name()), eq(TaskType.GREENFIELD.name()), any()))
            .thenReturn(Flux.just(greenfieldTask));
    when(taskStatusRepository.upsertTaskStatusEntity(any())).thenReturn(Mono.empty());

    purgeLongRunningTaskService.checkAndPurgeLongRunningTasks();

    verify(taskStatusRepository, times(1)).findLongRunningTasks(eq(TaskStatusType.SCHEDULED.name()), eq(TaskType.DELTA.name()), any());
    verify(taskStatusRepository, times(1)).findLongRunningTasks(eq(TaskStatusType.STARTED.name()), eq(TaskType.GREENFIELD.name()), any());
    verify(taskStatusRepository, times(2)).upsertTaskStatusEntity(taskStatusEntityCaptor.capture());

    List<TaskStatusEntity> capturedEntities = taskStatusEntityCaptor.getAllValues();
    assertThat(capturedEntities).hasSize(2);

    TaskStatusEntity capturedDeltaTask = capturedEntities.get(0);
    assertThat(capturedDeltaTask.getStatus()).isEqualTo(TaskStatusType.FAIL);
    assertThat(capturedDeltaTask.getEndTime()).isNotNull();
    assertThat(capturedDeltaTask.getStatusMessage()).isEqualTo("Long running task purged");

    TaskStatusEntity capturedGreenfieldTask = capturedEntities.get(1);
    assertThat(capturedGreenfieldTask.getStatus()).isEqualTo(TaskStatusType.FAIL);
    assertThat(capturedGreenfieldTask.getEndTime()).isNotNull();
    assertThat(capturedGreenfieldTask.getStatusMessage()).isEqualTo("Long running task purged");
  }
}
