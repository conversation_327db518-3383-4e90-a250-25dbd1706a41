package com.illumio.data.components;

import com.illumio.data.configuration.TaskManagerConfiguration;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import com.illumio.data.repositories.OnboardingRepository;
import com.illumio.data.util.DateTimeUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskSchedulerServiceTest {

    @Mock private OnboardingRepository onboardingRepository;
    @Mock private TaskDbService taskDbService;
    @Mock private TaskMessagePublisherService taskMessagePublisherService;
    @Mock private LDService ldService;
    @Mock private TaskManagerConfiguration taskManagerConfiguration;

    private TaskSchedulerService taskSchedulerService;

    private static final TenantOnboardingEntity ONBOARDING = new TenantOnboardingEntity(
            "tenant1",
            Integration.WIZ,
            "/secret/data/integrations-arch/integration1/tenant1/credentials",
            null,
            null
    );

    @BeforeEach
    void setUp() {
        taskSchedulerService = new TaskSchedulerService(onboardingRepository, taskDbService, taskMessagePublisherService, ldService, taskManagerConfiguration);
    }

    @Test
    void scheduleTask_shouldProcessAllOnboardings() {
        TenantOnboardingEntity onboarding2 = new TenantOnboardingEntity(
                "tenant2",
                Integration.ARMIS,
                "path2",
                null,
                null
        );

        TenantOnboardingEntity onboarding3 = new TenantOnboardingEntity(
                "tenant3",
                Integration.ARMIS,
                "path3",
                null,
                null
        );


        when(onboardingRepository.findAll()).thenReturn(Flux.just(ONBOARDING, onboarding2, onboarding3));
        when(taskDbService.canScheduleSyncTask(eq(ONBOARDING))).thenReturn(Mono.just(true));
        when(taskDbService.canScheduleSyncTask(eq(onboarding2))).thenReturn(Mono.just(true));
        when(taskDbService.canScheduleSyncTask(eq(onboarding3))).thenReturn(Mono.just(false));

        when(ldService.integrationIsEnabledForTenant(eq(ONBOARDING.getTenantId()), eq(ONBOARDING.getIntegration())))
                .thenReturn(Mono.just(true));
        when(ldService.integrationIsEnabledForTenant(eq(onboarding2.getTenantId()), eq(onboarding2.getIntegration())))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(eq(onboarding3.getTenantId()), eq(onboarding3.getIntegration())))
                .thenReturn(Mono.just(false));

        when(taskDbService.updateTaskStatus(any(TaskStatus.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        when(taskDbService.fetchLastSuccessfulTask(eq(ONBOARDING))).thenReturn(Mono.empty());
        when(taskMessagePublisherService.sendTask(any(SyncTask.class), eq(ONBOARDING))).thenReturn(Mono.empty());
        when(taskDbService.determineTaskType(eq(ONBOARDING))).thenReturn(Mono.just(TaskType.DELTA));

        StepVerifier.create(taskSchedulerService.scheduleTask())
                    .verifyComplete();

        verify(onboardingRepository).findAll();
        verify(taskDbService).canScheduleSyncTask(eq(ONBOARDING));
        verify(taskDbService).canScheduleSyncTask(eq(onboarding2));
        verify(taskDbService).fetchLastSuccessfulTask(eq(ONBOARDING));
        verify(ldService).integrationIsEnabledForTenant(eq(ONBOARDING.getTenantId()), eq(ONBOARDING.getIntegration()));
        verify(ldService).integrationIsEnabledForTenant(eq(onboarding2.getTenantId()), eq(onboarding2.getIntegration()));
        verify(ldService).integrationIsEnabledForTenant(eq(onboarding3.getTenantId()), eq(onboarding3.getIntegration()));
        verify(taskDbService).determineTaskType(eq(ONBOARDING));
        verify(taskDbService).updateTaskStatus(argThat(
                taskStatus -> taskStatus.getTenantId().equals(ONBOARDING.getTenantId())
                        && taskStatus.getIntegration().equals(ONBOARDING.getIntegration())
                        && taskStatus.getStatusType().equals(TaskStatusType.SCHEDULED)
                        && taskStatus.getTaskType().equals(TaskType.DELTA)
        ));

        verify(taskMessagePublisherService, times(1)).sendTask(argThat(
                syncTask -> syncTask.getTenantId().equals(ONBOARDING.getTenantId())
                        && syncTask.getIntegration().equals(ONBOARDING.getIntegration())
                        && syncTask.getCredentialsVaultPath().equals(ONBOARDING.getCredentialsVaultPath())
                        && syncTask.getTenantConfigurations() == ONBOARDING.getConfigurations()
        ), eq(ONBOARDING));
    }

    @Test
    void scheduleTask_shouldUseLastSuccessfulSyncTaskWhenAvailable() {
        TaskStatusEntity.TaskStatusEntityBuilder builder = TaskStatusEntity.builder()
                                                                           .taskId("task0")
                                                                           .integration(Integration.WIZ)
                                                                           .tenantId("tenant1")
                                                                           .statusMessage("Completed successfully")
                                                                           .status(TaskStatusType.SUCCESS)
                                                                           .taskType(TaskType.GREENFIELD);

        DateTimeUtil.toOffsetDateTime("2025-03-09T15:25:00Z").ifPresent(builder::scheduleTime);
        DateTimeUtil.toOffsetDateTime("2025-03-09T15:26:00Z").ifPresent(builder::startTime);
        DateTimeUtil.toOffsetDateTime("2025-03-09T15:30:00Z").ifPresent(builder::endTime);

        TaskStatusEntity lastSuccessfulTask = builder.build();

        when(onboardingRepository.findAll()).thenReturn(Flux.just(ONBOARDING));
        when(taskDbService.canScheduleSyncTask(eq(ONBOARDING))).thenReturn(Mono.just(true));

        when(ldService.integrationIsEnabledForTenant(eq(ONBOARDING.getTenantId()), eq(ONBOARDING.getIntegration())))
                .thenReturn(Mono.just(true));

        when(taskDbService.updateTaskStatus(any(TaskStatus.class)))
                .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        when(taskDbService.fetchLastSuccessfulTask(eq(ONBOARDING))).thenReturn(Mono.just(lastSuccessfulTask));
        when(taskMessagePublisherService.sendTask(any(SyncTask.class), eq(ONBOARDING))).thenReturn(Mono.empty());
        when(taskDbService.determineTaskType(eq(ONBOARDING))).thenReturn(Mono.just(TaskType.DELTA));

        StepVerifier.create(taskSchedulerService.scheduleTask())
                    .verifyComplete();

        verify(ldService).integrationIsEnabledForTenant(eq(ONBOARDING.getTenantId()), eq(ONBOARDING.getIntegration()));

        verify(taskDbService).fetchLastSuccessfulTask(any());
        verify(taskMessagePublisherService).sendTask(argThat(
                syncTask -> syncTask.getTenantId().equals(ONBOARDING.getTenantId())
                        && syncTask.getIntegration().equals(ONBOARDING.getIntegration())
                        && syncTask.getCredentialsVaultPath().equals(ONBOARDING.getCredentialsVaultPath())
                        && syncTask.getTenantConfigurations() == ONBOARDING.getConfigurations()
                        && syncTask.getPriorSuccessfulSync()
                                   .equals(TaskBasicMetadata.builder()
                                                            .startTime(DateTimeUtil.toString(lastSuccessfulTask.getStartTime()).orElse(null))
                                                            .endTime(DateTimeUtil.toString(lastSuccessfulTask.getEndTime()).orElse(null))
                                                            .endMessage(lastSuccessfulTask.getStatusMessage())
                                                            .build())
        ), eq(ONBOARDING));
    }

}