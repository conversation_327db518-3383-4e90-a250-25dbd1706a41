package com.illumio.data.components;

import com.illumio.data.components.mapper.TaskStatusMapper;
import com.illumio.data.entities.RetryMessageEntity;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.model.Integration;
import com.illumio.data.model.TaskStatus;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import com.illumio.data.repositories.RetryMessagesRepository;
import com.illumio.data.repositories.TaskStatusRepository;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static com.illumio.data.util.DateTimeUtil.toOffsetDateTime;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskDbOperationsTest {
    @Mock
    private TaskStatusRepository taskStatusRepository;
    @Mock
    private RetryMessagesRepository retryMessagesRepository;
    private TaskDbService taskDbService;

    private TaskStatus taskStatus;
    private TaskStatusEntity taskStatusEntity;

    @BeforeEach
    void setUp() {
        taskDbService = new TaskDbService(taskStatusRepository, retryMessagesRepository);

        taskStatus = TaskStatus.builder()
                               .taskId("1")
                               .tenantId("test-tenant")
                               .integration(Integration.WIZ)
                               .statusType(TaskStatusType.SUCCESS)
                               .taskType(TaskType.DELTA)
                               .statusUpdateTime(DateTimeUtil.generateCurrentTimestamp())
                               .statusMessage("")
                               .build();
        taskStatusEntity = TaskStatusMapper.mapToTaskStatusEntity(taskStatus);
    }

    @Test
    void updateTaskStatus_noRetryUpdates_succeeds() {
        when(taskStatusRepository.upsertTaskStatusEntity(eq(taskStatusEntity)))
                .thenReturn(Mono.just(taskStatusEntity));

        StepVerifier.create(taskDbService.updateTaskStatus(taskStatus))
                    .expectNext(taskStatus)
                    .verifyComplete();

        verify(taskStatusRepository, times(1))
                .upsertTaskStatusEntity(eq(taskStatusEntity));
        verifyNoInteractions(retryMessagesRepository);
    }

    @Test
    void updateTaskStatus_withRetryUpdates_succeeds() {
        taskStatus.setStatusType(TaskStatusType.RETRY);
        taskStatusEntity.setStatus(TaskStatusType.RETRY);
        final RetryMessageEntity expectedRetryMessageEntity = RetryMessageEntity.builder()
                                                                                .taskId(taskStatus.getTaskId())
                                                                                .message(taskStatus.getStatusMessage())
                                                                                .endTime(toOffsetDateTime(taskStatus.getStatusUpdateTime()).orElse(null))
                                                                                .build();

        when(taskStatusRepository.upsertTaskStatusEntity(eq(taskStatusEntity)))
                .thenReturn(Mono.just(taskStatusEntity));

        when(retryMessagesRepository.upsertRetryMessages(eq(expectedRetryMessageEntity)))
                .thenReturn(Mono.just(expectedRetryMessageEntity));

        StepVerifier.create(taskDbService.updateTaskStatus(taskStatus))
                    .expectNext(taskStatus)
                    .verifyComplete();

        verify(taskStatusRepository, times(1))
                .upsertTaskStatusEntity(eq(taskStatusEntity));
        verify(retryMessagesRepository, times(1))
                .upsertRetryMessages(eq(expectedRetryMessageEntity));
    }

}
