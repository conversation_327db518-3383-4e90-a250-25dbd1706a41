# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    kafka: DEBUG

ports:
- name: admin
  port: 8084
- name: rest
  port: 8080

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8080

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/integrations
  repositoryName: task-manager
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

spring:
  cloud:
    azure:
      cosmos:
        endpoint: "https://integrationstest.documents.azure.com:443/"
        key:    # should give at deployment time
        database: "integrations"
  data:
    r2dbc:
      repositories:
        enabled: true
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations
    username: citus
    password:    # should give at deployment time
    properties:
      sslMode: require # refer to https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-PROTECTION for possible values
  datasource:
    driverClassName: org.postgresql.Driver

launchDarkly:
  sdkKey:    # should give at deployment time

retryConfig:
  userOperations:
    minBackoff: 2s
    maxRetries: 2
  systemOperations:
    minBackoff: 30s
    maxRetries: 2

server:
  port: 8080
ansi:
  enabled: ALWAYS
taskManager:
  kafkaConsumerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "task-status-update-queue"
    isConnectionString: true
    connectionString:    # should give at deployment time
  kafkaLightTaskProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "light-task-queue"
    isConnectionString: true
    connectionString: # should give at deployment time
  kafkaHeavyTaskProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "heavy-task-queue"
    isConnectionString: true
    connectionString: # should give at deployment time
  longRunningTaskConfig:
    purgeCheckScheduleDuration: 1d
    deltaLongRunningWindow: 50m
    greenFieldLongRunningWindow: 1d
  scheduleTasksConfig:
    deltaSyncScheduleDuration: 10m
    asyncTaskReadinessCheckDelay: 5m

extraLabels: {}
