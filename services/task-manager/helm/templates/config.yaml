apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "TaskManager.fullname" . }}-env-configmap
  labels:
    {{- include "TaskManager.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "task-manager"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
      cloud:
        azure:
          cosmos:
            endpoint: "{{.Values.spring.cloud.azure.cosmos.endpoint}}"
            database: "{{.Values.spring.cloud.azure.cosmos.database}}"
      data:
        r2dbc:
          repositories:
            enabled: {{.Values.spring.data.r2dbc.repositories.enabled}}
      r2dbc:
        url: "{{.Values.spring.r2dbc.url}}"
        username: "{{.Values.spring.r2dbc.username}}"
        properties:
          sslMode: "{{.Values.spring.r2dbc.properties.sslMode}}"
      datasource:
        driver-class-name: "{{.Values.spring.datasource.driverClassName}}"
    server:
      port: {{.Values.server.port}}
    retry-config:
      user-operations:
        min-backoff: "{{.Values.retryConfig.userOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.userOperations.maxRetries}}
      system-operations:
        min-backoff: "{{.Values.retryConfig.systemOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.systemOperations.maxRetries}}
    
    task-manager:
      kafka-consumer-config:
        bootstrapServers: "{{.Values.taskManager.kafkaConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.taskManager.kafkaConsumerConfig.topic}}"
        groupId: "{{.Values.taskManager.kafkaConsumerConfig.groupId}}"
        isConnectionString: {{.Values.taskManager.kafkaConsumerConfig.isConnectionString}}
        autoOffsetReset: "{{.Values.taskManager.kafkaConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.taskManager.kafkaConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.taskManager.kafkaConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.taskManager.kafkaConsumerConfig.maxPartitionFetchBytes}}"
      kafka-light-task-producer-config:
        bootstrapServers: "{{.Values.taskManager.kafkaLightTaskProducerConfig.bootstrapServers}}"
        topic: "{{.Values.taskManager.kafkaLightTaskProducerConfig.topic}}"
        isConnectionString: {{.Values.taskManager.kafkaLightTaskProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.taskManager.kafkaLightTaskProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.taskManager.kafkaLightTaskProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.taskManager.kafkaLightTaskProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.taskManager.kafkaLightTaskProducerConfig.lingerMs}}"
        batchSize: "{{.Values.taskManager.kafkaLightTaskProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.taskManager.kafkaLightTaskProducerConfig.bufferMemory}}"
      kafka-heavy-task-producer-config:
        bootstrapServers: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.bootstrapServers}}"
        topic: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.topic}}"
        isConnectionString: {{.Values.taskManager.kafkaHeavyTaskProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.lingerMs}}"
        batchSize: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.taskManager.kafkaHeavyTaskProducerConfig.bufferMemory}}"

      long-running-task-config:
        purge-check-schedule-duration: {{.Values.taskManager.longRunningTaskConfig.purgeCheckScheduleDuration}}
        delta-long-running-window: {{.Values.taskManager.longRunningTaskConfig.deltaLongRunningWindow}}
        green-field-long-running-window: {{.Values.taskManager.longRunningTaskConfig.greenFieldLongRunningWindow}}

      schedule-tasks-config:
        delta-sync-schedule-duration: {{.Values.taskManager.scheduleTasksConfig.deltaSyncScheduleDuration}}
        async-task-readiness-check-delay: {{.Values.taskManager.scheduleTasksConfig.asyncTaskReadinessCheckDelay}}