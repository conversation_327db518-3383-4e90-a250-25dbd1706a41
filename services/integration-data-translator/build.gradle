plugins {
    id 'java-library'
}

dependencies {

    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    implementation project(":commons:utility-commons")

    // jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.mockito:mockito-junit-jupiter'
    testImplementation 'org.assertj:assertj-core'
    testImplementation 'org.junit.platform:junit-platform-suite'

    // Spring test support
    testImplementation 'org.springframework:spring-test'
    testImplementation 'org.springframework.boot:spring-boot-test'
}

// Test configuration
test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
}