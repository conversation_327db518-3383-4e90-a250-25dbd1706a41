plugins {
    id 'java-library'
}

dependencies {

    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    implementation project(":commons:utility-commons")

    // jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}