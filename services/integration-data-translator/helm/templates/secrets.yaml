apiVersion: v1
kind: Secret
metadata:
  name: {{ include "IntegrationDataTranslator.fullname" . }}-env-secrets
  labels:
    {{- include "IntegrationDataTranslator.labels" . | nindent 4 }}
type: Opaque
stringData:
  INTEGRATIONDATATRANSLATOR_KAFKACONSUMERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.integrationDataTranslator.kafkaConsumerConfig.connectionString }}";
  INTEGRATIONDATATRANSLATOR_KAFKAPRODUCERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.integrationDataTranslator.kafkaProducerConfig.connectionString }}";