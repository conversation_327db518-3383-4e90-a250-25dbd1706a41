apiVersion: v1
kind: Service
metadata:
  name: {{ include "IntegrationDataTranslator.name" . }}
  labels:
    {{- include "IntegrationDataTranslator.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "IntegrationDataTranslator.selectorLabels" . | nindent 4 }}
