apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "IntegrationDataTranslator.fullname" . }}-env-configmap
  labels:
    {{- include "IntegrationDataTranslator.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    spring:
      application:
        name: "integration-data-translator"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    server:
      port: {{.Values.server.port}}
    retry-config:
      user-operations:
        min-backoff: "{{.Values.retryConfig.userOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.userOperations.maxRetries}}
      system-operations:
        min-backoff: "{{.Values.retryConfig.systemOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.systemOperations.maxRetries}}
    
    integration-data-translator:
        
      kafka-consumer-config:
        bootstrapServers: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.topic}}"
        groupId: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.groupId}}"
        isConnectionString: {{.Values.integrationDataTranslator.kafkaConsumerConfig.isConnectionString}}
        autoOffsetReset: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.maxPartitionFetchBytes}}"
    
      kafka-producer-config:
        bootstrapServers: "{{.Values.integrationDataTranslator.kafkaProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataTranslator.kafkaProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationDataTranslator.kafkaProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.lingerMs}}"
        batchSize: "{{.Values.integrationDataTranslator.kafkaProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.integrationDataTranslator.kafkaProducerConfig.bufferMemory}}"
