apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "IntegrationDataTranslator.fullname" . }}-env-configmap
  labels:
    {{- include "IntegrationDataTranslator.labels" . | nindent 4 }}
data:
  boundary-data.json: |
    {
      "name": "boundary-mapping",
      "description": "Mapping rules for boundary data",
      "fieldMappings": {
        "boundaryId": "data.boundaries[*].id",
        "name": "data.boundaries[*].name",
        "affectedSites": "data.boundaries[*].affectedSites"
      }
    }
  cef-data.json: |
    {
    "name": "cef-mapping",
    "description": "Mapping rules for CEF data",
    "fieldMappings": {
    }
    }
  device-data.json: |
    {
    "name": "device-mapping",
    "description": "Mapping rules for device data",
    "fieldMappings": {
    "deviceId": "data.results[*].id",
    "displayTitle": "data.results[*].displayTitle",
    "name": "data.results[*].name",
    "names": "data.results[*].names",
    "manufacturer": "data.results[*].manufacturer",
    "type": "data.results[*].type",
    "model": "data.results[*].model",
    "firstSeen": "data.results[*].firstSeen",
    "lastSeen": "data.results[*].lastSeen",
    "macAddress": "data.results[*].macAddress",
    "ipv4": "data.results[*].ipAddress",
    "ipv6": "data.results[*].ipv6[@collect]",
    "accessSwitch": "data.results[*].accessSwitch",
    "tags": "data.results[*].tags[@collect]",
    "purdueLevel": "data.results[*].purdueLevel",
    "riskScore": "data.results[*].riskLevel",
    "operatingSystem": "data.results[*].operatingSystem",
    "protections": "data.results[*].protections[@object]",
    "sensor": "data.results[*].sensor",
    "tier": "data.results[*].tier",
    "typeEnum": "data.results[*].typeEnum",
    "visibility": "data.results[*].visibility",
    "siteId": "data.results[*].site.id",
    "categoryId": "data.results[*].category.id",
    "boundaryId": "data.results[*].boundaries.id",
    "dataSources": "data.results[*].dataSources[@object]"
    }
    }
  issue-data.json: |
    {
    "name": "issue-mapping",
    "description": "Mapping rules for issue data",
    "fieldMappings": {
    "name": "data.issues.nodes[*].sourceRules[0].name",
    "description": "data.issues.nodes[*].sourceRules[0].controlDescription",
    "subscriptionId": "data.issues.nodes[*].entitySnapshot.subscriptionExternalId",
    "severity": "data.issues.nodes[*].severity",
    "status": "data.issues.nodes[*].status",
    "risks": "data.issues.nodes[*].sourceRules[0].risks[@collect]"
    }
    }
  site-data.json: |
    {
    "name": "site-mapping",
    "description": "Mapping rules for site data",
    "fieldMappings": {
    "siteId": "data.sites[*].id",
    "name": "data.sites[*].name",
    "location": "data.sites[*].location",
    "lat": "data.sites[*].lat",
    "lng": "data.sites[*].lng",
    "parentId": "data.sites[*].parentId",
    "networkEquipmentDeviceIds": "data.sites[*].networkEquipmentDeviceIds[@collect]"
    }
    }
  vulnerability-data.json: |
    {
    "name": "vulnerability-mapping",
    "description": "Mapping rules for vulnerability data",
    "fieldMappings": {
    "cveId": "data.vulnerabilityFindings.nodes[*].name",
    "description": "data.vulnerabilityFindings.nodes[*].CVEDescription",
    "severity": "data.vulnerabilityFindings.nodes[*].severity",
    "cvssScore": "data.vulnerabilityFindings.nodes[*].score",
    "hasExploit": "data.vulnerabilityFindings.nodes[*].hasExploit",
    "hasCisaKevExploit": "data.vulnerabilityFindings.nodes[*].hasCisaKevExploit",
    "status": "data.vulnerabilityFindings.nodes[*].status",
    "firstDetectedAt": "data.vulnerabilityFindings.nodes[*].firstDetectedAt",
    "lastDetectedAt": "data.vulnerabilityFindings.nodes[*].lastDetectedAt",
    "workloadId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.providerUniqueId",
    "workloadName": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.name",
    "cloudPlatform": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.cloudPlatform",
    "region": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.region",
    "subscriptionId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.subscriptionExternalId",
    "operatingSystem": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.operatingSystem",
    "remediation": "data.vulnerabilityFindings.nodes[*].remediation",
    "detectionMethod": "data.vulnerabilityFindings.nodes[*].detectionMethod",
    "packageName": "data.vulnerabilityFindings.nodes[*].detailedName",
    "packageVersion": "data.vulnerabilityFindings.nodes[*].version",
    "fixedVersion": "data.vulnerabilityFindings.nodes[*].fixedVersion"
    }
    }
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "integration-data-translator"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    server:
      port: {{.Values.server.port}}
      namespace: "{{.Values.vault.namespace}}"
    retry-config:
      user-operations:
        min-backoff: "{{.Values.retryConfig.userOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.userOperations.maxRetries}}
      system-operations:
        min-backoff: "{{.Values.retryConfig.systemOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.systemOperations.maxRetries}}
    
    integration-data-translator:
        
      kafka-consumer-config:
        bootstrapServers: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.topic}}"
        groupId: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.groupId}}"
        isConnectionString: {{.Values.integrationDataSync.kafkaConsumerConfig.isConnectionString}}
        autoOffsetReset: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.integrationDataTranslator.kafkaConsumerConfig.maxPartitionFetchBytes}}"
    
      kafka-producer-config:
        bootstrapServers: "{{.Values.integrationDataTranslator.kafkaProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataTranslator.kafkaProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationDataTranslator.kafkaProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.integrationDataTranslator.kafkaProducerConfig.lingerMs}}"
        batchSize: "{{.Values.integrationDataTranslator.kafkaProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.integrationDataTranslator.kafkaProducerConfig.bufferMemory}}"
