{"name": "VulnerabilityDataMapping", "description": "Mapping configuration for vulnerability meta data", "fieldMappings": {"cveId": "data.vulnerabilityFindings.nodes[*].name", "description": "data.vulnerabilityFindings.nodes[*].CVEDescription", "severity": "data.vulnerabilityFindings.nodes[*].severity", "cvssScore": "data.vulnerabilityFindings.nodes[*].score", "hasExploit": "data.vulnerabilityFindings.nodes[*].hasExploit", "hasCisaKevExploit": "data.vulnerabilityFindings.nodes[*].hasCisaKevExploit", "status": "data.vulnerabilityFindings.nodes[*].status", "firstDetectedAt": "data.vulnerabilityFindings.nodes[*].firstDetectedAt", "lastDetectedAt": "data.vulnerabilityFindings.nodes[*].lastDetectedAt", "workloadId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.providerUniqueId", "workloadName": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.name", "cloudPlatform": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.cloudPlatform", "region": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.region", "subscriptionId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.subscriptionExternalId", "operatingSystem": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.operatingSystem", "remediation": "data.vulnerabilityFindings.nodes[*].remediation", "detectionMethod": "data.vulnerabilityFindings.nodes[*].detectionMethod", "packageName": "data.vulnerabilityFindings.nodes[*].detailedName", "packageVersion": "data.vulnerabilityFindings.nodes[*].version", "fixedVersion": "data.vulnerabilityFindings.nodes[*].fixedVersion"}}