{"data": {"boundaries": [{"affectedSites": "", "id": 1, "name": "ICS", "ruleAql": null}, {"affectedSites": "", "id": 2, "name": "Corporate", "ruleAql": null}, {"affectedSites": "", "id": 3, "name": "ICS_2", "ruleAql": null}, {"affectedSites": "", "id": 4, "name": "Guest", "ruleAql": null}, {"affectedSites": "", "id": 5, "name": "Diagnostic Imaging", "ruleAql": null}, {"affectedSites": "", "id": 6, "name": "Nursing Units", "ruleAql": null}, {"affectedSites": "", "id": 7, "name": "Medical", "ruleAql": null}, {"affectedSites": "", "id": 8, "name": "Automation", "ruleAql": null}, {"affectedSites": "", "id": 9, "name": "BMS", "ruleAql": null}, {"affectedSites": "", "id": 10, "name": "BYOD", "ruleAql": null}], "count": 10, "next": 10, "prev": 0}, "success": true}