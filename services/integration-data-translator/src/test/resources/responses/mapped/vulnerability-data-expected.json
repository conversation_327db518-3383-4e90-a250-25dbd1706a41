[{"cveId": "CVE-2022-1304", "description": "An out-of-bounds read/write vulnerability was found in e2fsprogs. This issue leads to a segmentation fault and possibly arbitrary code execution via a specially crafted file system.", "severity": "MEDIUM", "cvssScore": 7.8, "hasExploit": false, "hasCisaKevExploit": false, "status": "OPEN", "firstDetectedAt": "2023-01-15T10:30:00Z", "lastDetectedAt": "2023-12-01T14:22:00Z", "workloadId": "i-0123456789abcdef0", "workloadName": "saki-RH8-ven-lab02", "cloudPlatform": "AWS", "region": "us-east-1", "subscriptionId": "123456789012", "operatingSystem": "Red Hat Enterprise Linux 8", "remediation": "Update e2fsprogs to version 1.46.5-3.el8 or later", "detectionMethod": "PACKAGE_MANAGER", "packageName": "e2fsprogs", "packageVersion": "1.45.6-5.el8", "fixedVersion": "1.46.5-3.el8"}, {"cveId": "CVE-2021-3445", "description": "A flaw was found in libdnf's signature verification functionality in versions before 0.60.1. This flaw allows an attacker to bypass signature verification.", "severity": "HIGH", "cvssScore": 8.1, "hasExploit": true, "hasCisaKevExploit": false, "status": "OPEN", "firstDetectedAt": "2023-02-10T08:15:00Z", "lastDetectedAt": "2023-12-01T14:22:00Z", "workloadId": "i-0123456789abcdef0", "workloadName": "saki-RH8-ven-lab02", "cloudPlatform": "AWS", "region": "us-east-1", "subscriptionId": "123456789012", "operatingSystem": "Red Hat Enterprise Linux 8", "remediation": "Update libdnf to version 0.60.1 or later", "detectionMethod": "PACKAGE_MANAGER", "packageName": "libdnf", "packageVersion": "0.55.0-7.el8", "fixedVersion": "0.60.1"}, {"cveId": "CVE-2022-32189", "description": "A flaw was found in the golang crypto/tls package. This issue may cause a panic due to an improper tls handshake.", "severity": "LOW", "cvssScore": 3.1, "hasExploit": false, "hasCisaKevExploit": false, "status": "RESOLVED", "firstDetectedAt": "2023-03-05T12:45:00Z", "lastDetectedAt": "2023-11-15T09:30:00Z", "workloadId": "i-0123456789abcdef0", "workloadName": "saki-RH8-ven-lab02", "cloudPlatform": "AWS", "region": "us-east-1", "subscriptionId": "123456789012", "operatingSystem": "Red Hat Enterprise Linux 8", "remediation": "Update golang to version 1.18.6 or later", "detectionMethod": "PACKAGE_MANAGER", "packageName": "golang", "packageVersion": "1.17.12-1.el8", "fixedVersion": "1.18.6"}]