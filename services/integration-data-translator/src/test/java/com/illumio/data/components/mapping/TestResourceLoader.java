package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.model.MappingConfiguration;
import lombok.experimental.UtilityClass;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

/**
 * Utility class for loading test resources including mapping configurations and JSON responses.
 */
@UtilityClass
public class TestResourceLoader {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String MAPPING_PATH = "mapping/";
    private static final String RESPONSES_PATH = "responses/api/";
    private static final String MAPPED_RESPONSES_PATH = "responses/mapped/";

    /**
     * Loads a mapping configuration from the test resources/mapping folder.
     * Note: This method is deprecated. Use manual builder pattern instead due to Lombok @Builder limitations.
     *
     * @param fileName the name of the mapping file (e.g., "vulnerability-data.json")
     * @return the parsed MappingConfiguration
     * @throws IOException if the file cannot be read or parsed
     */
    @Deprecated
    public static MappingConfiguration loadMappingConfiguration(String fileName) throws IOException {
        // This method is kept for backward compatibility but may not work with @Builder classes
        // Use manual builder pattern instead
        throw new UnsupportedOperationException("Use manual builder pattern to create MappingConfiguration objects");
    }

    /**
     * Loads a JSON response from the test resources/responses/api folder as a JsonNode.
     *
     * @param fileName the name of the response file (e.g., "wiz_vulnerability_response.json")
     * @return the parsed JsonNode
     * @throws IOException if the file cannot be read or parsed
     */
    public static JsonNode loadJsonResponse(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(RESPONSES_PATH + fileName);
        return OBJECT_MAPPER.readTree(resource.getInputStream());
    }

    /**
     * Loads a JSON response from the test resources/responses/api folder as a String.
     *
     * @param fileName the name of the response file (e.g., "wiz_vulnerability_response.json")
     * @return the JSON content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadJsonResponseAsString(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(RESPONSES_PATH + fileName);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Loads any JSON file from test resources as a JsonNode.
     *
     * @param relativePath the relative path from test/resources (e.g., "responses/mapped/expected_output.json")
     * @return the parsed JsonNode
     * @throws IOException if the file cannot be read or parsed
     */
    public static JsonNode loadJsonFromPath(String relativePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(relativePath);
        return OBJECT_MAPPER.readTree(resource.getInputStream());
    }

    /**
     * Loads any file from test resources as a String.
     *
     * @param relativePath the relative path from test/resources
     * @return the file content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadStringFromPath(String relativePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(relativePath);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Creates a simple JsonNode from a JSON string for testing purposes.
     *
     * @param jsonString the JSON string to parse
     * @return the parsed JsonNode
     * @throws IOException if the JSON cannot be parsed
     */
    public static JsonNode createJsonNode(String jsonString) throws IOException {
        return OBJECT_MAPPER.readTree(jsonString);
    }

    /**
     * Creates a MappingConfiguration without wildcard paths for testing.
     * Note: This method is deprecated. Use manual builder pattern instead.
     *
     * @return a MappingConfiguration with simple field mappings
     */
    @Deprecated
    public static MappingConfiguration createMappingConfigurationWithoutWildcards() {
        // Use builder pattern instead of JSON deserialization
        return MappingConfiguration.builder()
                .name("SimpleMapping")
                .description("Simple mapping without wildcards")
                .fieldMappings(Map.of(
                    "id", "data.id",
                    "name", "data.name",
                    "status", "data.status"
                ))
                .build();
    }

    /**
     * Creates a null JsonNode for testing null scenarios.
     *
     * @return a null JsonNode
     */
    public static JsonNode createNullJsonNode() {
        return OBJECT_MAPPER.nullNode();
    }

    /**
     * Loads expected mapped JSON from the test resources/responses/mapped folder.
     *
     * @param fileName the name of the expected mapped file (e.g., "vulnerability-data-expected.json")
     * @return the JSON content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadExpectedMappedResult(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(MAPPED_RESPONSES_PATH + fileName);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Loads expected mapped JSON from the test resources/responses/mapped folder as JsonNode.
     *
     * @param fileName the name of the expected mapped file (e.g., "vulnerability-data-expected.json")
     * @return the parsed JsonNode
     * @throws IOException if the file cannot be read or parsed
     */
    public static JsonNode loadExpectedMappedResultAsJsonNode(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(MAPPED_RESPONSES_PATH + fileName);
        return OBJECT_MAPPER.readTree(resource.getInputStream());
    }

    /**
     * Loads a mapping configuration JSON file from test resources as a raw string.
     * This is useful for testing the MappingConfigurationLoader.
     *
     * @param fileName the name of the mapping file (e.g., "vulnerability-data.json")
     * @return the JSON content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadMappingConfigurationAsString(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(MAPPING_PATH + fileName);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Gets all mapping configuration file names from the test resources/mapping folder.
     *
     * @return array of mapping file names
     */
    public static String[] getAllMappingFileNames() {
        return new String[]{
            "vulnerability-data.json",
            "device-data.json",
            "issue-data.json",
            "site-data.json",
            "boundary-data.json"
        };
    }

    /**
     * Gets the expected mapped result file name for a given mapping file.
     *
     * @param mappingFileName the mapping file name (e.g., "vulnerability-data.json")
     * @return the expected result file name (e.g., "vulnerability-data-expected.json")
     */
    public static String getExpectedMappedFileName(String mappingFileName) {
        return mappingFileName.replace(".json", "-expected.json");
    }

    /**
     * Gets the data type from a mapping file name.
     *
     * @param mappingFileName the mapping file name (e.g., "vulnerability-data.json")
     * @return the data type (e.g., "vulnerability-data")
     */
    public static String getDataTypeFromFileName(String mappingFileName) {
        return mappingFileName.replace(".json", "");
    }
}
