package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.model.MappingConfiguration;
import lombok.experimental.UtilityClass;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Utility class for loading test resources including mapping configurations and JSON responses.
 */
@UtilityClass
public class TestResourceLoader {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final String MAPPING_PATH = "mapping/";
    private static final String RESPONSES_PATH = "responses/api/";

    /**
     * Loads a mapping configuration from the test resources/mapping folder.
     *
     * @param fileName the name of the mapping file (e.g., "vulnerability_mapping_rules.json")
     * @return the parsed MappingConfiguration
     * @throws IOException if the file cannot be read or parsed
     */
    public static MappingConfiguration loadMappingConfiguration(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(MAPPING_PATH + fileName);
        return OBJECT_MAPPER.readValue(resource.getInputStream(), MappingConfiguration.class);
    }

    /**
     * Loads a JSON response from the test resources/responses/api folder as a JsonNode.
     *
     * @param fileName the name of the response file (e.g., "wiz_vulnerability_response.json")
     * @return the parsed JsonNode
     * @throws IOException if the file cannot be read or parsed
     */
    public static JsonNode loadJsonResponse(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(RESPONSES_PATH + fileName);
        return OBJECT_MAPPER.readTree(resource.getInputStream());
    }

    /**
     * Loads a JSON response from the test resources/responses/api folder as a String.
     *
     * @param fileName the name of the response file (e.g., "wiz_vulnerability_response.json")
     * @return the JSON content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadJsonResponseAsString(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource(RESPONSES_PATH + fileName);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Loads any JSON file from test resources as a JsonNode.
     *
     * @param relativePath the relative path from test/resources (e.g., "responses/mapped/expected_output.json")
     * @return the parsed JsonNode
     * @throws IOException if the file cannot be read or parsed
     */
    public static JsonNode loadJsonFromPath(String relativePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(relativePath);
        return OBJECT_MAPPER.readTree(resource.getInputStream());
    }

    /**
     * Loads any file from test resources as a String.
     *
     * @param relativePath the relative path from test/resources
     * @return the file content as a String
     * @throws IOException if the file cannot be read
     */
    public static String loadStringFromPath(String relativePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(relativePath);
        return Files.readString(Paths.get(resource.getURI()), StandardCharsets.UTF_8);
    }

    /**
     * Creates a simple JsonNode from a JSON string for testing purposes.
     *
     * @param jsonString the JSON string to parse
     * @return the parsed JsonNode
     * @throws IOException if the JSON cannot be parsed
     */
    public static JsonNode createJsonNode(String jsonString) throws IOException {
        return OBJECT_MAPPER.readTree(jsonString);
    }

    /**
     * Creates a MappingConfiguration without wildcard paths for testing.
     *
     * @return a MappingConfiguration with simple field mappings
     * @throws IOException if the configuration cannot be created
     */
    public static MappingConfiguration createMappingConfigurationWithoutWildcards() throws IOException {
        String configJson = """
            {
                "name": "SimpleMapping",
                "description": "Simple mapping without wildcards",
                "fieldMappings": {
                    "id": "data.id",
                    "name": "data.name",
                    "status": "data.status"
                }
            }
            """;
        return OBJECT_MAPPER.readValue(configJson, MappingConfiguration.class);
    }

    /**
     * Creates a null JsonNode for testing null scenarios.
     *
     * @return a null JsonNode
     */
    public static JsonNode createNullJsonNode() {
        return OBJECT_MAPPER.nullNode();
    }
}
