# Mapping Components Test Suite

This directory contains comprehensive JUnit test cases for all classes in the `components.mapping` package of the integration-data-translator service.

## Test Structure

### Test Classes Created

1. **TestResourceLoader** - Utility class for loading test resources
2. **JsonNodeNavigatorTest** - Tests JSON navigation functionality
3. **JsonPathResolverTest** - Tests path resolution and wildcard handling
4. **ArrayIndexFinderTest** - Tests array index discovery
5. **JsonObjectMapperTest** - Tests object mapping functionality
6. **ValueExtractorTest** - Tests value extraction coordination
7. **JsonValueConverterTest** - Tests JSON to Java object conversion
8. **SingleValueExtractionStrategyTest** - Tests single value extraction strategy
9. **MappingComponentsTestSuite** - Test suite runner for all mapping tests

### Test Data Sources

#### Mapping Configurations
Located in: `src/test/resources/mapping/`
- `vulnerability_mapping_rules.json`
- `device_mapping_rules.json`
- `issue_mapping_rules.json`
- `site_mapping_rules.json`
- `boundary_mapping_rules.json`

#### JSON Response Data
Located in: `src/test/resources/responses/api/`
- `wiz_vulnerability_response.json`
- `wiz_issues_response.json`
- `armis_inventory_devices_response.json`
- `armis_inventory_sites_response.json`
- `armis_inventory_boundaries_response.json`

## Running the Tests

### Run All Mapping Tests
```bash
# Using Gradle
./gradlew test --tests "com.illumio.data.components.mapping.*"

# Using Maven
mvn test -Dtest="com.illumio.data.components.mapping.*"

# Run the test suite specifically
./gradlew test --tests "com.illumio.data.components.mapping.MappingComponentsTestSuite"
```

### Run Individual Test Classes
```bash
# Example: Run only JsonNodeNavigator tests
./gradlew test --tests "com.illumio.data.components.mapping.JsonNodeNavigatorTest"

# Example: Run only JsonPathResolver tests
./gradlew test --tests "com.illumio.data.components.mapping.JsonPathResolverTest"
```

### Run Specific Test Methods
```bash
# Example: Run specific test method
./gradlew test --tests "com.illumio.data.components.mapping.JsonNodeNavigatorTest.navigateToNode_shouldReturnCorrectNode_whenPathExists"
```

## Test Coverage

### JsonNodeNavigatorTest
- ✅ Navigation with dot notation paths
- ✅ Array index navigation
- ✅ Nested object navigation
- ✅ Error handling for invalid paths
- ✅ Null and empty input handling

### JsonPathResolverTest
- ✅ Wildcard replacement with array indices
- ✅ Multiple wildcard handling
- ✅ Marker removal functionality
- ✅ Path extraction methods
- ✅ Edge cases and error scenarios

### ArrayIndexFinderTest
- ✅ Array index discovery from JSON
- ✅ Empty array handling
- ✅ Non-array node handling
- ✅ Large array handling
- ✅ Error scenarios

### JsonObjectMapperTest
- ✅ Object mapping with field configurations
- ✅ Null value handling
- ✅ Complex data type mapping
- ✅ Field order preservation
- ✅ Error handling

### ValueExtractorTest
- ✅ Strategy selection and execution
- ✅ Multiple strategy coordination
- ✅ Error handling and fallback
- ✅ Complex return type handling
- ✅ Edge cases

### JsonValueConverterTest
- ✅ All JSON type conversions (string, number, boolean, array, object)
- ✅ Nested structure handling
- ✅ Real-world data conversion
- ✅ Error scenarios
- ✅ Type preservation

### SingleValueExtractionStrategyTest
- ✅ Strategy support detection
- ✅ Value extraction for different types
- ✅ Path resolution integration
- ✅ Error propagation
- ✅ Edge cases

## Key Features

### Config-Driven Testing
- Tests load actual mapping configurations from `resources/mapping/`
- Uses real JSON response data from `resources/responses/api/`
- Demonstrates the config-driven approach in action

### Reactive Testing
- All tests use `StepVerifier` for reactive stream testing
- Proper handling of `Mono` and `Flux` return types
- Error scenario testing with reactive streams

### Comprehensive Coverage
- Tests cover happy path, edge cases, and error scenarios
- Mock-based testing for unit isolation
- Integration-style tests with real data

### Resource Management
- `TestResourceLoader` utility for consistent resource loading
- Helper methods for creating test data
- Reusable test utilities across all test classes

## Dependencies

The tests require the following dependencies (should be in your test classpath):
- JUnit 5
- Mockito
- Reactor Test (for StepVerifier)
- AssertJ (for fluent assertions)
- Jackson (for JSON processing)

## Best Practices Demonstrated

1. **Separation of Concerns**: Each test class focuses on a single component
2. **Resource Loading**: Centralized resource loading through utility class
3. **Mock Usage**: Proper mocking for unit test isolation
4. **Reactive Testing**: Correct use of StepVerifier for reactive code
5. **Test Data**: Use of real-world test data for realistic scenarios
6. **Error Testing**: Comprehensive error scenario coverage
7. **Documentation**: Well-documented test methods and classes

## Adding New Tests

When adding new test cases:

1. Follow the existing naming convention: `methodName_shouldExpectedBehavior_whenCondition`
2. Use `TestResourceLoader` for loading test resources
3. Use `StepVerifier` for testing reactive streams
4. Include both positive and negative test cases
5. Add appropriate documentation and comments
6. Update this README if adding new test classes or significant functionality
