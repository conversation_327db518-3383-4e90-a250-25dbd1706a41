package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.model.MappingConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Map;
import java.util.TreeSet;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ArrayIndexFinderTest {

    @Mock
    private JsonPathResolver pathResolver;

    @Mock
    private JsonNodeNavigator navigator;

    @InjectMocks
    private ArrayIndexFinder arrayIndexFinder;

    private JsonNode testJsonNode;
    private MappingConfiguration mappingConfiguration;

    @BeforeEach
    void setUp() throws IOException {
        // Load test data
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");

        // Create mapping configuration manually using builder
        mappingConfiguration = MappingConfiguration.builder()
                .name("VulnerabilityDataMapping")
                .description("Mapping configuration for vulnerability meta data")
                .fieldMappings(Map.of(
                    "cveId", "data.vulnerabilityFindings.nodes[*].name",
                    "description", "data.vulnerabilityFindings.nodes[*].CVEDescription",
                    "severity", "data.vulnerabilityFindings.nodes[*].severity"
                ))
                .build();
    }

    @Test
    void findArrayIndices_shouldReturnCorrectIndices_whenArrayExists() throws IOException {
        // Given
        String arrayPath = "data.vulnerabilityFindings.nodes";
        JsonNode arrayNode = testJsonNode.get("data").get("vulnerabilityFindings").get("nodes");

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn(arrayPath);
        when(navigator.navigateToNode(any(JsonNode.class), anyString())).thenReturn(Mono.just(arrayNode));

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).hasSize(3); // Based on the test JSON file
                    assertThat(indices).containsExactly(0, 1, 2);
                })
                .verifyComplete();
    }

    @Test
    void findArrayIndices_shouldReturnEmptySet_whenArrayDoesNotExist() {
        // Given
        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn("data.nonExistentArray");
        when(navigator.navigateToNode(any(JsonNode.class), anyString())).thenReturn(Mono.empty());

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void findArrayIndices_shouldReturnEmptySet_whenNodeIsNotArray() throws IOException {
        // Given
        JsonNode nonArrayNode = TestResourceLoader.createJsonNode("""
            {
                "data": {
                    "singleItem": {
                        "name": "test"
                    }
                }
            }
            """);

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn("data.singleItem");
        when(navigator.navigateToNode(any(JsonNode.class), anyString()))
                .thenReturn(Mono.just(nonArrayNode.get("data").get("singleItem")));

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void findArrayIndices_shouldHandleEmptyArray() throws IOException {
        // Given
        JsonNode emptyArrayNode = TestResourceLoader.createJsonNode("""
            {
                "data": {
                    "emptyArray": []
                }
            }
            """);

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn("data.emptyArray");
        when(navigator.navigateToNode(any(JsonNode.class), anyString()))
                .thenReturn(Mono.just(emptyArrayNode.get("data").get("emptyArray")));

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void findArrayIndices_shouldHandleLargeArray() throws IOException {
        // Given
        JsonNode largeArrayNode = TestResourceLoader.createJsonNode("""
            {
                "data": {
                    "items": [
                        {"id": 1}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5},
                        {"id": 6}, {"id": 7}, {"id": 8}, {"id": 9}, {"id": 10}
                    ]
                }
            }
            """);

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn("data.items");
        when(navigator.navigateToNode(any(JsonNode.class), anyString()))
                .thenReturn(Mono.just(largeArrayNode.get("data").get("items")));

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).hasSize(10);
                    assertThat(indices).containsExactly(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);
                })
                .verifyComplete();
    }

    @Test
    void findArrayIndices_shouldReturnEmptySet_whenNoWildcardPathsExist() {
        // Given
        MappingConfiguration configWithoutWildcards = MappingConfiguration.builder()
                .name("SimpleMapping")
                .description("Simple mapping without wildcards")
                .fieldMappings(Map.of(
                    "id", "data.id",
                    "name", "data.name",
                    "status", "data.status"
                ))
                .build();

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(false);

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, configWithoutWildcards);

        // Then
        StepVerifier.create(result)
                .verifyError(); // Should error because no wildcard paths found
    }

    @Test
    void findArrayIndices_shouldHandleNullArrayNode() throws IOException {
        // Given
        JsonNode nullNode = TestResourceLoader.createNullJsonNode();

        when(pathResolver.hasArrayWildcard(anyString())).thenReturn(true);
        when(pathResolver.extractArrayBasePath(anyString())).thenReturn("data.nullArray");
        when(navigator.navigateToNode(any(JsonNode.class), anyString()))
                .thenReturn(Mono.just(nullNode));

        // When
        Mono<TreeSet<Integer>> result = arrayIndexFinder.findArrayIndices(testJsonNode, mappingConfiguration);

        // Then
        StepVerifier.create(result)
                .assertNext(indices -> {
                    assertThat(indices).isNotNull();
                    assertThat(indices).isEmpty();
                })
                .verifyComplete();
    }
}
