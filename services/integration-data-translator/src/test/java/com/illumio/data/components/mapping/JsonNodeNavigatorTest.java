package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class JsonNodeNavigatorTest {

    private JsonNodeNavigator jsonNodeNavigator;
    private JsonNode testJsonNode;

    @BeforeEach
    void setUp() throws IOException {
        jsonNodeNavigator = new JsonNodeNavigator();
        
        // Load test JSON data
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");
    }

    @Test
    void navigateToNode_shouldReturnCorrectNode_whenPathExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isNotNull();
                    assertThat(node.isArray()).isTrue();
                    assertThat(node.size()).isGreaterThan(0);
                })
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnCorrectValue_whenPathPointsToSimpleField() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].name";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isNotNull();
                    assertThat(node.asText()).isEqualTo("CVE-2022-1304");
                })
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnCorrectValue_whenPathPointsToNestedField() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].vulnerableAsset.name";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isNotNull();
                    assertThat(node.asText()).isEqualTo("saki-RH8-ven-lab02");
                })
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnEmpty_whenPathDoesNotExist() {
        // Given
        String path = "data.nonExistentField.someValue";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnEmpty_whenArrayIndexOutOfBounds() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[999].name";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnEmpty_whenInvalidArrayIndex() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[invalid].name";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldHandleSimpleJsonStructure() throws IOException {
        // Given
        JsonNode simpleJson = TestResourceLoader.createJsonNode("""
            {
                "user": {
                    "name": "John Doe",
                    "age": 30,
                    "addresses": [
                        {"type": "home", "city": "New York"},
                        {"type": "work", "city": "Boston"}
                    ]
                }
            }
            """);
        String path = "user.addresses[1].city";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(simpleJson, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isNotNull();
                    assertThat(node.asText()).isEqualTo("Boston");
                })
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldReturnEmpty_whenRootNodeIsNull() {
        // Given
        JsonNode nullNode = null;
        String path = "some.path";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(nullNode, path);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldHandleEmptyPath() {
        // Given
        String path = "";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isEqualTo(testJsonNode);
                })
                .verifyComplete();
    }

    @Test
    void navigateToNode_shouldHandleSingleFieldPath() {
        // Given
        String path = "data";

        // When
        Mono<JsonNode> result = jsonNodeNavigator.navigateToNode(testJsonNode, path);

        // Then
        StepVerifier.create(result)
                .assertNext(node -> {
                    assertThat(node).isNotNull();
                    assertThat(node.has("vulnerabilityFindings")).isTrue();
                })
                .verifyComplete();
    }
}
