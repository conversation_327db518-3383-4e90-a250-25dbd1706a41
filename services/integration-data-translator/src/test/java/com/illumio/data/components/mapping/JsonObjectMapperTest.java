package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.model.MappingConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JsonObjectMapperTest {

    @Mock
    private ValueExtractor valueExtractor;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private JsonObjectMapper jsonObjectMapper;

    private JsonNode testJsonNode;
    private MappingConfiguration mappingConfiguration;

    @BeforeEach
    void setUp() throws IOException {
        // Load test data
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");

        // Create mapping configuration manually using builder
        mappingConfiguration = MappingConfiguration.builder()
                .name("VulnerabilityDataMapping")
                .description("Mapping configuration for vulnerability meta data")
                .fieldMappings(Map.of(
                    "cveId", "data.vulnerabilityFindings.nodes[*].name",
                    "description", "data.vulnerabilityFindings.nodes[*].CVEDescription",
                    "severity", "data.vulnerabilityFindings.nodes[*].severity"
                ))
                .build();
    }

    @Test
    void mapSingleObject_shouldReturnMappedJsonString_whenValidInputProvided() throws Exception {
        // Given
        Integer arrayIndex = 0;
        String expectedJson = """
            {
                "cveId": "CVE-2022-1304",
                "description": "An out-of-bounds read/write vulnerability...",
                "severity": "MEDIUM"
            }
            """;

        // Mock value extractor responses
        when(valueExtractor.extractValue(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just("CVE-2022-1304"))
                .thenReturn(Mono.just("An out-of-bounds read/write vulnerability..."))
                .thenReturn(Mono.just("MEDIUM"));

        when(objectMapper.writeValueAsString(any())).thenReturn(expectedJson);

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isNotNull();
                    assertThat(jsonString).contains("CVE-2022-1304");
                    assertThat(jsonString).contains("MEDIUM");
                })
                .verifyComplete();
    }

    @Test
    void mapSingleObject_shouldHandleNullValues_whenSomeFieldsAreNull() throws Exception {
        // Given
        Integer arrayIndex = 0;
        String expectedJson = """
            {
                "cveId": "CVE-2022-1304",
                "severity": "MEDIUM"
            }
            """;

        // Mock value extractor responses - some return null
        when(valueExtractor.extractValue(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just("CVE-2022-1304"))
                .thenReturn(Mono.empty()) // null/empty value
                .thenReturn(Mono.just("MEDIUM"));

        when(objectMapper.writeValueAsString(any())).thenReturn(expectedJson);

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isNotNull();
                    assertThat(jsonString).contains("CVE-2022-1304");
                    assertThat(jsonString).contains("MEDIUM");
                    // Should not contain the null field
                })
                .verifyComplete();
    }

    @Test
    void mapSingleObject_shouldReturnEmpty_whenAllFieldsAreNull() throws Exception {
        // Given
        Integer arrayIndex = 0;

        // Mock value extractor to return empty for all fields
        when(valueExtractor.extractValue(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.empty());

        when(objectMapper.writeValueAsString(any())).thenReturn("{}");

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isEqualTo("{}");
                })
                .verifyComplete();
    }

    @Test
    void mapSingleObject_shouldReturnEmpty_whenObjectMapperThrowsException() throws Exception {
        // Given
        Integer arrayIndex = 0;

        when(valueExtractor.extractValue(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just("test-value"));

        when(objectMapper.writeValueAsString(any())).thenThrow(new RuntimeException("JSON serialization error"));

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete(); // Should return empty on error
    }

    @Test
    void mapSingleObject_shouldHandleComplexValues() throws Exception {
        // Given
        Integer arrayIndex = 0;
        String expectedJson = """
            {
                "cveId": "CVE-2022-1304",
                "score": 7.8,
                "hasExploit": false
            }
            """;

        // Mock value extractor responses with different types
        when(valueExtractor.extractValue(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just("CVE-2022-1304"))
                .thenReturn(Mono.just(7.8))
                .thenReturn(Mono.just(false));

        when(objectMapper.writeValueAsString(any())).thenReturn(expectedJson);

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isNotNull();
                    assertThat(jsonString).contains("CVE-2022-1304");
                    assertThat(jsonString).contains("7.8");
                    assertThat(jsonString).contains("false");
                })
                .verifyComplete();
    }

    @Test
    void mapSingleObject_shouldHandleEmptyMappingConfiguration() throws Exception {
        // Given
        MappingConfiguration emptyConfig = MappingConfiguration.builder()
                .name("EmptyMapping")
                .description("Empty mapping configuration")
                .fieldMappings(Map.of()) // Empty map
                .build();
        Integer arrayIndex = 0;

        when(objectMapper.writeValueAsString(any())).thenReturn("{}");

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, emptyConfig, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isEqualTo("{}");
                })
                .verifyComplete();
    }

    @Test
    void mapSingleObject_shouldReturnEmpty_whenArrayIndexIsNull() throws Exception {
        // Given
        Integer arrayIndex = null;

        // When null arrayIndex is passed, the method should complete empty
        // because it cannot process wildcard paths without a valid index

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(testJsonNode, mappingConfiguration, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete(); // Should complete empty when arrayIndex is null
    }

    @Test
    void mapSingleObject_shouldMapCEFSemanticFields_whenCEFMappingConfigurationProvided() throws Exception {
        // Given
        MappingConfiguration cefMappingConfig = MappingConfiguration.builder()
                .name("CEFSemanticMapping")
                .description("Mapping configuration for CEF semantic JSON to Kusto CommonSecurityLog fields")
                .fieldMappings(Map.of(
                    "SrcIP", "source.src"
                ))
                .build();

        Integer arrayIndex = 0;
        String expectedJson = """
            {
                "SrcIP": "*************"
            }
            """;

        // Create test JSON with CEF semantic structure
        JsonNode cefTestJson = TestResourceLoader.createJsonNode("""
            {
                "source": {
                    "src": "*************"
                }
            }
            """);

        // Mock value extractor to return the source IP
        when(valueExtractor.extractValue(any(JsonNode.class), eq("source.src"), anyInt()))
                .thenReturn(Mono.just("*************"));

        when(objectMapper.writeValueAsString(any())).thenReturn(expectedJson);

        // When
        Mono<String> result = jsonObjectMapper.mapSingleObject(cefTestJson, cefMappingConfig, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(jsonString -> {
                    assertThat(jsonString).isNotNull();
                    assertThat(jsonString).contains("SrcIP");
                    assertThat(jsonString).contains("*************");
                })
                .verifyComplete();
    }
}
