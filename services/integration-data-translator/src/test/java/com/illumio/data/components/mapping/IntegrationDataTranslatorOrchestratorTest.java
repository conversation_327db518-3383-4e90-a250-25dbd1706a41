package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.configuration.MappingConfigurationLoader;
import com.illumio.data.components.model.MappingConfiguration;
import com.illumio.data.components.producer.JsonOutputSenderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Map;
import java.util.TreeSet;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IntegrationDataTranslatorOrchestratorTest {

    @Mock
    private MappingConfigurationLoader configLoader;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private ArrayIndexFinder arrayIndexFinder;

    @Mock
    private JsonObjectMapper jsonObjectMapper;

    @Mock
    private JsonOutputSenderService jsonOutputSenderService;

    @InjectMocks
    private IntegrationDataTranslatorOrchestrator orchestrator;

    private ObjectMapper realObjectMapper;

    @BeforeEach
    void setUp() {
        realObjectMapper = new ObjectMapper();
    }

    @Test
    void loadMappingConfig_shouldValidateAllMappingFiles_whenAllFilesAreValid() throws IOException {
        // Given - Test all mapping files exist and can be loaded
        String[] mappingFiles = TestResourceLoader.getAllMappingFileNames();

        for (String mappingFileName : mappingFiles) {
            String dataType = TestResourceLoader.getDataTypeFromFileName(mappingFileName);
            MappingConfiguration expectedConfig = createMappingConfigurationFromFile(mappingFileName);

            // Mock the config loader for each data type
            when(configLoader.loadMappingConfig(dataType)).thenReturn(Mono.just(expectedConfig));

            // When
            Mono<MappingConfiguration> result = configLoader.loadMappingConfig(dataType);

            // Then - Verify each configuration loads correctly
            StepVerifier.create(result)
                    .assertNext(config -> {
                        assertThat(config).isNotNull();
                        assertThat(config.getName()).isNotEmpty();
                        assertThat(config.getDescription()).isNotEmpty();
                        assertThat(config.getFieldMappings()).isNotEmpty();

                        // Verify expected mapped files exist
                        String expectedMappedFileName = TestResourceLoader.getExpectedMappedFileName(mappingFileName);
                        try {
                            String expectedResult = TestResourceLoader.loadExpectedMappedResult(expectedMappedFileName);
                            assertThat(expectedResult).isNotEmpty();

                            // Verify it's valid JSON
                            JsonNode expectedJson = realObjectMapper.readTree(expectedResult);
                            assertThat(expectedJson).isNotNull();
                        } catch (IOException e) {
                            throw new RuntimeException("Expected mapped file not found or invalid: " + expectedMappedFileName, e);
                        }
                    })
                    .verifyComplete();
        }
    }

    @Test
    void loadMappingConfig_shouldLoadCorrectConfiguration_whenValidDataTypeProvided() throws IOException {
        // Given
        String dataType = "vulnerability-data";

        MappingConfiguration expectedConfig = MappingConfiguration.builder()
                .name("VulnerabilityDataMapping")
                .description("Mapping configuration for vulnerability meta data")
                .fieldMappings(Map.of(
                    "cveId", "data.vulnerabilityFindings.nodes[*].name",
                    "description", "data.vulnerabilityFindings.nodes[*].CVEDescription",
                    "severity", "data.vulnerabilityFindings.nodes[*].severity"
                ))
                .build();

        // Mock the config loader to return the expected configuration
        when(configLoader.loadMappingConfig(dataType)).thenReturn(Mono.just(expectedConfig));

        // When
        Mono<MappingConfiguration> result = configLoader.loadMappingConfig(dataType);

        // Then
        StepVerifier.create(result)
                .assertNext(config -> {
                    assertThat(config).isNotNull();
                    assertThat(config.getName()).isEqualTo("VulnerabilityDataMapping");
                    assertThat(config.getDescription()).isEqualTo("Mapping configuration for vulnerability meta data");
                    assertThat(config.getFieldMappings()).containsKey("cveId");
                    assertThat(config.getFieldMappings().get("cveId")).isEqualTo("data.vulnerabilityFindings.nodes[*].name");
                })
                .verifyComplete();
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "vulnerability-data.json",
        "device-data.json",
        "issue-data.json",
        "site-data.json",
        "boundary-data.json"
    })
    void loadMappingConfig_shouldLoadAllMappingConfigurations_whenValidFileNamesProvided(String mappingFileName) throws IOException {
        // Given
        String dataType = TestResourceLoader.getDataTypeFromFileName(mappingFileName);
        MappingConfiguration expectedConfig = createMappingConfigurationFromFile(mappingFileName);

        // Mock the config loader
        when(configLoader.loadMappingConfig(dataType)).thenReturn(Mono.just(expectedConfig));

        // When
        Mono<MappingConfiguration> result = configLoader.loadMappingConfig(dataType);

        // Then
        StepVerifier.create(result)
                .assertNext(config -> {
                    assertThat(config).isNotNull();
                    assertThat(config.getName()).isNotEmpty();
                    assertThat(config.getDescription()).isNotEmpty();
                    assertThat(config.getFieldMappings()).isNotEmpty();

                    // Verify specific mappings based on data type
                    switch (dataType) {
                        case "vulnerability-data" -> {
                            assertThat(config.getName()).isEqualTo("VulnerabilityDataMapping");
                            assertThat(config.getFieldMappings()).containsKey("cveId");
                            assertThat(config.getFieldMappings().get("cveId")).contains("nodes[*].name");
                        }
                        case "device-data" -> {
                            assertThat(config.getName()).isEqualTo("DeviceDataMapping");
                            assertThat(config.getFieldMappings()).containsKey("deviceId");
                            assertThat(config.getFieldMappings().get("deviceId")).contains("results[*].id");
                        }
                        case "issue-data" -> {
                            assertThat(config.getName()).isEqualTo("IssueDataMapping");
                            assertThat(config.getFieldMappings()).containsKey("name");
                            assertThat(config.getFieldMappings().get("name")).contains("issues.nodes[*]");
                        }
                        case "site-data" -> {
                            assertThat(config.getName()).isEqualTo("SiteDataMapping");
                            assertThat(config.getFieldMappings()).containsKey("siteId");
                            assertThat(config.getFieldMappings().get("siteId")).contains("sites[*].id");
                        }
                        case "boundary-data" -> {
                            assertThat(config.getName()).isEqualTo("BoundaryDataMapping");
                            assertThat(config.getFieldMappings()).containsKey("boundaryId");
                            assertThat(config.getFieldMappings().get("boundaryId")).contains("boundaries[*].id");
                        }
                    }
                })
                .verifyComplete();
    }

    @Test
    void mapJsonData_shouldValidateExpectedMappedResults_whenAllExpectedFilesExist() throws IOException {
        // Given - Test that all expected mapped result files exist and are valid JSON
        String[] mappingFiles = TestResourceLoader.getAllMappingFileNames();

        for (String mappingFileName : mappingFiles) {
            String expectedMappedFileName = TestResourceLoader.getExpectedMappedFileName(mappingFileName);

            // When - Load expected mapped result
            String expectedResult = TestResourceLoader.loadExpectedMappedResult(expectedMappedFileName);
            JsonNode expectedJson = realObjectMapper.readTree(expectedResult);

            // Then - Verify the expected result is valid and has expected structure
            assertThat(expectedResult).isNotEmpty();
            assertThat(expectedJson).isNotNull();

            if (expectedJson.isArray()) {
                assertThat(expectedJson.size()).isGreaterThan(0);
                // Verify each object in the array has fields
                for (JsonNode item : expectedJson) {
                    assertThat(item.isObject()).isTrue();
                    assertThat(item.size()).isGreaterThan(0);
                }
            } else {
                assertThat(expectedJson.isObject()).isTrue();
                assertThat(expectedJson.size()).isGreaterThan(0);
            }
        }
    }

    @Test
    void mapJsonData_shouldHandleError_whenConfigurationLoadingFails() {
        // Given
        String dataType = "invalid-data-type";
        String jsonData = "{}";

        when(configLoader.loadMappingConfig(dataType))
                .thenReturn(Mono.error(new RuntimeException("Configuration not found")));

        // When
        Flux<Void> result = orchestrator.mapJsonData(jsonData, dataType);

        // Then
        StepVerifier.create(result)
                .verifyComplete(); // Should complete empty on error
    }

    @Test
    void mapJsonData_shouldHandleError_whenJsonParsingFails() throws IOException {
        // Given
        String dataType = "vulnerability-data";
        String invalidJsonData = "{ invalid json }";

        MappingConfiguration config = MappingConfiguration.builder()
                .name("TestMapping")
                .fieldMappings(Map.of("field", "path"))
                .build();

        when(configLoader.loadMappingConfig(dataType)).thenReturn(Mono.just(config));
        when(objectMapper.readTree(invalidJsonData))
                .thenThrow(new RuntimeException("Invalid JSON"));

        // When
        Flux<Void> result = orchestrator.mapJsonData(invalidJsonData, dataType);

        // Then
        StepVerifier.create(result)
                .verifyComplete(); // Should complete empty on error
    }

    // Helper methods
    private MappingConfiguration createMappingConfigurationFromFile(String mappingFileName) throws IOException {
        String configContent = TestResourceLoader.loadMappingConfigurationAsString(mappingFileName);
        JsonNode configNode = realObjectMapper.readTree(configContent);

        String name = configNode.get("name").asText();
        String description = configNode.get("description").asText();
        Map<String, String> fieldMappings = realObjectMapper.convertValue(
            configNode.get("fieldMappings"),
            realObjectMapper.getTypeFactory().constructMapType(Map.class, String.class, String.class)
        );

        return MappingConfiguration.builder()
                .name(name)
                .description(description)
                .fieldMappings(fieldMappings)
                .build();
    }

}
