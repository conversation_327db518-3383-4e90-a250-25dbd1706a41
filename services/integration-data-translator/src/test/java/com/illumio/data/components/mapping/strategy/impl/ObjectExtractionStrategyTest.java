package com.illumio.data.components.mapping.strategy.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.JsonNodeNavigator;
import com.illumio.data.components.mapping.JsonPathResolver;
import com.illumio.data.components.mapping.JsonValueConverter;
import com.illumio.data.components.mapping.TestResourceLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ObjectExtractionStrategyTest {

    @Mock
    private JsonPathResolver pathResolver;

    @Mock
    private JsonNodeNavigator navigator;

    @Mock
    private JsonValueConverter converter;

    @InjectMocks
    private ObjectExtractionStrategy strategy;

    private JsonNode testJsonNode;

    @BeforeEach
    void setUp() throws IOException {
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");
    }

    @Test
    void supports_shouldReturnTrue_whenPathContainsObjectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].vulnerableAsset";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void supports_shouldReturnFalse_whenPathDoesNotContainObjectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnFalse_whenPathContainsOnlyCollectMarker() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collect].name";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void supports_shouldReturnTrue_whenPathContainsBothMarkersButObjectMarkerPresent() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collect][@object].vulnerableAsset";

        // When
        boolean result = strategy.supports(jsonPath);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void extract_shouldReturnCompleteObject_whenObjectPathExists() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].vulnerableAsset";
        String cleanPath = "data.vulnerabilityFindings.nodes.vulnerableAsset";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].vulnerableAsset";
        int arrayIndex = 0;

        JsonNode vulnerableAssetNode = TestResourceLoader.createJsonNode("""
            {
                "id": "asset-123",
                "name": "saki-RH8-ven-lab02",
                "type": "VM",
                "cloudPlatform": "AWS"
            }
            """);

        Map<String, Object> expectedObject = Map.of(
            "id", "asset-123",
            "name", "saki-RH8-ven-lab02",
            "type", "VM",
            "cloudPlatform", "AWS"
        );

        when(pathResolver.removeMarker(jsonPath, "[@object]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(vulnerableAssetNode));
        when(converter.convertJsonNodeToValue(vulnerableAssetNode)).thenReturn(Mono.just(expectedObject));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    @SuppressWarnings("unchecked")
                    Map<String, Object> objectMap = (Map<String, Object>) value;
                    assertThat(objectMap).hasSize(4);
                    assertThat(objectMap.get("id")).isEqualTo("asset-123");
                    assertThat(objectMap.get("name")).isEqualTo("saki-RH8-ven-lab02");
                    assertThat(objectMap.get("type")).isEqualTo("VM");
                    assertThat(objectMap.get("cloudPlatform")).isEqualTo("AWS");
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnCEFSemanticObject_whenCEFObjectPathProvided() throws IOException {
        // Given
        String jsonPath = "source[@object]";
        String cleanPath = "source";
        String resolvedPath = "source";
        int arrayIndex = -1; // No array index for CEF semantic structure

        JsonNode cefSourceNode = TestResourceLoader.createJsonNode("""
            {
                "src": "*************",
                "port": 8080,
                "hostname": "web-server-01",
                "mac": "00:1B:44:11:3A:B7"
            }
            """);

        Map<String, Object> expectedCefObject = Map.of(
            "src", "*************",
            "port", 8080,
            "hostname", "web-server-01",
            "mac", "00:1B:44:11:3A:B7"
        );

        when(pathResolver.removeMarker(jsonPath, "[@object]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(any(JsonNode.class), anyString())).thenReturn(Mono.just(cefSourceNode));
        when(converter.convertJsonNodeToValue(cefSourceNode)).thenReturn(Mono.just(expectedCefObject));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    @SuppressWarnings("unchecked")
                    Map<String, Object> cefMap = (Map<String, Object>) value;
                    assertThat(cefMap).hasSize(4);
                    assertThat(cefMap.get("src")).isEqualTo("*************");
                    assertThat(cefMap.get("port")).isEqualTo(8080);
                    assertThat(cefMap.get("hostname")).isEqualTo("web-server-01");
                    assertThat(cefMap.get("mac")).isEqualTo("00:1B:44:11:3A:B7");
                })
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmpty_whenObjectPathDoesNotExist() {
        // Given
        String jsonPath = "data.nonExistent[@object].field";
        String cleanPath = "data.nonExistent.field";
        String resolvedPath = "data.nonExistent[0].field";
        int arrayIndex = 0;

        when(pathResolver.removeMarker(jsonPath, "[@object]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extract_shouldReturnEmpty_whenConverterReturnsEmpty() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].vulnerableAsset";
        String cleanPath = "data.vulnerabilityFindings.nodes.vulnerableAsset";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].vulnerableAsset";
        int arrayIndex = 0;

        JsonNode nullNode = TestResourceLoader.createNullJsonNode();

        when(pathResolver.removeMarker(jsonPath, "[@object]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(nullNode));
        when(converter.convertJsonNodeToValue(nullNode)).thenReturn(Mono.empty());

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extract_shouldHandleNestedObjects_whenDeepObjectPathProvided() throws IOException {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].vulnerableAsset.cloudProvider";
        String cleanPath = "data.vulnerabilityFindings.nodes.vulnerableAsset.cloudProvider";
        String resolvedPath = "data.vulnerabilityFindings.nodes[0].vulnerableAsset.cloudProvider";
        int arrayIndex = 0;

        JsonNode cloudProviderNode = TestResourceLoader.createJsonNode("""
            {
                "name": "AWS",
                "region": "us-east-1",
                "accountId": "************",
                "services": ["EC2", "S3", "RDS"]
            }
            """);

        Map<String, Object> expectedCloudProvider = Map.of(
            "name", "AWS",
            "region", "us-east-1",
            "accountId", "************",
            "services", java.util.Arrays.asList("EC2", "S3", "RDS")
        );

        when(pathResolver.removeMarker(jsonPath, "[@object]")).thenReturn(cleanPath);
        when(pathResolver.resolveArrayPath(cleanPath, arrayIndex)).thenReturn(resolvedPath);
        when(navigator.navigateToNode(testJsonNode, resolvedPath)).thenReturn(Mono.just(cloudProviderNode));
        when(converter.convertJsonNodeToValue(cloudProviderNode)).thenReturn(Mono.just(expectedCloudProvider));

        // When
        Mono<Object> result = strategy.extract(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    @SuppressWarnings("unchecked")
                    Map<String, Object> providerMap = (Map<String, Object>) value;
                    assertThat(providerMap.get("name")).isEqualTo("AWS");
                    assertThat(providerMap.get("region")).isEqualTo("us-east-1");
                    assertThat(providerMap.get("accountId")).isEqualTo("************");
                    assertThat(providerMap.get("services")).isInstanceOf(java.util.List.class);
                })
                .verifyComplete();
    }

}
