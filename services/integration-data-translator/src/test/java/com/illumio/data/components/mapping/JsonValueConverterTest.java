package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class JsonValueConverterTest {

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private JsonValueConverter jsonValueConverter;

    private ObjectMapper realObjectMapper;

    @BeforeEach
    void setUp() {
        realObjectMapper = new ObjectMapper();
        // Use real ObjectMapper for most tests unless specifically mocking
        jsonValueConverter = new JsonValueConverter(realObjectMapper);
    }

    @Test
    void convertJsonNodeToValue_shouldReturnNull_whenNodeIsNull() {
        // Given
        JsonNode nullNode = null;

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(nullNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isNull();
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnNull_whenNodeIsNullNode() throws IOException {
        // Given
        JsonNode nullNode = TestResourceLoader.createNullJsonNode();

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(nullNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isNull();
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnBoolean_whenNodeIsBoolean() throws IOException {
        // Given
        JsonNode booleanNode = TestResourceLoader.createJsonNode("true");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(booleanNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Boolean.class);
                    assertThat(value).isEqualTo(true);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnString_whenNodeIsString() throws IOException {
        // Given
        JsonNode stringNode = TestResourceLoader.createJsonNode("\"Hello World\"");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(stringNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(String.class);
                    assertThat(value).isEqualTo("Hello World");
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnInteger_whenNodeIsInteger() throws IOException {
        // Given
        JsonNode intNode = TestResourceLoader.createJsonNode("42");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(intNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Integer.class);
                    assertThat(value).isEqualTo(42);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnLong_whenNodeIsLong() throws IOException {
        // Given
        JsonNode longNode = TestResourceLoader.createJsonNode("9223372036854775807");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(longNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Long.class);
                    assertThat(value).isEqualTo(9223372036854775807L);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnDouble_whenNodeIsDouble() throws IOException {
        // Given
        JsonNode doubleNode = TestResourceLoader.createJsonNode("3.14159");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(doubleNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Double.class);
                    assertThat(value).isEqualTo(3.14159);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnList_whenNodeIsArray() throws IOException {
        // Given
        JsonNode arrayNode = TestResourceLoader.createJsonNode("""
            ["item1", "item2", "item3"]
            """);

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(arrayNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    List<?> list = (List<?>) value;
                    assertThat(list).hasSize(3);
                    assertThat(list).containsExactly("item1", "item2", "item3");
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnListWithMixedTypes_whenArrayHasMixedTypes() throws IOException {
        // Given
        JsonNode mixedArrayNode = TestResourceLoader.createJsonNode("""
            ["string", 42, true, 3.14]
            """);

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(mixedArrayNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    List<?> list = (List<?>) value;
                    assertThat(list).hasSize(4);
                    assertThat(list.get(0)).isEqualTo("string");
                    assertThat(list.get(1)).isEqualTo(42);
                    assertThat(list.get(2)).isEqualTo(true);
                    assertThat(list.get(3)).isEqualTo(3.14);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnEmptyList_whenArrayIsEmpty() throws IOException {
        // Given
        JsonNode emptyArrayNode = TestResourceLoader.createJsonNode("[]");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(emptyArrayNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    List<?> list = (List<?>) value;
                    assertThat(list).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnObject_whenNodeIsObject() throws IOException {
        // Given
        JsonNode objectNode = TestResourceLoader.createJsonNode("""
            {
                "name": "John Doe",
                "age": 30,
                "active": true
            }
            """);

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(objectNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    Map<?, ?> map = (Map<?, ?>) value;
                    assertThat(map).hasSize(3);
                    assertThat(map.get("name")).isEqualTo("John Doe");
                    assertThat(map.get("age")).isEqualTo(30);
                    assertThat(map.get("active")).isEqualTo(true);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldReturnEmptyObject_whenObjectIsEmpty() throws IOException {
        // Given
        JsonNode emptyObjectNode = TestResourceLoader.createJsonNode("{}");

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(emptyObjectNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    Map<?, ?> map = (Map<?, ?>) value;
                    assertThat(map).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldHandleNestedStructures() throws IOException {
        // Given
        JsonNode nestedNode = TestResourceLoader.createJsonNode("""
            {
                "user": {
                    "name": "John",
                    "addresses": [
                        {"type": "home", "city": "New York"},
                        {"type": "work", "city": "Boston"}
                    ]
                },
                "scores": [85, 92, 78]
            }
            """);

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(nestedNode);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    Map<?, ?> map = (Map<?, ?>) value;
                    assertThat(map).containsKey("user");
                    assertThat(map).containsKey("scores");
                    
                    // Check nested user object
                    Map<?, ?> user = (Map<?, ?>) map.get("user");
                    assertThat(user.get("name")).isEqualTo("John");
                    assertThat(user.get("addresses")).isInstanceOf(List.class);
                    
                    // Check scores array
                    List<?> scores = (List<?>) map.get("scores");
                    assertThat(scores).containsExactly(85, 92, 78);
                })
                .verifyComplete();
    }

    @Test
    void convertJsonNodeToValue_shouldHandleObjectMapperException() throws IOException {
        // Given
        JsonNode objectNode = TestResourceLoader.createJsonNode("""
            {"name": "test"}
            """);
        
        // Use mocked ObjectMapper that throws exception
        jsonValueConverter = new JsonValueConverter(objectMapper);
        when(objectMapper.convertValue(any(JsonNode.class), eq(Object.class)))
                .thenThrow(new RuntimeException("Conversion failed"));

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(objectNode);

        // Then
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }

    @Test
    void convertJsonNodeToValue_shouldHandleComplexRealWorldData() throws IOException {
        // Given - Use actual test data
        JsonNode realData = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");
        JsonNode firstVulnerability = realData.get("data").get("vulnerabilityFindings").get("nodes").get(0);

        // When
        Mono<Object> result = jsonValueConverter.convertJsonNodeToValue(firstVulnerability);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(Map.class);
                    Map<?, ?> map = (Map<?, ?>) value;
                    assertThat(map).containsKey("id");
                    assertThat(map).containsKey("name");
                    assertThat(map).containsKey("severity");
                    assertThat(map).containsKey("score");
                    assertThat(map).containsKey("hasExploit");
                    assertThat(map.get("name")).isEqualTo("CVE-2022-1304");
                    assertThat(map.get("severity")).isEqualTo("MEDIUM");
                    assertThat(map.get("hasExploit")).isEqualTo(false);
                })
                .verifyComplete();
    }
}
