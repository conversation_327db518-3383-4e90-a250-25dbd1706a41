package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.strategy.ExtractionStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValueExtractorTest {

    @Mock
    private ExtractionStrategy strategy1;

    @Mock
    private ExtractionStrategy strategy2;

    @Mock
    private ExtractionStrategy strategy3;

    @InjectMocks
    private ValueExtractor valueExtractor;

    private JsonNode testJsonNode;

    @BeforeEach
    void setUp() throws IOException {
        // Load test data
        testJsonNode = TestResourceLoader.loadJsonResponse("wiz_vulnerability_response.json");

        // Set up the strategies list
        List<ExtractionStrategy> strategies = Arrays.asList(strategy1, strategy2, strategy3);
        valueExtractor = new ValueExtractor(strategies);
    }

    @Test
    void extractValue_shouldReturnValue_whenFirstStrategySupportPath() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 0;
        Object expectedValue = "CVE-2022-1304";

        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldReturnValue_whenSecondStrategySupportPath() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collect].name";
        int arrayIndex = 0;
        Object expectedValue = Arrays.asList("CVE-2022-1304", "CVE-2021-3445", "CVE-2022-32189");

        when(strategy1.supports(jsonPath)).thenReturn(false);
        when(strategy2.supports(jsonPath)).thenReturn(true);
        when(strategy2.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldReturnValue_whenThirdStrategySupportPath() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@object].name";
        int arrayIndex = 0;
        Object expectedValue = "mapped_object_value";

        when(strategy1.supports(jsonPath)).thenReturn(false);
        when(strategy2.supports(jsonPath)).thenReturn(false);
        when(strategy3.supports(jsonPath)).thenReturn(true);
        when(strategy3.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldReturnEmpty_whenNoStrategySupportPath() {
        // Given
        String jsonPath = "unsupported.path";
        int arrayIndex = 0;

        when(strategy1.supports(jsonPath)).thenReturn(false);
        when(strategy2.supports(jsonPath)).thenReturn(false);
        when(strategy3.supports(jsonPath)).thenReturn(false);

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extractValue_shouldReturnEmpty_whenStrategyReturnsEmpty() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].nonExistentField";
        int arrayIndex = 0;

        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.empty());

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extractValue_shouldReturnError_whenStrategyThrowsException() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 0;

        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.error(new RuntimeException("Extraction failed")));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }

    @Test
    void extractValue_shouldUseFirstSupportingStrategy_whenMultipleStrategiesSupport() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 0;
        Object expectedValue = "first_strategy_result";

        // Only stub the first strategy since it will be used and others won't be called
        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldHandleEmptyStrategiesList() {
        // Given
        ValueExtractor emptyExtractor = new ValueExtractor(Collections.emptyList());
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 0;

        // When
        Mono<Object> result = emptyExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extractValue_shouldHandleNullJsonNode() {
        // Given
        JsonNode nullNode = null;
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 0;

        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(), anyString(), anyInt()))
                .thenReturn(Mono.empty());

        // When
        Mono<Object> result = valueExtractor.extractValue(nullNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void extractValue_shouldHandleNegativeArrayIndex() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = -1;
        Object expectedValue = "result_for_negative_index";

        when(strategy1.supports(jsonPath)).thenReturn(true);
        when(strategy1.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldHandleComplexReturnTypes() {
        // Given
        String jsonPath = "data.vulnerabilityFindings.nodes[@collect]";
        int arrayIndex = 0;
        List<String> expectedValue = Arrays.asList("item1", "item2", "item3");

        when(strategy2.supports(jsonPath)).thenReturn(true);
        when(strategy2.extract(any(JsonNode.class), anyString(), anyInt()))
                .thenReturn(Mono.just(expectedValue));

        // When
        Mono<Object> result = valueExtractor.extractValue(testJsonNode, jsonPath, arrayIndex);

        // Then
        StepVerifier.create(result)
                .assertNext(value -> {
                    assertThat(value).isInstanceOf(List.class);
                    @SuppressWarnings("unchecked")
                    List<String> list = (List<String>) value;
                    assertThat(list).hasSize(3);
                    assertThat(value).isEqualTo(expectedValue);
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldExtractCEFSemanticFields_whenCEFPathProvided() throws IOException {
        // Given - CEF semantic JSON structure
        JsonNode cefJsonNode = TestResourceLoader.createJsonNode("""
            {
                "source": {
                    "src": "*************",
                    "port": 8080
                },
                "destination": {
                    "dst": "********",
                    "port": 443
                },
                "event": {
                    "severity": "High",
                    "name": "Suspicious Activity"
                }
            }
            """);

        String srcIpPath = "source.src";
        String dstIpPath = "destination.dst";
        String severityPath = "event.severity";
        int arrayIndex = 0;

        // Mock strategy to support CEF semantic paths
        when(strategy1.supports(srcIpPath)).thenReturn(true);
        when(strategy1.supports(dstIpPath)).thenReturn(true);
        when(strategy1.supports(severityPath)).thenReturn(true);

        when(strategy1.extract(cefJsonNode, srcIpPath, arrayIndex))
                .thenReturn(Mono.just("*************"));
        when(strategy1.extract(cefJsonNode, dstIpPath, arrayIndex))
                .thenReturn(Mono.just("********"));
        when(strategy1.extract(cefJsonNode, severityPath, arrayIndex))
                .thenReturn(Mono.just("High"));

        // When & Then - Test source IP extraction
        StepVerifier.create(valueExtractor.extractValue(cefJsonNode, srcIpPath, arrayIndex))
                .assertNext(value -> {
                    assertThat(value).isEqualTo("*************");
                })
                .verifyComplete();

        // When & Then - Test destination IP extraction
        StepVerifier.create(valueExtractor.extractValue(cefJsonNode, dstIpPath, arrayIndex))
                .assertNext(value -> {
                    assertThat(value).isEqualTo("********");
                })
                .verifyComplete();

        // When & Then - Test severity extraction
        StepVerifier.create(valueExtractor.extractValue(cefJsonNode, severityPath, arrayIndex))
                .assertNext(value -> {
                    assertThat(value).isEqualTo("High");
                })
                .verifyComplete();
    }

    @Test
    void extractValue_shouldHandleCEFSemanticFieldsWithMissingData_whenOptionalFieldsNotPresent() throws IOException {
        // Given - CEF semantic JSON with missing optional fields
        JsonNode incompleteCefJson = TestResourceLoader.createJsonNode("""
            {
                "source": {
                    "src": "*************"
                },
                "event": {
                    "name": "Basic Event"
                }
            }
            """);

        String missingDestinationPath = "destination.dst";
        String missingSeverityPath = "event.severity";
        int arrayIndex = 0;

        // Mock strategy to support paths but return empty for missing fields
        when(strategy1.supports(missingDestinationPath)).thenReturn(true);
        when(strategy1.supports(missingSeverityPath)).thenReturn(true);

        when(strategy1.extract(incompleteCefJson, missingDestinationPath, arrayIndex))
                .thenReturn(Mono.empty()); // Missing destination
        when(strategy1.extract(incompleteCefJson, missingSeverityPath, arrayIndex))
                .thenReturn(Mono.empty()); // Missing severity

        // When & Then - Test missing destination IP
        StepVerifier.create(valueExtractor.extractValue(incompleteCefJson, missingDestinationPath, arrayIndex))
                .verifyComplete(); // Should complete empty

        // When & Then - Test missing severity
        StepVerifier.create(valueExtractor.extractValue(incompleteCefJson, missingSeverityPath, arrayIndex))
                .verifyComplete(); // Should complete empty
    }
}
