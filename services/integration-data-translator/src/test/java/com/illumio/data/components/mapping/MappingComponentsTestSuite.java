package com.illumio.data.components.mapping;



import com.illumio.data.components.mapping.strategy.impl.CollectionExtractionStrategyTest;
import com.illumio.data.components.mapping.strategy.impl.ObjectExtractionStrategyTest;
import com.illumio.data.components.mapping.strategy.impl.SingleValueExtractionStrategyTest;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * Test suite for all mapping component classes.
 * This suite runs all the test classes for the components in the mapping package.
 *
 * To run this test suite:
 * - From IDE: Right-click and run as JUnit test
 * - From command line: ./gradlew test --tests "com.illumio.data.components.mapping.MappingComponentsTestSuite"
 *
 * Test Coverage:
 * - JsonNodeNavigator: Tests navigation through JSON structures with dot notation and array indices
 * - JsonPathResolver: Tests path resolution, wildcard handling, and marker removal
 * - ArrayIndexFinder: Tests finding array indices from JSON structures
 * - JsonObjectMapper: Tests mapping JSON to objects using configuration
 * - ValueExtractor: Tests value extraction using different strategies
 * - JsonValueConverter: Tests conversion of JsonNode to Java objects
 * - SingleValueExtractionStrategy: Tests single value extraction strategy
 * - ObjectExtractionStrategy: Tests object extraction with [@object] marker
 *
 * Test Data Sources:
 * - Mapping configurations loaded from: src/test/resources/mapping/
 * - JSON responses loaded from: src/test/resources/responses/api/
 * - Test utility class: TestResourceLoader for loading test resources
 */
@Suite
@SelectClasses({
    JsonNodeNavigatorTest.class,
    JsonPathResolverTest.class,
    ArrayIndexFinderTest.class,
    JsonObjectMapperTest.class,
    ValueExtractorTest.class,
    JsonValueConverterTest.class,
    SingleValueExtractionStrategyTest.class,
    ObjectExtractionStrategyTest.class
})
public class MappingComponentsTestSuite {

    // This class serves as a test suite runner
    // Individual test classes contain the actual test methods

    /*
     * Test Structure Overview:
     *
     * 1. TestResourceLoader - Utility class for loading test resources
     *    - Loads mapping configurations from resources/mapping/
     *    - Loads JSON responses from resources/responses/api/
     *    - Provides helper methods for creating test data
     *
     * 2. JsonNodeNavigatorTest - Tests JSON navigation functionality
     *    - Tests path navigation with dot notation
     *    - Tests array index navigation
     *    - Tests error handling for invalid paths
     *
     * 3. JsonPathResolverTest - Tests path resolution functionality
     *    - Tests wildcard replacement with array indices
     *    - Tests marker removal
     *    - Tests path extraction methods
     *
     * 4. ArrayIndexFinderTest - Tests array index discovery
     *    - Tests finding indices from JSON arrays
     *    - Tests handling of empty arrays and non-arrays
     *    - Tests error scenarios
     *
     * 5. JsonObjectMapperTest - Tests object mapping functionality
     *    - Tests mapping JSON to objects using field mappings
     *    - Tests handling of null values
     *    - Tests error scenarios
     *
     * 6. ValueExtractorTest - Tests value extraction coordination
     *    - Tests strategy selection and execution
     *    - Tests error handling and fallback behavior
     *    - Tests with multiple strategies
     *
     * 7. JsonValueConverterTest - Tests JSON to Java object conversion
     *    - Tests conversion of different JSON types
     *    - Tests handling of complex nested structures
     *    - Tests error scenarios
     *
     * 8. SingleValueExtractionStrategyTest - Tests single value extraction
     *    - Tests strategy support detection
     *    - Tests value extraction for different data types
     *    - Tests error handling and edge cases
     */
}
