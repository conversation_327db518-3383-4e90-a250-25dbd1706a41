package com.illumio.data.components.mapping;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class JsonPathResolverTest {

    private JsonPathResolver jsonPathResolver;

    @BeforeEach
    void setUp() {
        jsonPathResolver = new JsonPathResolver();
    }

    @Test
    void resolveArrayPath_shouldReplaceWildcardWithIndex_whenValidIndexProvided() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = 2;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes[2].name");
    }

    @Test
    void resolveArrayPath_shouldReplaceFirstWildcardOnly_whenMultipleWildcardsExist() {
        // Given
        String path = "data.items[*].subItems[*].value";
        int arrayIndex = 1;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.items[1].subItems[*].value");
    }

    @Test
    void resolveArrayPath_shouldRemoveWildcard_whenIndexIsMinusOne() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[*].name";
        int arrayIndex = -1;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes.name");
    }

    @Test
    void resolveArrayPath_shouldRemoveAllWildcards_whenIndexIsMinusOne() {
        // Given
        String path = "data.items[*].subItems[*].value";
        int arrayIndex = -1;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.items.subItems.value");
    }

    @Test
    void resolveArrayPath_shouldReturnOriginalPath_whenNoWildcardExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = 2;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes[0].name");
    }

    @Test
    void resolveArrayPath_shouldReturnOriginalPath_whenNegativeIndexAndNoWildcard() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].name";
        int arrayIndex = -1;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes[0].name");
    }

    @Test
    void resolveArrayPath_shouldHandleZeroIndex() {
        // Given
        String path = "data.items[*].name";
        int arrayIndex = 0;

        // When
        String result = jsonPathResolver.resolveArrayPath(path, arrayIndex);

        // Then
        assertThat(result).isEqualTo("data.items[0].name");
    }

    @Test
    void hasArrayWildcard_shouldReturnTrue_whenWildcardExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[*].name";

        // When
        boolean result = jsonPathResolver.hasArrayWildcard(path);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void hasArrayWildcard_shouldReturnFalse_whenNoWildcardExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].name";

        // When
        boolean result = jsonPathResolver.hasArrayWildcard(path);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void hasArrayWildcard_shouldReturnTrue_whenMultipleWildcardsExist() {
        // Given
        String path = "data.items[*].subItems[*].value";

        // When
        boolean result = jsonPathResolver.hasArrayWildcard(path);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void hasArrayWildcard_shouldReturnFalse_whenEmptyPath() {
        // Given
        String path = "";

        // When
        boolean result = jsonPathResolver.hasArrayWildcard(path);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void extractArrayBasePath_shouldReturnPathBeforeWildcard_whenWildcardExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[*].name";

        // When
        String result = jsonPathResolver.extractArrayBasePath(path);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes");
    }

    @Test
    void extractArrayBasePath_shouldReturnOriginalPath_whenNoWildcardExists() {
        // Given
        String path = "data.vulnerabilityFindings.nodes[0].name";

        // When
        String result = jsonPathResolver.extractArrayBasePath(path);

        // Then
        assertThat(result).isEqualTo("data.vulnerabilityFindings.nodes[0].name");
    }

    @Test
    void extractArrayBasePath_shouldReturnPathBeforeFirstWildcard_whenMultipleWildcardsExist() {
        // Given
        String path = "data.items[*].subItems[*].value";

        // When
        String result = jsonPathResolver.extractArrayBasePath(path);

        // Then
        assertThat(result).isEqualTo("data.items");
    }

    @Test
    void removeMarker_shouldRemoveSpecifiedMarker() {
        // Given
        String path = "data.items[@collect].name";
        String marker = "[@collect]";

        // When
        String result = jsonPathResolver.removeMarker(path, marker);

        // Then
        assertThat(result).isEqualTo("data.items.name");
    }

    @Test
    void removeMarker_shouldRemoveAllOccurrences() {
        // Given
        String path = "data[@object].items[@object].name";
        String marker = "[@object]";

        // When
        String result = jsonPathResolver.removeMarker(path, marker);

        // Then
        assertThat(result).isEqualTo("data.items.name");
    }

    @Test
    void removeMarker_shouldReturnOriginalPath_whenMarkerNotFound() {
        // Given
        String path = "data.items.name";
        String marker = "[@collect]";

        // When
        String result = jsonPathResolver.removeMarker(path, marker);

        // Then
        assertThat(result).isEqualTo("data.items.name");
    }
}
