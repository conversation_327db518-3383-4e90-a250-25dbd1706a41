{"name": "DeviceDataMapping", "description": "Mapping configuration for device meta data", "fieldMappings": {"deviceId": "data.results[*].id", "displayTitle": "data.results[*].displayTitle", "name": "data.results[*].name", "names": "data.results[*].names", "manufacturer": "data.results[*].manufacturer", "type": "data.results[*].type", "model": "data.results[*].model", "firstSeen": "data.results[*].firstSeen", "lastSeen": "data.results[*].lastSeen", "macAddress": "data.results[*].macAddress", "ipv4": "data.results[*].ipAddress", "ipv6": "data.results[*].ipv6[@collect]", "accessSwitch": "data.results[*].accessSwitch", "tags": "data.results[*].tags[@collect]", "purdueLevel": "data.results[*].purdueLevel", "riskScore": "data.results[*].riskLevel", "operatingSystem": "data.results[*].operatingSystem", "protections": "data.results[*].protections[@object]", "sensor": "data.results[*].sensor", "tier": "data.results[*].tier", "typeEnum": "data.results[*].typeEnum", "visibility": "data.results[*].visibility", "siteId": "data.results[*].site.id", "categoryId": "data.results[*].category.id", "boundaryId": "data.results[*].boundaries.id", "dataSources": "data.results[*].dataSources[@object]"}}