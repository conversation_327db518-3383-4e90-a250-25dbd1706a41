{"name": "IssueDataMapping", "description": "Mapping configuration for issue meta data", "fieldMappings": {"name": "data.issues.nodes[*].sourceRules[0].name", "description": "data.issues.nodes[*].sourceRules[0].controlDescription", "subscriptionId": "data.issues.nodes[*].entitySnapshot.subscriptionExternalId", "severity": "data.issues.nodes[*].severity", "status": "data.issues.nodes[*].status", "risks": "data.issues.nodes[*].sourceRules[0].risks[@collect]"}}