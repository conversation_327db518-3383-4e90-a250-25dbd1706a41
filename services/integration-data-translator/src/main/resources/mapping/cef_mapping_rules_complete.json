{"name": "CEFSemanticMapping", "description": "Mapping configuration for CEF semantic JSON to Kusto CommonSecurityLog fields", "fieldMappings": {"SrcIP": "source.src", "SrcId": "source.srcId", "CSSrcId": "source.csSrcId", "DestIP": "destination.dst", "DestId": "destination.destId", "CSDestId": "destination.csDestId", "Port": "network.port", "Protocol": "network.proto", "SentBytes": "network.out", "ReceivedBytes": "network.in", "SrcTenantId": "source.srcTenantId", "SrcSubId": "source.srcSubId", "SrcRegion": "source.srcRegion", "SrcResId": "source.srcResId", "SrcVnetId": "source.srcVnetId", "SourceUserName": "source.suser", "DestTenantId": "destination.destTenantId", "DestSubId": "destination.destSubId", "DestRegion": "destination.destRegion", "DestResId": "destination.destResId", "DestVnetId": "destination.destVnetId", "DestinationUserName": "destination.duser", "SrcFlowType": "source.srcFlowType", "DestFlowType": "destination.destFlowType", "SrcDeviceId": "source.srcDeviceId", "SrcFirewallId": "source.srcFirewallId", "SourceUserID": "source.suid", "DestDeviceId": "destination.destDeviceId", "DestFirewallId": "destination.destFirewallId", "DestUserId": "destination.duid", "SrcResourceType": "source.srcResourceType", "DestResourceType": "destination.destResourceType", "SrcThreatLevel": "source.srcThreatLevel", "DestThreatLevel": "destination.destThreatLevel", "SrcIsWellknown": "source.srcIsWellknown", "DestIsWellknown": "destination.destIsWellknown", "SrcDomain": "source.srcDomain", "DestDomain": "destination.destDomain", "SrcCountry": "source.srcCountry", "DestCountry": "destination.destCountry", "SrcCity": "source.srcCity", "DestCity": "destination.destCity", "SrcCloudProvider": "source.srcCloudProvider", "DestCloudProvider": "destination.destCloudProvider", "SrcGeoRegion": "source.srcGeoRegion", "DestGeoRegion": "destination.destGeoRegion", "SourceHostName": "source.shost", "SourceMACAddress": "source.smac", "SourceNTDomain": "source.sntdom", "SourceProcessId": "source.spid", "SourceProcessName": "source.sproc", "SourceUserPrivileges": "source.spriv", "DeviceAction": "event.act", "DeviceAddress": "device.dvc", "DestinationDnsDomain": "destination.destinationDnsDomain", "DestinationHostName": "destination.dhost", "DestinationMACAddress": "destination.dmac", "DestinationNTDomain": "destination.dntdom", "DestinationProcessId": "destination.dpid", "DestinationProcessName": "destination.dproc", "DestinationServiceName": "destination.destinationServiceName", "DestinationTranslatedAddress": "destination.destinationTranslatedAddress", "LogSeverity": "event.Severity", "MaliciousIP": "security.MaliciousIP", "MaliciousIPCountry": "security.MaliciousIPCountry", "MaliciousIPLatitude": "security.MaliciousIPLatitude", "MaliciousIPLongitude": "security.MaliciousIPLongitude", "LAWTenantId": "security.lawTenantId", "ThreatConfidence": "security.ThreatConfidence", "ThreatDescription": "security.ThreatDescription", "ThreatSeverity": "security.ThreatSeverity", "StartTime": "metadata.startTime", "EndTime": "metadata.endTime", "SourceDnsDomain": "source.sourceDnsDomain", "SourceServiceName": "source.sourceServiceName", "SourceSystem": "custom.SourceSystem", "DeviceMacAddress": "device.dvcmac", "DeviceName": "device.dvchost", "DeviceOutboundInterface": "device.deviceOutboundInterface", "DeviceProduct": "device['Device Product']", "DeviceTranslatedAddress": "device.deviceTranslatedAddress", "DeviceVersion": "device['Device Version']", "DeviceTimeZone": "device.dtz", "DeviceExternalID": "device.deviceExternalId", "DeviceCustomNumber3": "custom.cn3", "ReceiptTime": "metadata.rt", "Activity": "event.Name", "AdditionalExtensions": "custom.additionalExtensions", "SourceZone": "source.sourceZone", "DestinationZone": "destination.destinationZone", "RequestURL": "application.Request", "Computer": "application.Host", "SrcCloudTags": "source.srcCloudTags", "DestCloudTags": "destination.destCloudTags", "FlowCount": "network.flowCount", "PacketsReceived": "network.packetsReceived", "PacketsSent": "network.packetsSent", "TrafficStatus": "network.trafficStatus", "SourceLabel": "source.sourceLabel", "DestinationLabel": "destination.destinationLabel", "SrcAccountName": "source.srcAccountName", "DestAccountName": "destination.destAccountName", "SrcResourceCategory": "source.srcResourceCategory", "DestResourceCategory": "destination.destResourceCategory", "SrcResourceName": "source.srcResourceName", "DestResourceName": "destination.destResourceName", "SrcResourceGroup": "source.srcResourceGroup", "DestResourceGroup": "destination.destResourceGroup", "SrcSubnetId": "source.srcSubnetId", "DestSubnetId": "destination.destSubnetId", "SrcCSLabel": "source.srcCSLabel", "DestCSLabel": "destination.destCSLabel", "DestinationExternalLabel": "destination.destinationExternalLabel", "DestinationExternalLabelCategory": "destination.destinationExternalLabelCategory", "SourceExternalLabel": "source.sourceExternalLabel", "SourceExternalLabelCategory": "source.sourceExternalLabelCategory", "SrcCSLabels": "source.srcCSLabels", "DestCSLabels": "destination.destCSLabels", "Hops": "network.hops", "DeniedAt": "network.deniedAt", "ApplicationProtocol": "network.app", "DeviceEventCategory": "event.cat", "EventCount": "event.cnt", "DeviceVendor": "device['<PERSON><PERSON>']", "DestinationTranslatedPort": "destination.destinationTranslatedPort", "CommunicationDirection": "network.deviceDirection", "DeviceDnsDomain": "device.deviceDnsDomain", "DeviceEventClassID": "event.DeviceEventClassID", "DeviceFacility": "device.deviceFacility", "DeviceInboundInterface": "device.deviceInboundInterface", "DeviceNtDomain": "device.deviceNtDomain", "DevicePayloadId": "device.devicePayloadId", "ProcessName": "device.deviceProcessName", "DestinationPort": "destination.dpt", "ProcessID": "device.dvcpid", "ExternalID": "custom.externalId", "FileCreateTime": "file.fileCreateTime", "FileHash": "file.fileHash", "FileID": "file.fileId", "FileModificationTime": "file.fileModificationTime", "FilePath": "file.filePath", "FilePermission": "file.filePermission", "FileType": "file.fileType", "FileName": "file.fname", "FileSize": "file.fsize", "Message": "custom.msg", "OldFileCreateTime": "file.oldFileCreateTime", "OldFileHash": "file.oldFileHash", "OldFileID": "file.oldFileId", "OldFileModificationTime": "file.oldFileModificationTime", "OldFileName": "file.oldFileName", "OldFilePath": "file.oldFilePath", "OldFilePermission": "file.oldFilePermission", "OldFileSize": "file.oldFileSize", "OldFileType": "file.oldFileType", "EventOutcome": "event.outcome", "Reason": "event.reason", "RequestClientApplication": "application.requestClientApplication", "RequestContext": "application.requestContext", "RequestCookies": "application.requestCookies", "RequestMethod": "application.requestMethod", "SourceTranslatedAddress": "source.sourceTranslatedAddress", "SourceTranslatedPort": "source.sourceTranslatedPort", "SourcePort": "source.spt", "EventType": "event.type", "DeviceCustomIPv6Address1": "custom.c6a1", "DeviceCustomIPv6Address1Label": "custom.c6a1Label", "DeviceCustomIPv6Address2": "custom.c6a2", "DeviceCustomIPv6Address2Label": "custom.c6a2Label", "DeviceCustomIPv6Address3": "custom.c6a3", "DeviceCustomIPv6Address3Label": "custom.c6a3Label", "DeviceCustomIPv6Address4": "custom.c6a4", "DeviceCustomIPv6Address4Label": "custom.c6a4Label", "DeviceCustomFloatingPoint1": "custom.cfp1", "DeviceCustomFloatingPoint1Label": "custom.cfp1Label", "DeviceCustomFloatingPoint2": "custom.cfp2", "DeviceCustomFloatingPoint2Label": "custom.cfp2Label", "DeviceCustomFloatingPoint3": "custom.cfp3", "DeviceCustomFloatingPoint3Label": "custom.cfp3Label", "DeviceCustomFloatingPoint4": "custom.cfp4", "DeviceCustomFloatingPoint4Label": "custom.cfp4Label", "DeviceCustomNumber1": "custom.cn1", "DeviceCustomNumber1Label": "custom.cn1Label", "DeviceCustomNumber2": "custom.cn2", "DeviceCustomNumber2Label": "custom.cn2Label", "DeviceCustomString1": "custom.cs1", "DeviceCustomString1Label": "custom.cs1Label", "DeviceCustomString2": "custom.cs2", "DeviceCustomString2Label": "custom.cs2Label", "DeviceCustomString3": "custom.cs3", "DeviceCustomString3Label": "custom.cs3Label", "DeviceCustomString4": "custom.cs4", "DeviceCustomString4Label": "custom.cs4Label", "DeviceCustomString5": "custom.cs5", "DeviceCustomString5Label": "custom.cs5Label", "DeviceCustomString6": "custom.cs6", "DeviceCustomString6Label": "custom.cs6Label", "FlexString1": "custom.flexString1", "FlexString1Label": "custom.flexString1Label", "FlexString2": "custom.flexString2", "FlexString2Label": "custom.flexString2Label", "DeviceCustomDate1": "custom.deviceCustomDate1", "DeviceCustomDate1Label": "custom.deviceCustomDate1Label", "DeviceCustomDate2": "custom.deviceCustomDate2", "DeviceCustomDate2Label": "custom.deviceCustomDate2Label", "FlexDate1": "custom.flexDate1", "FlexDate1Label": "custom.flexDate1Label", "FlexNumber1": "custom.flexNumber1", "FlexNumber1Label": "custom.flexNumber1Label", "FlexNumber2": "custom.flexNumber2", "FlexNumber2Label": "custom.flexNumber2Label", "IndicatorThreatType": "security.IndicatorThreatType", "ReportReferenceLink": "security.ReportReferenceLink", "OriginalLogSeverity": "security.OriginalLogSeverity", "RemoteIP": "security.RemoteIP", "RemotePort": "security.RemotePort", "SimplifiedDeviceAction": "security.SimplifiedDeviceAction", "DestinationUserPrivileges": "destination.dpriv", "LayerName": "raw_extensions.layer_name", "LayerUuid": "raw_extensions.layer_uuid", "MatchId": "raw_extensions.match_id", "ParentRule": "raw_extensions.parent_rule", "RuleAction": "raw_extensions.rule_action", "RuleUid": "raw_extensions.rule_uid", "ConnDirection": "raw_extensions.conn_direction", "InterfaceName": "raw_extensions.ifname", "LogId": "raw_extensions.logid", "Origin": "raw_extensions.origin", "SequenceNum": "raw_extensions.sequencenum", "Version": "raw_extensions.version", "InZone": "raw_extensions.inzone", "LastUpdateTime": "raw_extensions.lastupdatetime", "OutZone": "raw_extensions.outzone", "Packets": "raw_extensions.packets", "Product": "raw_extensions.product", "SegmentTime": "raw_extensions.segment_time", "ServiceId": "raw_extensions.service_id"}}