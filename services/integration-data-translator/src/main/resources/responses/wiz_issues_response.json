{"data": {"issues": {"nodes": [{"id": "93af2908-0d9d-4f5f-954b-7ee2abb4c6cd", "sourceRules": [{"__typename": "Control", "id": "wc-id-140", "name": "Publicly exposed VM/serverless with high privileges and initial access vulnerabilities", "controlDescription": "This VM is exposed to the public internet, has high privileges in the environment, and has a critical/high severity [initial access vulnerability](https://docs.wiz.io/wiz-docs/docs/vulnerability-findings?lng=en#initial-access). High permissions allow access to data or the ability to delete resources and disrupt workflows.\n\nPublicly exposed resources are more easily accessible for an attacker than internal ones. Moreover, highly privileged resources are a greater risk to the environment when compromised. Therefore, an attacker that manages to execute code on the publicly exposed VM, could abuse the resource's permissions, potentially performing highly privileged operations in the environment.", "resolutionRecommendation": "### Limit external exposure\n* Restrict access to resources that do not need to be accessible from the internet.\n* Ensure that exposed ports allow only encrypted communications.\n\n### Patch vulnerabilities\n* Update all software running in your environment to the latest version.\n   * For public images, ensure that you update to the latest version.\n   * For private images, check the Finding for detailed information regarding the vulnerability.\n   * Build a new image with the fixed vulnerability and replace it in the resource.\n* If you cannot use the latest version, prioritize patching resources in your environment according to the attack surface they are exposed to and the potential impact of this resource’s compromise (based on the Issue severity).\n\n### Protect highly privileged principals\n* Use the “least privilege” principle when assigning permissions, meaning that each account is assigned the exact permissions that it needs to function properly. When assigning permissions, avoid wild-card permissions.\n* To find unused permissions assigned to the principal, use the “Excessive Permissions” object on the Security Graph.\n* Minimize the attack surface of principals with sensitive permissions by remediating all Issues associated to them.\n* Avoid using access keys. They function as long-term credentials to the principal, and are more easily compromised.\n* Enable MFA for users with sensitive permissions if possible.", "securitySubCategories": [{"title": "164.308(a)(1)(ii)(B) Risk Management (R)", "category": {"name": "164.308 Administrative Safeguards", "framework": {"name": "HIPAA Security Rules"}}}, {"title": "Valid Accounts: <PERSON> Accounts", "category": {"name": "Privilege Escalation", "framework": {"name": "MITRE ATT&CK Cloud Matrix"}}}, {"title": "B2.c Identity and Access Control - Privileged User Management", "category": {"name": "B2 Identity and Access Control", "framework": {"name": "CAF (Cyber Assessment Framework by NCSC)"}}}, {"title": "Privileged resource", "category": {"name": "Identity Management", "framework": {"name": "Wiz for Risk Assessment"}}}, {"title": "OPS-22 Testing and Documentation of known Vulnerabilities", "category": {"name": "5.6 Operations (OPS)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "1.2 Prevent access to the administrative interface from\nthe internet", "category": {"name": "1 Firewalls", "framework": {"name": "UK Cyber Essentials"}}}, {"title": "AC-2(7) Account Management | Privileged User Accounts", "category": {"name": "AC Access Control", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "1.1 An automated method of asset discovery is used at least fortnightly to support the detection of assets for subsequent vulnerability scanning activities - (Maturity Level 1)", "category": {"name": "1 Patch applications", "framework": {"name": "Essential Eight"}}}, {"title": "1.3 A vulnerability scanner is used at least daily to identify missing patches or updates for security vulnerabilities in internet-facing services - (Maturity Level 1)", "category": {"name": "1 Patch applications", "framework": {"name": "Essential Eight"}}}, {"title": "PR.AA-01 Identities and credentials for authorized users, services, and hardware are managed by the organization", "category": {"name": "PR.AA Identity Management, Authentication, and Access Control", "framework": {"name": "NIST CSF v2.0"}}}, {"title": "IDM-06 Privileged access rights", "category": {"name": "5.7 Identity and Access Management (IDM)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "IAC-20.4 Dedicated Administrative Machines", "category": {"name": "16-IAC Identification & Authentication", "framework": {"name": "SCF (Secure Controls Framework)"}}}, {"title": "RA-3 Risk Assessment", "category": {"name": "RA Risk Assessment", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "SI-2 Flaw Remediation", "category": {"name": "SI System And Information Integrity", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "1 Physical devices and systems within the organization are inventoried", "category": {"name": "1 Asset Management (ID.AM)", "framework": {"name": "NIST CSF v1.1"}}}, {"title": "Exposure Management", "category": {"name": "Exposure Management", "framework": {"name": "W<PERSON> (Legacy)"}}}, {"title": "SA-11 Developer Testing and Evaluation", "category": {"name": "SA System And Services Acquisition", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "8.31 Separation of development, test and production environments", "category": {"name": "Technological controls", "framework": {"name": "ISO/IEC 27001-2022"}}}, {"title": "A.12.6.1 Management of technical vulnerabilities", "category": {"name": "A.12 Operations security", "framework": {"name": "ISO/IEC 27001"}}}, {"title": "Art. 32 Security of processing", "category": {"name": "Chapter 4 Controller and processor", "framework": {"name": "GDPR"}}}, {"title": "5 Threats, vulnerabilities, likelihoods, and impacts are used to determine risk", "category": {"name": "4 Risk Assessment (ID.RA)", "framework": {"name": "NIST CSF v1.1"}}}, {"title": "TA0004-T1078.004 Valid Accounts: Cloud Accounts", "category": {"name": "TA0004 Privilege Escalation", "framework": {"name": "MITRE ATT&CK Matrix"}}}, {"title": "T1203 Exploitation for Client Execution", "category": {"name": "TA0002 Execution", "framework": {"name": "MITRE ATT&CK Matrix"}}}, {"title": "3.4.2 Establish and enforce security configuration settings for information technology products employed in organizational systems.", "category": {"name": "3.4 Configuration Management", "framework": {"name": "NIST 800-171 Rev.2"}}}, {"title": "11.3.2 Perform internal penetration testing at least annually and after any significant infrastructure or application upgrade or modification (such as an operating system upgrade, a sub-network added to the environment, or a web server added to the environment).", "category": {"name": "11 regularly test security systems and processes", "framework": {"name": "PCI DSS v3.1.0"}}}, {"title": "8.2 Privileged access rights", "category": {"name": "Technological controls", "framework": {"name": "ISO/IEC 27001-2022"}}}, {"title": "11.3.1 Perform external penetration testing at least annually and after any significant infrastructure or application upgrade or modification (such as an operating system upgrade, a sub-network added to the environment, or a web server added to the environment).", "category": {"name": "11 regularly test security systems and processes", "framework": {"name": "PCI DSS v3.1.0"}}}, {"title": "01.e Review of User Access Rights", "category": {"name": "1.02 Authorized Access to Information Systems - Access Control", "framework": {"name": "HITRUST CSF v11.2"}}}, {"title": "PM-15 Security and Privacy Groups and Associations", "category": {"name": "PM Program Management", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "Vulnerability Assessment", "category": {"name": "Vulnerability Assessment", "framework": {"name": "W<PERSON> (Legacy)"}}}, {"title": "2.2 Develop configuration standards for all system components. Assure that these standards address all known security vulnerabilities and are consistent with industry- accepted system hardening standards", "category": {"name": "2 Do not use vendor-supplied defaults for system passwords and other security parameters", "framework": {"name": "PCI DSS v3.1.0"}}}, {"title": "PR.AA-05 Access permissions, entitlements, and authorizations are defined in a policy, managed, enforced, and reviewed, and incorporate the principles of least privilege and separation of duties", "category": {"name": "PR.AA Identity Management, Authentication, and Access Control", "framework": {"name": "NIST CSF v2.0"}}}, {"title": "9.4 As part of the ICT risk management framework referred to in Article 6(1), financial entities shall:\n\n(a) develop and document an information security policy defining rules to protect the availability, authenticity, integrity and confidentiality of data, information assets and ICT assets, including those of their customers, where applicable;\n\n(b) following a risk-based approach, establish a sound network and infrastructure management structure using appropriate techniques, methods and protocols that may include implementing automated mechanisms to isolate affected information assets in the event of cyber-attacks;\n\n(c) implement policies that limit the physical or logical access to information assets and ICT assets to what is required for legitimate and approved functions and activities only, and establish to that end a set of policies, procedures and controls that address access rights and ensure a sound administration thereof;\n\n(d) implement policies and protocols for strong authentication mechanisms, based on relevant standards and dedicated control systems, and protection measures of cryptographic keys whereby data is encrypted based on results of approved data classification and ICT risk assessment processes;\n\n(e) implement documented policies, procedures and controls for ICT change management, including changes to software, hardware, firmware components, systems or security parameters, that are based on a risk assessment approach and are an integral part of the financial entity’s overall change management process, in order to ensure that all changes to ICT systems are recorded, tested, assessed, approved, implemented and verified in a controlled manner;\n\n(f) have appropriate and comprehensive documented policies for patches and updates.\n\nFor the purposes of the first subparagraph, point (b), financial entities shall design the network connection infrastructure in a way that allows it to be instantaneously severed or segmented in order to minimise and prevent contagion, especially for interconnected financial processes.\n\nFor the purposes of the first subparagraph, point (e), the ICT change management process shall be approved by appropriate lines of management and shall have specific protocols in place.", "category": {"name": "Art 9 Protection and prevention - CHAPTER II - ICT risk management", "framework": {"name": "Digital Operational Resilience Act (DORA)"}}}, {"title": "500.07 As part of its cybersecurity program, based on the Covered Entity’s Risk Assessment each Covered Entity shall limit user access privileges to Information Systems that provide access to Nonpublic Information and shall periodically review such access privileges.", "category": {"name": "500.07 Access Privileges", "framework": {"name": "NYDFS (23 NYCRR 500)"}}}, {"title": "Resource with public exposure", "category": {"name": "Network & API Design", "framework": {"name": "Wiz for Risk Assessment"}}}, {"title": "5.1 Establish Secure Configurations", "category": {"name": "5 Secure Configuration for Hardware and Software on Mobile Devices, Laptops, Workstations and Servers", "framework": {"name": "CIS Controls v7.1"}}}, {"title": "3.5.4 Employ replay-resistant authentication mechanisms for network access to privileged and nonprivileged accounts.", "category": {"name": "3.5 Identification and Authentication", "framework": {"name": "NIST 800-171 Rev.2"}}}, {"title": "3.3 A vulnerability scanner is used at least daily to identify missing patches or updates for security vulnerabilities in operating systems of internet-facing services - (Maturity Level 1)", "category": {"name": "3 Patch operating systems", "framework": {"name": "Essential Eight"}}}, {"title": "OPS-20 Managing Vulnerabilities, Malfunctions and Errors - Measurements, Analyses and Assessments of Procedures", "category": {"name": "5.6 Operations (OPS)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "SEC08-BP04 Enforce access control", "category": {"name": "SEC 8 Data protection - How do you protect your data at rest?", "framework": {"name": "AWS Well-Architected Framework (Section 2 - Security)"}}}, {"title": "B4.d System security - Vulnerability Management", "category": {"name": "B4 System Security", "framework": {"name": "CAF (Cyber Assessment Framework by NCSC)"}}}, {"title": "AC-4(21) Information Flow Enforcement | Physical or Logical Separation of Information Flows", "category": {"name": "AC Access Control", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "TS-2.12 Cloud Service Provider & Cloud Service Consumer", "category": {"name": "TS-2 Technical Security - Network Security", "framework": {"name": "MPA (Motion Picture Association - Security Best Practices)"}}}, {"title": "SI-5 Security Alerts, Advisories, and Directives", "category": {"name": "SI System And Information Integrity", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "7.1 In order to address and manage ICT risk, financial entities shall use and maintain updated ICT systems, protocols and tools that are:\n(a) appropriate to the magnitude of operations supporting the conduct of their activities, in accordance with the proportionality principle as referred to in Article 4;\n(b) reliable;\n(c) equipped with sufficient capacity to accurately process the data necessary for the performance of activities and the timely provision of services, and to deal with peak orders, message or transaction volumes, as needed, including where new technology is introduced;\n(d) technologically resilient in order to adequately deal with additional information processing needs as required under stressed market conditions or other adverse situations.", "category": {"name": "Art 7 ICT systems, protocols and tools - CHAPTER II - ICT risk management", "framework": {"name": "Digital Operational Resilience Act (DORA)"}}}, {"title": "B2.a Identity and Access Control - Identity Verification, Authentication, and Authorization", "category": {"name": "B2 Identity and Access Control", "framework": {"name": "CAF (Cyber Assessment Framework by NCSC)"}}}, {"title": "1.9 A vulnerability scanner is used at least fortnightly to identify missing patches or updates for security vulnerabilities in other applications - (Maturity Level 2)", "category": {"name": "1 Patch applications", "framework": {"name": "Essential Eight"}}}, {"title": "1.2 A vulnerability scanner with an up-to-date vulnerability database is used for vulnerability scanning activities - (Maturity Level 1)", "category": {"name": "1 Patch applications", "framework": {"name": "Essential Eight"}}}, {"title": "Exploit Public-Facing Application", "category": {"name": "Initial Access", "framework": {"name": "MITRE ATT&CK Cloud Matrix"}}}, {"title": "3.11.2 Scan for vulnerabilities in organizational systems and applications periodically and when new vulnerabilities affecting those systems and applications are identified.", "category": {"name": "3.11 Risk Assessment", "framework": {"name": "NIST 800-171 Rev.2"}}}, {"title": "3.7 Utilize a Risk-rating Process", "category": {"name": "3 Continuous Vulnerability Management", "framework": {"name": "CIS Controls v7.1"}}}, {"title": "8.20 Networks security", "category": {"name": "Technological controls", "framework": {"name": "ISO/IEC 27001-2022"}}}, {"title": "Vulnerable resource with high privileges", "category": {"name": "Vulnerability Assessment", "framework": {"name": "Wiz for Risk Assessment"}}}, {"title": "3.12.3 Monitor security controls on an ongoing basis to ensure the continued effectiveness of the controls.", "category": {"name": "3.12 Security Assessment", "framework": {"name": "NIST 800-171 Rev.2"}}}, {"title": "CA-7 Continuous Monitoring", "category": {"name": "CA Assessment, Authorization, And Monitoring", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "RA-5 Vulnerability Monitoring and Scanning", "category": {"name": "RA Risk Assessment", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "10.m Control of Technical Vulnerabilities", "category": {"name": "10.06 Technical Vulnerability Management - Information Systems Acquisition, Development, and Maintenance", "framework": {"name": "HITRUST CSF v11.2"}}}, {"title": "COS-03 Monitoring of connections in the Cloud Service Provider's network", "category": {"name": "5.9 Communication Security (COS)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "A.8.1.3 Acceptable use of assets", "category": {"name": "A.8 Asset management", "framework": {"name": "ISO/IEC 27001"}}}, {"title": "IDM-05 Regular review of access rights", "category": {"name": "5.7 Identity and Access Management (IDM)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "OPS-19 Managing Vulnerabilities, Malfunctions and Errors - Penetration Tests", "category": {"name": "5.6 Operations (OPS)", "framework": {"name": "C5 - Cloud Computing Compliance Criteria Catalogue"}}}, {"title": "21.2.1 The measures to protect network and information systems shall include policies on risk analysis and information system security", "category": {"name": "Article 21 Cybersecurity risk-management measures", "framework": {"name": "NIS2 Directive (Article 21)"}}}, {"title": "20.2 Conduct Regular External and Internal Penetration Tests", "category": {"name": "20 Penetration Tests and Red Team Exercises", "framework": {"name": "CIS Controls v7.1"}}}, {"title": "CA-2 Control Assessments", "category": {"name": "CA Assessment, Authorization, And Monitoring", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "SI-4 System Monitoring", "category": {"name": "SI System And Information Integrity", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "VPM-06.9 Correlate Scanning Information", "category": {"name": "32-VPM  Vulnerability & Patch Management", "framework": {"name": "SCF (Secure Controls Framework)"}}}, {"title": "Identity Management", "category": {"name": "Identity Management", "framework": {"name": "W<PERSON> (Legacy)"}}}, {"title": "TS-2.0 Network Configuration", "category": {"name": "TS-2 Technical Security - Network Security", "framework": {"name": "MPA (Motion Picture Association - Security Best Practices)"}}}, {"title": "3.1 An automated method of asset discovery is used at least fortnightly to support the detection of assets for subsequent vulnerability scanning activities - (Maturity Level 1)", "category": {"name": "3 Patch operating systems", "framework": {"name": "Essential Eight"}}}, {"title": "3.4.1 Establish and maintain baseline configurations and inventories of organizational systems (including hardware, software, firmware, and documentation) throughout the respective system development life cycles.", "category": {"name": "3.4 Configuration Management", "framework": {"name": "NIST 800-171 Rev.2"}}}, {"title": "A.14.2.5 Secure system engineering principles", "category": {"name": "A.14 System acquisition, development and maintenance", "framework": {"name": "ISO/IEC 27001"}}}, {"title": "IAC-01 Identity & Access Management (IAM) ", "category": {"name": "16-IAC Identification & Authentication", "framework": {"name": "SCF (Secure Controls Framework)"}}}, {"title": "B2.d Identity and Access Control - Identity and Access Management (IdAM)", "category": {"name": "B2 Identity and Access Control", "framework": {"name": "CAF (Cyber Assessment Framework by NCSC)"}}}, {"title": "T1190 Exploit Public-Facing Application", "category": {"name": "TA0001 Initial Access", "framework": {"name": "MITRE ATT&CK Matrix"}}}, {"title": "SA-5 System Documentation", "category": {"name": "SA System And Services Acquisition", "framework": {"name": "NIST SP 800-53 Revision 5"}}}, {"title": "PR.IR-01 Networks and environments are protected from unauthorized logical access and usage", "category": {"name": "PR.IR Technology Infrastructure Resilience", "framework": {"name": "NIST CSF v2.0"}}}, {"title": "12 A vulnerability management plan is developed and implemented", "category": {"name": "10 Information Protection Processes and Procedures (PR.IP)", "framework": {"name": "NIST CSF v1.1"}}}, {"title": "3 Organizational communication and data flows are mapped", "category": {"name": "1 Asset Management (ID.AM)", "framework": {"name": "NIST CSF v1.1"}}}], "risks": ["EXTERNAL_EXPOSURE", "UNPROTECTED_PRINCIPAL", "VULNERABILITY"]}], "createdAt": "2025-03-18T00:56:45.96955Z", "updatedAt": "2025-05-14T20:47:46.466496Z", "dueAt": null, "type": "TOXIC_COMBINATION", "resolvedAt": "2025-05-14T20:47:46.466496Z", "statusChangedAt": "2025-05-14T20:47:46.466496Z", "projects": [{"id": "28a933d2-e304-52a2-a6a5-43f312b27b0b", "name": "Sales - AWS + Azure", "slug": "sales---aws-azure", "businessUnit": "", "riskProfile": {"businessImpact": "MBI"}}], "status": "RESOLVED", "severity": "CRITICAL", "entitySnapshot": {"id": "953c28b5-3d26-5e2d-b5ee-f79a812d3c87", "type": "COMPUTE_INSTANCE_GROUP", "nativeType": "autoScalingGroup", "name": "eks-hana-bank-lab-test-cluster-58c991d6-b047-1cd9-e43e-1e50b125c5e6", "status": "Active", "cloudPlatform": "AWS", "cloudProviderURL": "https://ap-southeast-2.console.aws.amazon.com/ec2/home?region=ap-southeast-2#AutoScalingGroupDetails:id=eks-hana-bank-lab-test-cluster-58c991d6-b047-1cd9-e43e-1e50b125c5e6;view=details", "providerId": "arn:aws:autoscaling:ap-southeast-2:************:autoScalingGroup:3600841e-fd47-427c-88d1-1edc80e4e1e3:autoScalingGroupName/eks-hana-bank-lab-test-cluster-58c991d6-b047-1cd9-e43e-1e50b125c5e6", "region": "ap-southeast-2", "resourceGroupExternalId": "", "subscriptionExternalId": "************", "subscriptionName": "aws-demo-sales", "subscriptionTags": {}, "tags": {"eks:cluster-name": "hana-bank-test-cluster", "eks:nodegroup-name": "hana-bank-lab-test-cluster", "k8s.io/cluster-autoscaler/enabled": "true", "k8s.io/cluster-autoscaler/hana-bank-test-cluster": "owned", "kubernetes.io/cluster/hana-bank-test-cluster": "owned"}, "createdAt": "2024-11-13T04:00:26Z", "externalId": "arn:aws:autoscaling:ap-southeast-2:************:autoScalingGroup:3600841e-fd47-427c-88d1-1edc80e4e1e3:autoScalingGroupName/eks-hana-bank-lab-test-cluster-58c991d6-b047-1cd9-e43e-1e50b125c5e6"}, "serviceTickets": null, "notes": []}], "pageInfo": {"hasNextPage": true, "endCursor": "eyJmaWVsZHMiOlt7IkZpZWxkIjoiU2V2ZXJpdHkiLCJWYWx1ZSI6NDAwfSx7IkZpZWxkIjoiQ3JlYXRlZEF0IiwiVmFsdWUiOiIyMDI1LTAzLTE4VDAwOjU2OjQ1Ljk2OTU1WiJ9LHsiRmllbGQiOiJJZCIsIlZhbHVlIjoiOTNhZjI5MDgtMGQ5ZC00ZjVmLTk1NGItN2VlMmFiYjRjNmNkIn1dfQ=="}}}}