{"data": {"vulnerabilityFindings": {"nodes": [{"id": "bddd4811-502d-5ecb-8441-5ba34c728d05", "portalUrl": "https://app.wiz.io/explorer/vulnerability-findings#~(entity~(~'bddd4811-502d-5ecb-8441-5ba34c728d05*2cSECURITY_TOOL_FINDING))", "name": "CVE-2022-1304", "CVEDescription": "An out-of-bounds read/write vulnerability was found in e2fsprogs 1.46.5. This issue leads to a segmentation fault and possibly arbitrary code execution via a specially crafted filesystem.", "CVSSSeverity": "HIGH", "score": 7.8, "exploitabilityScore": 1.8, "severity": "MEDIUM", "nvdSeverity": "HIGH", "weightedSeverity": null, "impactScore": 5.9, "dataSourceName": null, "hasExploit": false, "hasCisaKevExploit": false, "status": "OPEN", "vendorSeverity": "MEDIUM", "firstDetectedAt": "2023-05-10T18:07:07.210127Z", "lastDetectedAt": "2025-05-26T11:38:48Z", "resolvedAt": null, "description": "The package `libss` version `1.45.6-1.el8` was detected in `YUM/DNF package manager` on a machine running `RedHat 8.4` is vulnerable to `CVE-2022-1304`, which exists in versions `< 0:1.45.6-5.el8`.\n\nThe vulnerability was found in the [Official RedHat Security Advisories](https://access.redhat.com/errata/RHSA-2022:7720) with vendor severity: `Medium` ([NVD](https://nvd.nist.gov/vuln/detail/CVE-2022-1304) severity: `High`).\n\nThe vulnerability can be remediated by updating the package to version `1.45.6-5.el8` or higher, using `yum update libss`.", "remediation": "yum update libss", "detailedName": "libss", "version": "1.45.6-1.el8", "fixedVersion": "1.45.6-5.el8", "detectionMethod": "PACKAGE", "link": "https://access.redhat.com/errata/RHSA-2022:7720", "locationPath": null, "resolutionReason": null, "epssSeverity": "HIGH", "epssPercentile": 69.3, "epssProbability": 0.6, "validatedInRuntime": null, "layerMetadata": null, "projects": [{"id": "28a933d2-e304-52a2-a6a5-43f312b27b0b", "name": "Sales - AWS + Azure", "slug": "sales---aws-azure", "businessUnit": "", "riskProfile": {"businessImpact": "MBI"}}], "ignoreRules": null, "cvssv2": {"attackVector": "NETWORK", "attackComplexity": "MEDIUM", "confidentialityImpact": "PARTIAL", "integrityImpact": "PARTIAL", "privilegesRequired": "NONE", "userInteractionRequired": true}, "cvssv3": {"attackVector": "LOCAL", "attackComplexity": "LOW", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "privilegesRequired": "NONE", "userInteractionRequired": true}, "relatedIssueAnalytics": {"issueCount": 0, "criticalSeverityCount": 0, "highSeverityCount": 0, "mediumSeverityCount": 0, "lowSeverityCount": 0, "informationalSeverityCount": 0}, "cnaScore": 7.8, "vulnerableAsset": {"id": "7ced02e6-16e6-5f38-802f-a4e8e17ce076", "type": "VIRTUAL_MACHINE", "name": "saki-RH8-ven-lab02", "region": "us-east-1", "providerUniqueId": "arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f", "cloudProviderURL": "https://us-east-1.console.aws.amazon.com/ec2/v2/home?region=us-east-1#InstanceDetails:instanceId=i-089e095a7a5de1a4f", "cloudPlatform": "AWS", "status": "Inactive", "subscriptionName": "aws-demo-sales", "subscriptionExternalId": "931505774614", "subscriptionId": "7906e4be-0628-5e0d-bd25-c224370be1e4", "tags": {"Email": "<EMAIL>", "Name": "saki-RH8-ven-lab02"}, "hasLimitedInternetExposure": false, "hasWideInternetExposure": false, "isAccessibleFromVPN": null, "isAccessibleFromOtherVnets": null, "isAccessibleFromOtherSubscriptions": null, "operatingSystem": "Linux", "ipAddresses": ["************"], "imageName": "RHEL-8.4.0_HVM-20210504-arm64-2-Hourly2-GP2", "nativeType": "virtualMachine", "computeInstanceGroup": null}}, {"id": "02e80a77-9c64-5891-9319-f901757f5542", "portalUrl": "https://app.wiz.io/explorer/vulnerability-findings#~(entity~(~'02e80a77-9c64-5891-9319-f901757f5542*2cSECURITY_TOOL_FINDING))", "name": "CVE-2021-3445", "CVEDescription": "A flaw was found in libdnf's signature verification functionality in versions before 0.60.1. This flaw allows an attacker to achieve code execution if they can alter the header information of an RPM package and then trick a user or system into installing it. The highest risk of this vulnerability is to confidentiality, integrity, as well as system availability.", "CVSSSeverity": "HIGH", "score": 7.5, "exploitabilityScore": 1.6, "severity": "MEDIUM", "nvdSeverity": "HIGH", "weightedSeverity": null, "impactScore": 5.9, "dataSourceName": null, "hasExploit": false, "hasCisaKevExploit": false, "status": "OPEN", "vendorSeverity": "MEDIUM", "firstDetectedAt": "2023-05-10T18:07:07.263232Z", "lastDetectedAt": "2025-05-26T11:38:49Z", "resolvedAt": null, "description": "The package `yum-utils` version `4.0.18-4.el8` was detected in `YUM/DNF package manager` on a machine running `RedHat 8.4` is vulnerable to `CVE-2021-3445`, which exists in versions `< 0:4.0.21-3.el8`.\n\nThe vulnerability was found in the [Official RedHat Security Advisories](https://access.redhat.com/errata/RHSA-2021:4464) with vendor severity: `Medium` ([NVD](https://nvd.nist.gov/vuln/detail/CVE-2021-3445) severity: `High`).\n\nThe vulnerability can be remediated by updating the package to version `4.0.21-3.el8` or higher, using `yum update yum-utils`.", "remediation": "yum update yum-utils", "detailedName": "yum-utils", "version": "4.0.18-4.el8", "fixedVersion": "4.0.21-3.el8", "detectionMethod": "PACKAGE", "link": "https://access.redhat.com/errata/RHSA-2021:4464", "locationPath": null, "resolutionReason": null, "epssSeverity": "LOW", "epssPercentile": 15.3, "epssProbability": 0, "validatedInRuntime": null, "layerMetadata": null, "projects": [{"id": "28a933d2-e304-52a2-a6a5-43f312b27b0b", "name": "Sales - AWS + Azure", "slug": "sales---aws-azure", "businessUnit": "", "riskProfile": {"businessImpact": "MBI"}}], "ignoreRules": null, "cvssv2": {"attackVector": "NETWORK", "attackComplexity": "HIGH", "confidentialityImpact": "PARTIAL", "integrityImpact": "PARTIAL", "privilegesRequired": "NONE", "userInteractionRequired": true}, "cvssv3": {"attackVector": "NETWORK", "attackComplexity": "HIGH", "confidentialityImpact": "HIGH", "integrityImpact": "HIGH", "privilegesRequired": "NONE", "userInteractionRequired": true}, "relatedIssueAnalytics": {"issueCount": 0, "criticalSeverityCount": 0, "highSeverityCount": 0, "mediumSeverityCount": 0, "lowSeverityCount": 0, "informationalSeverityCount": 0}, "cnaScore": 0, "vulnerableAsset": {"id": "7ced02e6-16e6-5f38-802f-a4e8e17ce076", "type": "VIRTUAL_MACHINE", "name": "saki-RH8-ven-lab02", "region": "us-east-1", "providerUniqueId": "arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f", "cloudProviderURL": "https://us-east-1.console.aws.amazon.com/ec2/v2/home?region=us-east-1#InstanceDetails:instanceId=i-089e095a7a5de1a4f", "cloudPlatform": "AWS", "status": "Inactive", "subscriptionName": "aws-demo-sales", "subscriptionExternalId": "931505774614", "subscriptionId": "7906e4be-0628-5e0d-bd25-c224370be1e4", "tags": {"Email": "<EMAIL>", "Name": "saki-RH8-ven-lab02"}, "hasLimitedInternetExposure": false, "hasWideInternetExposure": false, "isAccessibleFromVPN": null, "isAccessibleFromOtherVnets": null, "isAccessibleFromOtherSubscriptions": null, "operatingSystem": "Linux", "ipAddresses": ["************"], "imageName": "RHEL-8.4.0_HVM-20210504-arm64-2-Hourly2-GP2", "nativeType": "virtualMachine", "computeInstanceGroup": null}}, {"id": "39972103-45d1-5022-8ff3-930411ea8d01", "portalUrl": "https://app.wiz.io/explorer/vulnerability-findings#~(entity~(~'39972103-45d1-5022-8ff3-930411ea8d01*2cSECURITY_TOOL_FINDING))", "name": "CVE-2022-32189", "CVEDescription": "A too-short encoded message can cause a panic in Float.GobDecode and Rat GobDecode in math/big in Go before 1.17.13 and 1.18.5, potentially allowing a denial of service.", "CVSSSeverity": "HIGH", "score": 7.5, "exploitabilityScore": 3.9, "severity": "LOW", "nvdSeverity": "HIGH", "weightedSeverity": null, "impactScore": 3.6, "dataSourceName": null, "hasExploit": false, "hasCisaKevExploit": false, "status": "OPEN", "vendorSeverity": "LOW", "firstDetectedAt": "2023-05-10T18:07:07.312288Z", "lastDetectedAt": "2025-05-26T11:39:01Z", "resolvedAt": null, "description": "The package `rsyslog` version `8.1911.0-7.el8` was detected in `YUM/DNF package manager` on a machine running `RedHat 8.4` is vulnerable to `CVE-2022-32189`, which exists in `all current versions`.\n\nThe vulnerability was found in the [Official RedHat Security Advisories](https://access.redhat.com/security/cve/CVE-2022-32189) with vendor severity: `Low` ([NVD](https://nvd.nist.gov/vuln/detail/CVE-2022-32189) severity: `High`).\n\nThis vulnerability cannot be remediated because a fix has not been released.\n\nThe package is associated with the technology `rsyslog`.", "remediation": null, "detailedName": "rsyslog", "version": "8.1911.0-7.el8", "fixedVersion": null, "detectionMethod": "PACKAGE", "link": "https://access.redhat.com/security/cve/CVE-2022-32189", "locationPath": null, "resolutionReason": null, "epssSeverity": "LOW", "epssPercentile": 27.4, "epssProbability": 0.1, "validatedInRuntime": null, "layerMetadata": null, "projects": [{"id": "28a933d2-e304-52a2-a6a5-43f312b27b0b", "name": "Sales - AWS + Azure", "slug": "sales---aws-azure", "businessUnit": "", "riskProfile": {"businessImpact": "MBI"}}], "ignoreRules": null, "cvssv2": {"attackVector": null, "attackComplexity": null, "confidentialityImpact": null, "integrityImpact": null, "privilegesRequired": null, "userInteractionRequired": false}, "cvssv3": {"attackVector": "NETWORK", "attackComplexity": "LOW", "confidentialityImpact": "NONE", "integrityImpact": "NONE", "privilegesRequired": "NONE", "userInteractionRequired": false}, "relatedIssueAnalytics": {"issueCount": 0, "criticalSeverityCount": 0, "highSeverityCount": 0, "mediumSeverityCount": 0, "lowSeverityCount": 0, "informationalSeverityCount": 0}, "cnaScore": 0, "vulnerableAsset": {"id": "7ced02e6-16e6-5f38-802f-a4e8e17ce076", "type": "VIRTUAL_MACHINE", "name": "saki-RH8-ven-lab02", "region": "us-east-1", "providerUniqueId": "arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f", "cloudProviderURL": "https://us-east-1.console.aws.amazon.com/ec2/v2/home?region=us-east-1#InstanceDetails:instanceId=i-089e095a7a5de1a4f", "cloudPlatform": "AWS", "status": "Inactive", "subscriptionName": "aws-demo-sales", "subscriptionExternalId": "931505774614", "subscriptionId": "7906e4be-0628-5e0d-bd25-c224370be1e4", "tags": {"Email": "<EMAIL>", "Name": "saki-RH8-ven-lab02"}, "hasLimitedInternetExposure": false, "hasWideInternetExposure": false, "isAccessibleFromVPN": null, "isAccessibleFromOtherVnets": null, "isAccessibleFromOtherSubscriptions": null, "operatingSystem": "Linux", "ipAddresses": ["************"], "imageName": "RHEL-8.4.0_HVM-20210504-arm64-2-Hourly2-GP2", "nativeType": "virtualMachine", "computeInstanceGroup": null}}], "pageInfo": {"hasNextPage": true, "endCursor": "eyJmaWVsZHMiOlt7IkZpZWxkIjoiZmluZGluZ19jcmVhdGVkQXQiLCJWYWx1ZSI6IjIwMjMtMDUtMTBUMTg6MDc6MDcuMzEyMjg4WiJ9LHsiRmllbGQiOiJFeGNsdWRlIiwiVmFsdWUiOlsiMzk5NzIxMDMtNDVkMS01MDIyLThmZjMtOTMwNDExZWE4ZDAxIl19XX0="}}}}