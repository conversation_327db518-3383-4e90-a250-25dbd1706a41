{"data": {"count": 10, "next": 10, "prev": null, "results": [{"accessSwitch": "PALO_ALTO-IDF04-SW01:Gig1/0/44 Enterprise", "boundaries": "Corporate", "businessImpact": "Unassigned", "category": "Computers", "customProperties": {}, "dataSources": [{"firstSeen": "2025-02-10T10:04:55.976756+00:00", "instances": [{"firstSeen": "2025-02-10T10:04:55.976756+00:00", "lastSeen": "2025-05-30T17:07:00.007065+00:00", "name": "ClicoIntegration"}], "lastSeen": "2025-05-30T17:07:00.007065+00:00", "name": "Active Directory", "types": ["Asset & System Management", "Identity Provider"]}, {"firstSeen": "2025-05-20T08:50:59.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:13.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-02-15T09:48:22.700500+00:00", "instances": [{"firstSeen": "2024-02-15T09:48:22.700500+00:00", "lastSeen": "2025-05-30T23:12:44.017065+00:00", "name": "Test-FGT"}], "lastSeen": "2025-05-30T23:12:44.017065+00:00", "name": "CrowdStrike", "types": ["Agent Based", "Endpoint Protection"]}, {"firstSeen": "2025-05-18T16:05:46.999065+00:00", "instances": [], "lastSeen": "2025-05-30T23:41:08.999065+00:00", "name": "Palo Alto Networks GlobalProtect", "types": ["Firewall", "NAC"]}, {"firstSeen": "2025-01-11T15:06:10.999065+00:00", "instances": [], "lastSeen": "2025-05-31T01:53:04.999065+00:00", "name": "Qualys", "types": ["Vulnerability Management"]}, {"firstSeen": "2025-01-08T10:01:14.999065+00:00", "instances": [], "lastSeen": "2025-05-30T22:13:15.999065+00:00", "name": "SCCM", "types": ["Asset & System Management", "Patch Management"]}, {"firstSeen": "2025-01-21T08:47:15.999065+00:00", "instances": [], "lastSeen": "2025-01-21T08:50:59.999065+00:00", "name": "ServiceNow", "types": ["Asset & System Management"]}, {"firstSeen": "2025-05-30T16:50:59.013065+00:00", "instances": [], "lastSeen": "2025-05-30T19:12:44.012065+00:00", "name": "SPAN/TAP", "types": ["Switch"]}, {"firstSeen": "2025-05-23T17:00:00.360310+00:00", "instances": [], "lastSeen": "2025-05-31T00:33:58.999065+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}, {}, {"firstSeen": "2024-12-13T09:21:21.495058+00:00", "instances": [], "lastSeen": "2025-05-24T03:10:18.495058+00:00", "name": "User", "types": ["Data Upload"]}], "displayTitle": "000000731194pc.corporate.acme.com", "firstSeen": "2025-05-23T16:59:58.360310+00:00", "id": 1176, "ipAddress": "************", "ipv6": ["fe80::647b:ba0f:9628:6014"], "lastSeen": "2025-05-31T20:53:13.999065+00:00", "macAddress": "50:76:AF:D3:3F:AB", "manufacturer": "Lenovo", "model": "ThinkPad X1 Yoga 3rd Gen", "name": "000000731194pc.corporate.acme.com", "names": "000000731194pc.corporate.acme.com,000000731194pc", "operatingSystem": "Windows", "operatingSystemVersion": "10", "protections": [], "purdueLevel": 4.0, "riskLevel": 80, "sensor": {"name": "PALO_ALTO-IDF04-SW01:Gig1/0/44 Enterprise", "type": "Access Switch"}, "site": {"location": "No location", "name": null}, "tags": [], "tier": "No tier", "type": "Laptops", "typeEnum": "LAPTOP", "userIds": [12], "visibility": "Full"}, {"accessSwitch": "Factory 1", "boundaries": "ICS", "businessImpact": "Unassigned", "category": "Manufacturing Equipment", "customProperties": {}, "dataSources": [{"firstSeen": "2025-03-24T05:37:44.933732+00:00", "instances": [{"firstSeen": "2025-03-24T05:37:44.933732+00:00", "lastSeen": "2025-03-26T11:31:27.488804+00:00", "name": "ClicoIntegration"}], "lastSeen": "2025-03-26T11:31:27.488804+00:00", "name": "Active Directory", "types": ["Asset & System Management", "Identity Provider"]}, {"firstSeen": "2025-05-31T07:36:56.106800+00:00", "instances": [], "lastSeen": "2025-05-31T17:45:01.208136+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2024-07-27T00:49:27.753807+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:12.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-08-21T04:29:39.219257+00:00", "instances": [], "lastSeen": "2025-05-30T13:57:31.337921+00:00", "name": "Nvidia Bluefield", "types": ["Switch"]}, {"firstSeen": "2025-03-22T09:22:48.933732+00:00", "instances": [], "lastSeen": "2025-05-28T10:00:01.933732+00:00", "name": "SCCM", "types": ["Asset & System Management", "Patch Management"]}, {"firstSeen": "2024-09-29T04:48:06.109303+00:00", "instances": [], "lastSeen": "2025-05-28T07:08:47.933732+00:00", "name": "Tanium Interact", "types": ["Agent Based", "Asset & System Management"]}, {"firstSeen": "2025-05-23T18:00:00.039276+00:00", "instances": [], "lastSeen": "2025-05-30T18:00:00.115775+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-09-16T06:22:06.191947+00:00", "instances": [{"firstSeen": "2024-09-16T06:22:06.191947+00:00", "lastSeen": "2025-05-30T15:40:13.933732+00:00", "name": "Armis-SNOW integration"}], "lastSeen": "2025-05-30T15:40:13.933732+00:00", "name": "Cisco WLC", "types": ["WLC"]}], "displayTitle": "engineering workstation", "firstSeen": "2024-07-27T00:49:27.753807+00:00", "id": 2217, "ipAddress": "************", "ipv6": ["fe80::1932:f0d3:8fd9:4e4e"], "lastSeen": "2025-05-31T20:53:12.999065+00:00", "macAddress": "26:D1:3E:86:A3:11", "manufacturer": "Lenovo", "model": "ThinkPad P52", "name": "engineering workstation", "names": "engineering workstation", "operatingSystem": "Windows", "operatingSystemVersion": "10.0", "protections": [], "purdueLevel": 2.0, "riskLevel": 100, "sensor": {"name": "Factory 1", "type": "Switch"}, "site": {"location": "Austin, TX", "name": "TX ICS Center"}, "tags": ["airgap:protected"], "tier": "No tier", "type": "Engineering Workstations", "typeEnum": "ENGINEERING_WORKSTATION", "userIds": [], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Guest", "businessImpact": "Unassigned", "category": "Multimedia", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-15T19:50:38.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:11.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}], "displayTitle": "my echo", "firstSeen": "2025-05-15T19:50:38.999065+00:00", "id": 2925, "ipAddress": "**************", "ipv6": [], "lastSeen": "2025-05-31T20:53:11.999065+00:00", "macAddress": "53:AA:92:9C:3C:39, FD:65:75:21:EB:B6", "manufacturer": "Amazon", "model": "Echo", "name": "my echo", "names": "my echo", "operatingSystem": null, "operatingSystemVersion": null, "protections": [], "purdueLevel": null, "riskLevel": 60, "sensor": {"name": "Medical 2", "type": "Wireless LAN Controller"}, "site": {"location": "San Jose, CA", "name": "CA Health Center"}, "tags": ["Insecure Traffic and Behavior", "External to Internal Traffic", "Deprecated SW/HW", "Misconfigurations", "Unprotected Sensitive Data", "Insecure Credentials and Access Control"], "tier": "No tier", "type": "Virtual Assistants", "typeEnum": "VIRTUAL_ASSISTANT", "userIds": [], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Medical, Corporate", "businessImpact": "Unassigned", "category": "Medical", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-31T06:08:26.314505+00:00", "instances": [], "lastSeen": "2025-05-31T17:53:32.803772+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2025-05-16T18:38:20.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:10.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2025-05-16T18:38:22.999065+00:00", "instances": [], "lastSeen": "2025-05-16T18:38:22.999065+00:00", "name": "<PERSON><PERSON><PERSON><PERSON>", "types": ["Asset & System Management"]}, {"firstSeen": "2025-05-16T21:15:55.999065+00:00", "instances": [], "lastSeen": "2025-05-30T01:15:55.999065+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}], "displayTitle": "orthoscan mini c-arm", "firstSeen": "2025-05-16T18:38:20.999065+00:00", "id": 2238, "ipAddress": "************", "ipv6": [], "lastSeen": "2025-05-31T20:53:10.999065+00:00", "macAddress": "52:CA:9C:16:80:12", "manufacturer": "OrthoScan Inc.", "model": "Mini C-Arm", "name": "orthoscan mini c-arm", "names": "orthoscan mini c-arm", "operatingSystem": "Windows", "operatingSystemVersion": "XP", "protections": [], "purdueLevel": 4.0, "riskLevel": 100, "sensor": {"name": "Medical 1", "type": "Wireless LAN Controller"}, "site": {"location": "New York", "name": "ACME Hospital"}, "tags": ["airgap:protected"], "tier": "No tier", "type": "X-Rays", "typeEnum": "X_RAY", "userIds": [6], "visibility": "Full"}, {"accessSwitch": "Factory 1", "boundaries": "ICS", "businessImpact": "Unassigned", "category": "Manufacturing Equipment", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-31T07:54:01.084140+00:00", "instances": [], "lastSeen": "2025-05-31T18:53:46.374397+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2024-02-27T06:15:10.864407+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:09.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-02-27T07:49:41.528933+00:00", "instances": [], "lastSeen": "2025-05-30T14:05:50.954582+00:00", "name": "Engineering Workstation - Rockwell", "types": ["Manufacturing"]}, {"firstSeen": "2024-08-24T00:54:14.933732+00:00", "instances": [], "lastSeen": "2024-08-30T03:45:53.933732+00:00", "name": "Network Mapper", "types": ["Switch"]}, {"firstSeen": "2024-08-02T12:31:09.933732+00:00", "instances": [], "lastSeen": "2025-05-29T14:08:52.641238+00:00", "name": "Nvidia Bluefield", "types": ["Switch"]}, {"firstSeen": "2025-05-23T17:00:00.116846+00:00", "instances": [], "lastSeen": "2025-05-30T11:00:00.970660+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}], "displayTitle": "plc 1756-l73/b", "firstSeen": "2024-02-27T06:15:10.864407+00:00", "id": 2220, "ipAddress": "*************", "ipv6": [], "lastSeen": "2025-05-31T20:53:09.999065+00:00", "macAddress": "AA:20:A5:49:D5:71", "manufacturer": "Rockwell Automation", "model": "1756-L61S/B", "name": "plc 1756-l73/b", "names": "plc 1756-l73/b", "operatingSystem": null, "operatingSystemVersion": "17.007", "protections": [], "purdueLevel": 1.0, "riskLevel": 90, "sensor": {"name": "Factory 1", "type": "Switch"}, "site": {"location": "Austin, TX", "name": "TX ICS Center"}, "tags": ["airgap:protected"], "tier": "No tier", "type": "PLCs", "typeEnum": "PLC", "userIds": [], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Diagnostic Imaging", "businessImpact": "Unassigned", "category": "Medical", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-31T06:09:06.723822+00:00", "instances": [], "lastSeen": "2025-05-31T15:13:15.604101+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2025-05-04T00:53:02.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:08.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2025-04-21T22:32:18.188648+00:00", "instances": [], "lastSeen": "2025-04-21T22:32:18.188648+00:00", "name": "SentinelOne", "types": ["Agent Based", "Endpoint Protection"]}, {"firstSeen": "2025-05-24T03:00:00.748419+00:00", "instances": [], "lastSeen": "2025-05-30T13:00:00.551798+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}], "displayTitle": "somatom-ct006", "firstSeen": "2025-05-04T00:53:02.999065+00:00", "id": 2742, "ipAddress": "************, ************", "ipv6": ["fe80::7d21:ce54:fd9a:eb13"], "lastSeen": "2025-05-31T20:53:08.999065+00:00", "macAddress": "E6:45:E8:18:98:1A, 07:F8:84:31:9B:E2", "manufacturer": "Siemens Healthcare", "model": "SOMATOM Definition AS", "name": "somatom-ct006", "names": "somatom-ct006", "operatingSystem": "Windows", "operatingSystemVersion": "7", "protections": [{"creationTime": "Mon, 21 Apr 2025 22:32:18 GMT", "deviceId": 2742, "lastSeenTime": "Mon, 21 Apr 2025 22:32:18 GMT", "protectionName": "SentinelOne"}], "purdueLevel": null, "riskLevel": 72, "sensor": {"name": "Medical 1", "type": "Wireless LAN Controller"}, "site": {"location": "New York", "name": "ACME Hospital"}, "tags": ["Insecure Traffic and Behavior", "Unprotected Sensitive Data", "External to Internal Traffic", "Misconfigurations", "Deprecated SW/HW", "Insecure Credentials and Access Control", "airgap:protected"], "tier": "No tier", "type": "CTs", "typeEnum": "CT", "userIds": [], "visibility": "Full"}, {"accessSwitch": "Factory 1", "boundaries": "ICS", "businessImpact": "Unassigned", "category": "Manufacturing Equipment", "customProperties": {}, "dataSources": [{"firstSeen": "2024-03-19T01:46:16.616552+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:07.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-03-19T01:46:38.933732+00:00", "instances": [], "lastSeen": "2025-05-30T22:41:59.225826+00:00", "name": "Nvidia Bluefield", "types": ["Switch"]}, {"firstSeen": "2025-03-22T14:57:55.933732+00:00", "instances": [], "lastSeen": "2025-05-29T15:47:19.933732+00:00", "name": "SCCM", "types": ["Asset & System Management", "Patch Management"]}, {"firstSeen": "2024-09-25T06:40:02.126266+00:00", "instances": [], "lastSeen": "2025-05-30T22:29:00.933732+00:00", "name": "Tanium Interact", "types": ["Agent Based", "Asset & System Management"]}, {"firstSeen": "2025-05-23T17:00:00.120384+00:00", "instances": [], "lastSeen": "2025-05-30T22:41:59.225826+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}, {}, {"firstSeen": "2025-03-11T02:51:38.311855+00:00", "instances": [], "lastSeen": "2025-05-30T22:00:59.195720+00:00", "name": "vSphere vCenter", "types": ["Asset & System Management", "Virtualization"]}], "displayTitle": "scada server", "firstSeen": "2024-03-19T01:46:16.616552+00:00", "id": 2232, "ipAddress": "************", "ipv6": [], "lastSeen": "2025-05-31T20:53:07.999065+00:00", "macAddress": "2D:8C:3B:94:28:22", "manufacturer": "VMware", "model": "VMware", "name": "scada server", "names": "scada server", "operatingSystem": "Windows", "operatingSystemVersion": "10.0", "protections": [], "purdueLevel": 2.0, "riskLevel": 10, "sensor": {"name": "Factory 1", "type": "Switch"}, "site": {"location": "No location", "name": null}, "tags": [], "tier": "No tier", "type": "SCADA Servers", "typeEnum": "SCADA_SERVER", "userIds": [5], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Nursing Units", "businessImpact": "Unassigned", "category": "Medical", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-23T00:52:03.588023+00:00", "instances": [], "lastSeen": "2025-05-31T01:53:04.999065+00:00", "name": "<PERSON><PERSON>", "types": ["Asset & System Management"]}, {"firstSeen": "2025-05-31T05:11:54.334233+00:00", "instances": [], "lastSeen": "2025-05-31T15:06:35.006572+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2025-05-16T17:38:22.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:06.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2025-05-23T00:52:03.588023+00:00", "instances": [], "lastSeen": "2025-05-31T01:53:04.999065+00:00", "name": "Microsoft DHCP", "types": ["DHCP Service Provider", "DNS Service Provider"]}, {"firstSeen": "2025-05-16T18:38:22.999065+00:00", "instances": [], "lastSeen": "2025-05-16T18:38:22.999065+00:00", "name": "<PERSON><PERSON><PERSON><PERSON>", "types": ["Asset & System Management"]}, {"firstSeen": "2025-05-23T14:00:00.624254+00:00", "instances": [], "lastSeen": "2025-05-30T08:00:00.374727+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-08-28T21:34:04.406023+00:00", "instances": [{"firstSeen": "2024-08-28T21:34:04.406023+00:00", "lastSeen": "2025-05-31T01:53:04.999065+00:00", "name": "Armis-SNOW integration"}], "lastSeen": "2025-05-31T01:53:04.999065+00:00", "name": "Cisco WLC", "types": ["WLC"]}], "displayTitle": "w02405w12", "firstSeen": "2025-05-16T18:38:20.999065+00:00", "id": 2270, "ipAddress": "*************", "ipv6": [], "lastSeen": "2025-05-31T20:53:06.999065+00:00", "macAddress": "AC:64:A5:65:45:60", "manufacturer": "BD CareFusion", "model": "Alaris PCU 8015", "name": "w02405w12", "names": "w02405w12", "operatingSystem": "Enea OSE", "operatingSystemVersion": "2.6", "protections": [], "purdueLevel": 4.0, "riskLevel": 100, "sensor": {"name": "Medical 1", "type": "Wireless LAN Controller"}, "site": {"location": "New York", "name": "ACME Hospital"}, "tags": ["airgap:protected"], "tier": "No tier", "type": "Infusion Pumps", "typeEnum": "INFUSION_PUMP", "userIds": [], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Corporate", "businessImpact": "Unassigned", "category": "Imaging", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-15T15:34:01.736065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:05.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2022-12-27T23:05:13.905065+00:00", "instances": [], "lastSeen": "2022-12-28T16:48:40.905065+00:00", "name": "SPAN/TAP", "types": ["Switch"]}, {"firstSeen": "2023-05-17T16:48:40.905065+00:00", "instances": [], "lastSeen": "2025-05-28T18:34:28.472065+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2024-12-13T09:21:21.495058+00:00", "instances": [], "lastSeen": "2025-05-24T03:10:18.495058+00:00", "name": "User", "types": ["Data Upload"]}], "displayTitle": "axis-accc8ec36cf9", "firstSeen": "2025-05-15T15:34:01.736065+00:00", "id": 1177, "ipAddress": "*************", "ipv6": [], "lastSeen": "2025-05-31T20:53:05.999065+00:00", "macAddress": "17:1C:1B:9B:D2:AA", "manufacturer": "Axis Communications", "model": "M3045-V Network Camera", "name": "axis-accc8ec36cf9", "names": "axis-accc8ec36cf9", "operatingSystem": "Linux", "operatingSystemVersion": "4.4.27", "protections": [], "purdueLevel": 4.0, "riskLevel": 100, "sensor": {"name": "WLC1 Enterprise", "type": "Wireless LAN Controller"}, "site": {"location": "No location", "name": null}, "tags": [], "tier": "No tier", "type": "IP Cameras", "typeEnum": "IP_CAMERA", "userIds": [], "visibility": "Full"}, {"accessSwitch": null, "boundaries": "Corporate", "businessImpact": "Unassigned", "category": "Imaging", "customProperties": {}, "dataSources": [{"firstSeen": "2025-05-31T05:08:44.155076+00:00", "instances": [], "lastSeen": "2025-05-31T16:14:26.231253+00:00", "name": "API", "types": ["Data Upload"]}, {"firstSeen": "2025-05-12T14:07:21.999065+00:00", "instances": [], "lastSeen": "2025-05-31T20:53:04.999065+00:00", "name": "Knowledge Base", "types": ["Traffic Inspection", "Data Analysis"]}, {"firstSeen": "2025-04-20T15:38:53.643564+00:00", "instances": [], "lastSeen": "2025-05-30T19:48:58.815808+00:00", "name": "Network Mapper", "types": ["Switch"]}, {"firstSeen": "2025-04-21T22:32:18.188648+00:00", "instances": [], "lastSeen": "2025-04-21T22:32:18.188648+00:00", "name": "SentinelOne", "types": ["Agent Based", "Endpoint Protection"]}, {"firstSeen": "2025-05-23T15:00:00.809391+00:00", "instances": [], "lastSeen": "2025-05-30T11:00:00.080923+00:00", "name": "Traffic Inspection", "types": ["Traffic Inspection", "Data Analysis"]}], "displayTitle": "3469968f_7fb", "firstSeen": "2025-05-12T14:07:21.999065+00:00", "id": 3214, "ipAddress": "***********, ***********", "ipv6": [], "lastSeen": "2025-05-31T20:53:04.999065+00:00", "macAddress": "90:36:97:5A:BF:40, C6:A2:EB:12:BC:EC", "manufacturer": "Axis Communications", "model": "Q6055-E PTZ Dome Network Camera", "name": "3469968f_7fb", "names": "3469968f_7fb", "operatingSystem": "Linux", "operatingSystemVersion": "4.9.197", "protections": [{"creationTime": "Mon, 21 Apr 2025 22:32:18 GMT", "deviceId": 3214, "lastSeenTime": "Mon, 21 Apr 2025 22:32:18 GMT", "protectionName": "SentinelOne"}], "purdueLevel": null, "riskLevel": 80, "sensor": {"name": "Medical 1", "type": "Wireless LAN Controller"}, "site": {"location": "New York", "name": "ACME Hospital"}, "tags": ["Insecure Traffic and Behavior", "Unprotected Sensitive Data", "Critical Vulnerabilities", "Misconfigurations", "Deprecated SW/HW", "Insecure Credentials and Access Control", "airgap:protected"], "tier": "No tier", "type": "IP Cameras", "typeEnum": "IP_CAMERA", "userIds": [], "visibility": "Full"}], "total": 3669}, "success": true}