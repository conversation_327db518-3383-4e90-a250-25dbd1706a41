logging:
  level:
    ROOT: INFO
server:
  port: 8080
spring:
  application:
    name: integration-data-translator
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none


integration-data-translator:
  kafka-consumer-config:
    bootstrapServers: <server>
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT_";
    isConnectionString: true
    topic: json-input-queue
  kafka-producer-config:
    bootstrapServers: <server>
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true
    topic: mapped-data-queue
