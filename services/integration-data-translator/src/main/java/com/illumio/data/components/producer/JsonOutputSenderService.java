package com.illumio.data.components.producer;


import com.illumio.data.components.configuration.IntegrationDataTranslatorConfig;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class JsonOutputSenderService {

    private final KafkaSender<String, String> kafkaSender;
    private final IntegrationDataTranslatorConfig integrationDataTranslatorConfig;

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendMappedData(String mappedData, String header) {
        return kafkaSender.send(Mono.just( senderRecord(mappedData, header)))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> senderRecord(final String mappedData, final String header) {
        final String topic = integrationDataTranslatorConfig.getKafkaProducerConfig().getTopic();
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, mappedData);
        return SenderRecord.create(producerRecord, null);
    }

}
