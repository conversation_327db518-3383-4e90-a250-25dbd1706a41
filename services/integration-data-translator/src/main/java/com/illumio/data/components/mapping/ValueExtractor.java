package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.strategy.ExtractionStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.util.List;


@Slf4j
@Component
@RequiredArgsConstructor
public class ValueExtractor {

    private final List<ExtractionStrategy> extractionStrategies;

    public Mono<Object> extractValue(JsonNode rootNode, String jsonPath, int arrayIndex) {
        return extractionStrategies.stream()
                                   .filter(strategy -> strategy.supports(jsonPath))
                                   .findFirst()
                                   .map(strategy -> strategy.extract(rootNode, jsonPath, arrayIndex))
                                   .orElse(Mono.empty());
    }
}