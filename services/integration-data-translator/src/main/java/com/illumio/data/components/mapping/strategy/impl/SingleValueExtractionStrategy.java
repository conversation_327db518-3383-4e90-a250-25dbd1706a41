package com.illumio.data.components.mapping.strategy.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.JsonNodeNavigator;
import com.illumio.data.components.mapping.JsonPathResolver;
import com.illumio.data.components.mapping.JsonValueConverter;
import com.illumio.data.components.mapping.strategy.ExtractionStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.illumio.data.components.util.Constants.COLLECT_MARKER;
import static com.illumio.data.components.util.Constants.OBJECT_MARKER;

@Component
@RequiredArgsConstructor
public class SingleValueExtractionStrategy implements ExtractionStrategy {

    private final JsonPathResolver pathResolver;
    private final JsonNodeNavigator navigator;
    private final JsonValueConverter converter;

    @Override
    public boolean supports(String jsonPath) {
        return !jsonPath.contains(COLLECT_MARKER) && !jsonPath.contains(OBJECT_MARKER);
    }

    @Override
    public Mono<Object> extract(JsonNode rootNode, String jsonPath, int arrayIndex) {
        String actualPath = pathResolver.resolveArrayPath(jsonPath, arrayIndex);

        return navigator.navigateToNode(rootNode, actualPath)
                        .flatMap(converter::convertJsonNodeToValue)
                        .switchIfEmpty(Mono.empty());
    }
}
