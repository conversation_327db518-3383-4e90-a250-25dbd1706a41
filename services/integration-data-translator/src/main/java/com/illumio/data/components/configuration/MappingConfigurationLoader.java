package com.illumio.data.components.configuration;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.exception.MappingConfigurationException;
import com.illumio.data.components.model.MappingConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class MappingConfigurationLoader {

    private final ObjectMapper objectMapper;

    @Value("${integration-data-translator.mapping.rules-path:/config/mapping-rules}")
    private String mappingRulesPath;

    public Mono<MappingConfiguration> loadMappingConfig(String dataType) {
        return Mono.fromCallable(() -> {
                       validateDataType(dataType);
                       String fileName = resolveFileName(dataType);
                       return loadMappingsFromFile(fileName, dataType);
                   });
    }

    private void validateDataType(String dataType) {
        if (dataType == null || dataType.trim().isEmpty()) {
            throw new MappingConfigurationException(
                    "Data type is required but was not provided in the message header");
        }
    }

    private String resolveFileName(String dataType) {

        String fileName = dataType.concat(".json");
        Path filePath = Paths.get(mappingRulesPath, fileName);

        if (!Files.exists(filePath)) {
            throw new MappingConfigurationException(
                    String.format("Configuration file not found for data type '%s'. Expected file: %s",
                            dataType, filePath));
        }

        return fileName;
    }

    private MappingConfiguration loadMappingsFromFile(String fileName, String dataType) {
        try {
            Path filePath = Paths.get(mappingRulesPath, fileName);
            String content = Files.readString(filePath);
            JsonNode rootNode = objectMapper.readTree(content);

            JsonNode fieldMappingsNode = rootNode.get("fieldMappings");
            if (fieldMappingsNode == null) {
                throw new MappingConfigurationException(
                        String.format("Missing 'fieldMappings' in configuration file for data type '%s': %s",
                                dataType, fileName));
            }

            String name = rootNode.path("name").asText(dataType);
            String description = rootNode.path("description").asText("");
            Map<String, String> fieldMappings = objectMapper.convertValue(fieldMappingsNode,
                    new TypeReference<Map<String, String>>() {});

            return MappingConfiguration.builder()
                                       .name(name)
                                       .description(description)
                                       .fieldMappings(fieldMappings)
                                       .build();

        } catch (MappingConfigurationException e) {
            throw e;
        } catch (Exception e) {
            throw new MappingConfigurationException(
                    String.format("Error reading configuration file for data type '%s': %s",
                            dataType, fileName), e);
        }
    }
}
