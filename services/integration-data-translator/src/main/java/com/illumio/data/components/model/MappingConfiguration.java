package com.illumio.data.components.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
@JsonDeserialize(builder = MappingConfiguration.MappingConfigurationBuilder.class)
public class MappingConfiguration {
    String name;
    String description;
    Map<String, String> fieldMappings;

    @JsonPOJOBuilder(withPrefix = "")
    public static class MappingConfigurationBuilder {
        // Lombok will generate the builder methods
    }
}