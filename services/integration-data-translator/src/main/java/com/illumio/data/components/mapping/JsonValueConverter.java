package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import java.util.List;
import java.util.stream.StreamSupport;

@Slf4j
@Component
@RequiredArgsConstructor
public class JsonValueConverter {

    private final ObjectMapper objectMapper;

    public Mono<Object> convertJsonNodeToValue(JsonNode node) {
        return Mono.fromCallable(() -> convertNode(node));
    }

    private Object convertNode(JsonNode node) {
        if (node == null || node.isNull()) {
            return null;
        }

        return switch (node.getNodeType()) {
            case BOOLEAN -> node.asBoolean();
            case NUMBER -> getNumberValue(node);
            case STRING -> node.asText();
            case ARRAY -> convertArrayNode(node);
            case OBJECT -> convertObjectNode(node);
            default -> node.toString();
        };
    }

    private Object getNumberValue(JsonNode node) {
        if (node.isInt()) {
            return node.asInt();
        } else if (node.isLong()) {
            return node.asLong();
        } else {
            return node.asDouble();
        }
    }

    private List<Object> convertArrayNode(JsonNode arrayNode) {
        return StreamSupport.stream(arrayNode.spliterator(), false)
                            .map(this::convertNode)
                            .toList();
    }


    @SneakyThrows
    private Object convertObjectNode(JsonNode objectNode) {
            return objectMapper.convertValue(objectNode, Object.class);
    }
}