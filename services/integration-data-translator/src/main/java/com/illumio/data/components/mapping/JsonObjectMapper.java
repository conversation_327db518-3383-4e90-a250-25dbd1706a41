package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.illumio.data.components.model.MappingConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class JsonObjectMapper {

    private final ValueExtractor valueExtractor;
    private final ObjectMapper objectMapper;

    public Mono<String> mapSingleObject(JsonNode rootNode, MappingConfiguration config, Integer arrayIndex) {
        return Flux.fromIterable(config.getFieldMappings().entrySet())
                   .flatMap(mapping -> mapField(rootNode, mapping, arrayIndex))
                   .collectMap(Map.Entry::getKey, Map.Entry::getValue, LinkedHashMap::new)
                   .flatMap(this::convertToJsonString)
                   .onErrorResume(e -> {
                       log.error("Failed to map object for index {}", arrayIndex, e);
                       return Mono.empty();
                   });
    }

    private Mono<Map.Entry<String, Object>> mapField(JsonNode rootNode,
                                                     Map.Entry<String, String> mapping,
                                                     Integer arrayIndex) {
        String fieldName = mapping.getKey();
        String jsonPath = mapping.getValue();

    return valueExtractor
        .extractValue(rootNode, jsonPath, arrayIndex)
        .filter(Objects::nonNull)
        .map(value -> Map.entry(fieldName, value));
    }

    private Mono<String> convertToJsonString(Map<String, Object> mappedObject) {
        return Mono.fromCallable(() -> objectMapper.writeValueAsString(mappedObject));
    }
}
