package com.illumio.data.components.mapping.strategy.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.mapping.JsonNodeNavigator;
import com.illumio.data.components.mapping.JsonPathResolver;
import com.illumio.data.components.mapping.JsonValueConverter;
import com.illumio.data.components.mapping.strategy.ExtractionStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collections;

import static com.illumio.data.components.util.Constants.COLLECT_MARKER;

@Slf4j
@Component
@RequiredArgsConstructor
public class CollectionExtractionStrategy implements ExtractionStrategy {

    private final JsonPathResolver pathResolver;
    private final JsonNodeNavigator navigator;
    private final JsonValueConverter converter;

    @Override
    public boolean supports(String jsonPath) {
        return jsonPath.contains(COLLECT_MARKER);
    }

    @Override
    public Mono<Object> extract(JsonNode rootNode, String jsonPath, int arrayIndex) {
        String basePath = pathResolver.removeMarker(jsonPath, COLLECT_MARKER);
        String actualPath = pathResolver.resolveArrayPath(basePath, arrayIndex);

        return navigator.navigateToNode(rootNode, actualPath)
                        .flatMap(arrayNode -> {
                            if (arrayNode != null && arrayNode.isArray()) {
                                return Flux.fromIterable(arrayNode)
                                           .flatMap(converter::convertJsonNodeToValue)
                                           .collectList()
                                           .map(Object.class::cast);
                            }
                            return Mono.just((Object) Collections.emptyList());
                        })
                        .defaultIfEmpty(Collections.emptyList());
    }
}
