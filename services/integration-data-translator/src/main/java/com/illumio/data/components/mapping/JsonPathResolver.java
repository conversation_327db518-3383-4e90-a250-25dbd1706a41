package com.illumio.data.components.mapping;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.illumio.data.components.util.Constants.*;

@Slf4j
@Component
public class JsonPathResolver {

    public String resolveArrayPath(String path, int arrayIndex) {
        if (arrayIndex >= 0 && path.contains(ARRAY_WILDCARD)) {
            return path.replaceFirst(ARRAY_WILDCARD_REGEX, ARRAY_OPEN_BRACKET + arrayIndex + ARRAY_CLOSE_BRACKET);
        } else if (arrayIndex == -1) {
            return path.replaceAll(ARRAY_WILDCARD_REGEX, EMPTY_STRING);
        }
        return path;
    }

    public boolean hasArrayWildcard(String path) {
        return path.contains(ARRAY_WILDCARD);
    }

    public String extractArrayBasePath(String path) {
        if (!hasArrayWildcard(path)) {
            return path;
        }
        return path.substring(0, path.indexOf(ARRAY_WILDCARD));
    }

    public String removeMarker(String path, String marker) {
        return path.replace(marker, "");
    }
}
