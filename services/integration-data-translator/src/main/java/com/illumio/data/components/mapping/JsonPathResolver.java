package com.illumio.data.components.mapping;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JsonPathResolver {

    public String resolveArrayPath(String path, int arrayIndex) {
        if (arrayIndex >= 0 && path.contains("[*]")) {
            return path.replaceFirst("\\[\\*\\]", "[" + arrayIndex + "]");
        } else if (arrayIndex == -1) {
            return path.replaceAll("\\[\\*\\]", "");
        }
        return path;
    }

    public boolean hasArrayWildcard(String path) {
        return path.contains("[*]");
    }

    public String extractArrayBasePath(String path) {
        if (!hasArrayWildcard(path)) {
            return path;
        }
        return path.substring(0, path.indexOf("[*]"));
    }

    public String removeMarker(String path, String marker) {
        return path.replace(marker, "");
    }
}
