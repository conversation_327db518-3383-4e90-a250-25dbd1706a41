package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.model.MappingConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
@RequiredArgsConstructor
public class ArrayIndexFinder {

    private final JsonPathResolver pathResolver;
    private final JsonNodeNavigator navigator;

    public Mono<TreeSet<Integer>> findArrayIndices(JsonNode rootNode, MappingConfiguration config) {
        return Flux.fromIterable(config.getFieldMappings().values())
                   .filter(pathResolver::hasArrayWildcard)
                   .take(1)
                   .flatMap(jsonPath -> findIndicesForPath(rootNode, jsonPath))
                   .single();
    }

    private Mono<TreeSet<Integer>> findIndicesForPath(JsonNode rootNode, String jsonPath) {
        String arrayPath = pathResolver.extractArrayBasePath(jsonPath);

        return navigator.navigateToNode(rootNode, arrayPath)
                        .map(arrayNode -> {
                            if (arrayNode != null && arrayNode.isArray()) {
                                return IntStream.range(0, arrayNode.size())
                                                .boxed()
                                                .collect(Collectors.toCollection(TreeSet::new));
                            }
                            return new TreeSet<Integer>();
                        })
                        .doOnNext(indices -> log.debug("Found array indices: {} for path: {}", indices, arrayPath))
                        .defaultIfEmpty(new TreeSet<>());
    }
}
