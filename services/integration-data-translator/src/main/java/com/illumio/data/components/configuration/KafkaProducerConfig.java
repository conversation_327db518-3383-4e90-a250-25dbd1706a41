package com.illumio.data.components.configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RequiredArgsConstructor
@Configuration
@Slf4j
public class KafkaProducerConfig {

    private final IntegrationDataTranslatorConfig integrationDataTranslatorConfig;

    @Bean
    public SenderOptions<String, String> kafkaSenderOptions() {
        Map<String, Object> producerProps = producerOptions();

        return SenderOptions.<String, String>create(producerProps)
                            // Non-blocking back-pressure
                            .maxInFlight(1024);
    }

    @Bean
    public KafkaSender<String, String> reactiveKafkaSender(
            SenderOptions<String, String> senderOptions) {
        return KafkaSender.create(senderOptions);
    }

    private Map<String, Object> producerOptions() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                integrationDataTranslatorConfig.getKafkaProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (Optional.of(integrationDataTranslatorConfig)
                    .map(IntegrationDataTranslatorConfig::getKafkaProducerConfig)
                    .map(IntegrationDataTranslatorConfig.KafkaProducerConfig::getIsConnectionString)
                    .orElse(Boolean.FALSE)) {
            log.debug("Using SASL security protocol to authenticate with Kafka");
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    integrationDataTranslatorConfig.getKafkaProducerConfig().getSaslJaasConfig());
        }
        return producerProps;
    }
}
