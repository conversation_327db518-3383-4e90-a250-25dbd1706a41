package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.components.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Arrays;

@Slf4j
@Component
public class JsonNodeNavigator {

    public Mono<JsonNode> navigateToNode(JsonNode rootNode, String path) {
        return Mono.fromCallable(() -> navigate(rootNode, path))
                   .onErrorResume(e -> Mono.empty());
    }

    private JsonNode navigate(JsonNode rootNode, String path) {
        return Arrays.stream(path.split(Constants.PATH_SEPARATOR_REGEX))
                     .reduce(rootNode, this::navigateToPathPart, (current, next) -> next);
    }

    private JsonNode navigateToPathPart(JsonNode current, String part) {
        if (current == null || current.isNull()) {
            return null;
        }
        return part.contains(Constants.ARRAY_OPEN_BRACKET) && part.contains(Constants.ARRAY_CLOSE_BRACKET)
            ? navigateToArrayElement(current, part)
            : current.get(part);
    }

    private JsonNode navigateToArrayElement(JsonNode current, String part) {
        String fieldName = part.substring(0, part.indexOf(Constants.ARRAY_OPEN_BRACKET));
        String indexStr = part.substring(part.indexOf(Constants.ARRAY_OPEN_BRACKET) + 1, part.indexOf(Constants.ARRAY_CLOSE_BRACKET));

        JsonNode arrayNode = current.get(fieldName);
        if (arrayNode != null && arrayNode.isArray()) {
            try {
                int index = Integer.parseInt(indexStr);
                return arrayNode.get(index);
            } catch (NumberFormatException e) {
                log.debug(Constants.INVALID_ARRAY_INDEX_LOG_MSG, indexStr);
                return null;
            }
        }
        return null;
    }
}