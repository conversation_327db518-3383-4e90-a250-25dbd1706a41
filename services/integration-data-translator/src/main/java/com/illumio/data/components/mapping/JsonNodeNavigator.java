package com.illumio.data.components.mapping;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Arrays;

@Slf4j
@Component
public class JsonNodeNavigator {

    public Mono<JsonNode> navigateToNode(JsonNode rootNode, String path) {
        return Mono.fromCallable(() -> navigate(rootNode, path))
                   .onErrorResume(e -> Mono.empty());
    }

    private JsonNode navigate(JsonNode rootNode, String path) {
        return Arrays.stream(path.split("\\."))
                     .reduce(rootNode, this::navigateToPathPart, (current, next) -> next);
    }

    private JsonNode navigateToPathPart(JsonNode current, String part) {
        if (current == null || current.isNull()) {
            return null;
        }
        return part.contains("[") && part.contains("]") ? navigateToArrayElement(current, part) : current.get(part);
    }

    private JsonNode navigateToArrayElement(JsonNode current, String part) {
        String fieldName = part.substring(0, part.indexOf("["));
        String indexStr = part.substring(part.indexOf("[") + 1, part.indexOf("]"));

        JsonNode arrayNode = current.get(fieldName);
        if (arrayNode != null && arrayNode.isArray()) {
            try {
                int index = Integer.parseInt(indexStr);
                return arrayNode.get(index);
            } catch (NumberFormatException e) {
                log.debug("Invalid array index: {}", indexStr);
                return null;
            }
        }
        return null;
    }
}