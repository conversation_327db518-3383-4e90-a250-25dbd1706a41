package com.illumio.data.components.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "integration-data-translator")
@Getter
@Setter
public class IntegrationDataTranslatorConfig {

    private final KafkaProducerConfig kafkaProducerConfig = new KafkaProducerConfig();
    private final KafkaConsumerConfig kafkaConsumerConfig = new KafkaConsumerConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
    }


}
