# Integration Data Translator

A Spring Boot reactive service that provides configurable JSON data transformation capabilities for security integrations. The service transforms raw API responses from various security platforms (Wiz, Armis, etc.) into standardized formats using configuration-driven mapping rules.

## 🚀 Overview

The Integration Data Translator is a core component of the Illumio security data platform that:

- **Transforms JSON data** from various security integrations using configurable mapping rules
- **Supports complex data structures** including arrays, nested objects, and collections
- **Provides reactive processing** using Spring WebFlux for high-performance data transformation
- **Enables config-driven mapping** without code changes for new data sources
- **Handles multiple data types** including vulnerabilities, devices, issues, sites, and boundaries

## 🏗️ Architecture

### Core Components

```
IntegrationDataTranslatorOrchestrator
├── MappingConfigurationLoader    # Loads JSON mapping configurations
├── JsonObjectMapper             # Maps JSON objects using configurations
├── ArrayIndexFinder            # Finds array indices for wildcard paths
├── ValueExtractor              # Extracts values using strategy pattern
│   ├── SingleValueExtractionStrategy    # Extracts single values
│   ├── ObjectExtractionStrategy         # Extracts complete objects [@object]
│   └── CollectionExtractionStrategy     # Extracts collections [@collect]
├── JsonNodeNavigator           # Navigates JSON structures with dot notation
├── JsonPathResolver            # Resolves JSON paths and wildcards
└── JsonValueConverter          # Converts JsonNode to Java objects
```

### Data Flow

```
Raw JSON Input → Configuration Loading → Path Resolution → Value Extraction → Object Mapping → Transformed Output
```

## 📋 Features

### 🔧 Configuration-Driven Mapping

- **JSON-based mapping rules** stored in `src/main/resources/mapping/`
- **Dynamic field mapping** without code changes
- **Support for nested structures** and complex transformations
- **Wildcard array processing** with `[*]` notation

### 🎯 Advanced Path Resolution

- **Dot notation navigation**: `data.vulnerabilityFindings.nodes[*].name`
- **Array wildcard support**: `[*]` for processing all array elements
- **Collection extraction**: `[@collect]` marker for gathering array elements
- **Object extraction**: `[@object]` marker for extracting complete objects

### ⚡ Reactive Processing

- **Spring WebFlux** for non-blocking I/O
- **Reactor streams** for efficient data processing
- **Backpressure handling** for large datasets
- **Error resilience** with graceful degradation

## 🗂️ Project Structure

```
src/
├── main/java/com/illumio/data/
│   ├── IntegrationDataTranslatorApplication.java
│   └── components/
│       ├── configuration/
│       │   └── MappingConfigurationLoader.java
│       ├── mapping/
│       │   ├── IntegrationDataTranslatorOrchestrator.java
│       │   ├── JsonObjectMapper.java
│       │   ├── ArrayIndexFinder.java
│       │   ├── ValueExtractor.java
│       │   ├── JsonNodeNavigator.java
│       │   ├── JsonPathResolver.java
│       │   ├── JsonValueConverter.java
│       │   └── strategy/
│       │       └── impl/
│       │           ├── SingleValueExtractionStrategy.java
│       │           ├── ObjectExtractionStrategy.java
│       │           └── CollectionExtractionStrategy.java
│       ├── model/
│       │   └── MappingConfiguration.java
│       ├── producer/
│       │   └── JsonOutputSenderService.java
│       └── exception/
│           └── MappingConfigurationException.java
├── main/resources/
│   └── mapping/
│       ├── vulnerability-data.json
│       ├── device-data.json
│       ├── issue-data.json
│       ├── site-data.json
│       └── boundary-data.json
└── test/
    ├── java/com/illumio/data/components/mapping/
    │   ├── IntegrationDataTranslatorOrchestratorTest.java
    │   ├── JsonObjectMapperTest.java
    │   ├── ArrayIndexFinderTest.java
    │   ├── ValueExtractorTest.java
    │   ├── JsonNodeNavigatorTest.java
    │   ├── JsonPathResolverTest.java
    │   ├── JsonValueConverterTest.java
    │   ├── MappingComponentsTestSuite.java
    │   └── strategy/impl/
    │       ├── SingleValueExtractionStrategyTest.java
    │       ├── ObjectExtractionStrategyTest.java
    │       └── CollectionExtractionStrategyTest.java
    └── resources/
        ├── mapping/           # Test mapping configurations
        └── responses/
            ├── api/          # Sample API responses
            └── mapped/       # Expected transformation results
```

## 📝 Configuration Format

### Mapping Configuration Structure

```json
{
  "name": "VulnerabilityDataMapping",
  "description": "Mapping configuration for vulnerability meta data",
  "fieldMappings": {
    "cveId": "data.vulnerabilityFindings.nodes[*].name",
    "description": "data.vulnerabilityFindings.nodes[*].CVEDescription",
    "severity": "data.vulnerabilityFindings.nodes[*].severity",
    "cvssScore": "data.vulnerabilityFindings.nodes[*].score",
    "workloadId": "data.vulnerabilityFindings.nodes[*].vulnerableAsset.providerUniqueId",
    "tags": "data.vulnerabilityFindings.nodes[*].tags[@collect]",
    "assetDetails": "data.vulnerabilityFindings.nodes[*].vulnerableAsset[@object]"
  }
}
```

### Supported Path Patterns

| Pattern | Description | Example |
|---------|-------------|---------|
| `field.subfield` | Simple dot notation | `data.name` |
| `array[*].field` | Array wildcard | `data.items[*].id` |
| `field[@collect]` | Collection extraction | `data.tags[@collect]` |
| `field[@object]` | Object extraction | `data.metadata[@object]` |

## 🚀 Getting Started

### Prerequisites

- Java 17+
- Gradle 8.0+
- Spring Boot 3.x

### Building the Service

```bash
# Build the service
./gradlew :services:integration-data-translator:build

# Run tests
./gradlew :services:integration-data-translator:test

# Run specific test suite
./gradlew :services:integration-data-translator:test --tests "MappingComponentsTestSuite"
```

### Running the Service

```bash
# Start the application
./gradlew :services:integration-data-translator:bootRun

# Or run the JAR
java -jar build/libs/integration-data-translator.jar
```

## 🔧 Usage Examples

### Basic Transformation

```java
// Load configuration
MappingConfiguration config = configLoader.loadMappingConfig("vulnerability-data");

// Transform JSON data
Flux<String> results = orchestrator.mapJsonData(jsonInput, "vulnerability-data");
```

### Custom Mapping Configuration

```json
{
  "name": "CustomMapping",
  "description": "Custom data transformation",
  "fieldMappings": {
    "id": "data.id",
    "name": "data.displayName",
    "tags": "data.labels[@collect]",
    "metadata": "data.properties[@object]"
  }
}
```

## 🧪 Testing

### Test Coverage

The service includes comprehensive test coverage:

- **Unit Tests**: All core components with 95%+ coverage
- **Integration Tests**: End-to-end mapping validation
- **Parameterized Tests**: All mapping configurations tested
- **Strategy Tests**: All extraction strategies validated

### Running Tests

```bash
# Run all tests
./gradlew test

# Run mapping component tests only
./gradlew test --tests "com.illumio.data.components.mapping.*"

# Run with coverage report
./gradlew test jacocoTestReport
```

### Test Data

- **Sample API Responses**: `src/test/resources/responses/api/`
- **Expected Results**: `src/test/resources/responses/mapped/`
- **Test Configurations**: `src/test/resources/mapping/`

## 📊 Supported Data Types

| Data Type | Source | Configuration File |
|-----------|--------|-------------------|
| Vulnerabilities | Wiz Security | `vulnerability-data.json` |
| Devices | Armis | `device-data.json` |
| Security Issues | Wiz Security | `issue-data.json` |
| Network Sites | Armis | `site-data.json` |
| Network Boundaries | Armis | `boundary-data.json` |

## 🔍 Monitoring & Observability

### Logging

- **Structured logging** with correlation IDs
- **Error tracking** with detailed stack traces
- **Performance metrics** for transformation operations

### Health Checks

- **Configuration validation** on startup
- **Mapping file integrity** checks
- **Reactive stream health** monitoring

## 🤝 Contributing

### Adding New Data Types

1. Create mapping configuration in `src/main/resources/mapping/`
2. Add test data in `src/test/resources/responses/`
3. Create expected results in `src/test/resources/responses/mapped/`
4. Update `TestResourceLoader.getAllMappingFileNames()`

### Development Guidelines

- Follow reactive programming patterns
- Maintain comprehensive test coverage
- Use configuration-driven approach
- Document complex transformations

## 📚 Dependencies

### Core Dependencies

- **Spring Boot 3.x**: Application framework
- **Spring WebFlux**: Reactive web framework
- **Project Reactor**: Reactive streams
- **Jackson**: JSON processing
- **Lombok**: Code generation

### Test Dependencies

- **JUnit 5**: Testing framework
- **Mockito**: Mocking framework
- **AssertJ**: Fluent assertions
- **Reactor Test**: Reactive testing utilities

## 📄 License

This project is part of the Illumio security data platform.

---

