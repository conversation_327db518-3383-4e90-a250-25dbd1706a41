package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.configuration.FindingsPersistenceConfiguration;
import com.illumio.data.model.*;
import java.util.*;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.test.StepVerifier;

import static com.illumio.data.util.FindingsPersistenceConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InventoryLabelSenderServiceTest {

    @Mock private KafkaSender<String, String> kafkaSender;
    @Mock private SenderResult<Object> senderResult;;
    @Mock private FindingsPersistenceConfiguration findingsPersistenceConfig;
    @Mock private FindingsPersistenceConfiguration.KafkaProducerConfig kafkaProducerConfig;
    @Mock private FindingsPersistenceConfiguration.InventoryLabelConfig inventoryLabelConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private InventoryLabelSenderService inventoryLabelSenderService;

    private final Vulnerability quarantineVulnerability =
            Vulnerability.builder()
                         .status(OPEN)
                         .metadata(VulnerabilityMetadata.builder()
                                                        .severity(CRITICAL)
                                                        .hasExploit(true)
                                                        .build())
                         .resource(FindingResource.builder()
                                                  .cloudProviderUniqueId("arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f")
                                                  .subscriptionId("fd72c36d-cdc1-4fe9-9c31-13f3e670b601")
                                                  .build())
                         .build();

    private final Vulnerability nonQuarantineVulnerability1 =
            quarantineVulnerability.toBuilder()
                                   .metadata(quarantineVulnerability.getMetadata().toBuilder()
                                                                    .severity("medium")
                                                                    .build())
                                   .build();

    private final Vulnerability nonQuarantineVulnerability2 =
            quarantineVulnerability.toBuilder()
                                   .metadata(quarantineVulnerability.getMetadata().toBuilder()
                                                                    .hasExploit(false)
                                                                    .build())
                                   .build();

    private final Vulnerability nonQuarantineVulnerability3 =
            quarantineVulnerability.toBuilder()
                                   .status("closed")
                                   .build();

    private final Issue nonQuarantineIssue =
            Issue.builder()
                 .status(OPEN)
                 .metadata(IssueMetadata.builder()
                                        .severity(CRITICAL)
                                        .build())
                 .resource(FindingResource.builder()
                                          .cloudProviderUniqueId("arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f")
                                          .subscriptionId("fd72c36d-cdc1-4fe9-9c31-13f3e670b601")
                                          .build())
                 .build();

    private final static String QUARANTINE_LABEL_ID = "18577348462923448";
    private final static String CS_TENANT_ID1 = "83f1b531-864b-4c02-9124-9de1645017c1";
    private final static String CS_TENANT_ID2 = "83f1b531-864b-4c02-9124-9de1645017c2";

    @BeforeEach
    public void setUp() {
        inventoryLabelSenderService =
                new InventoryLabelSenderService(kafkaSender, findingsPersistenceConfig, objectMapper);
    }

    @Test
    public void saveInventoryLabels_QuarantineVulnerability_Success() {
        setUpQuarantineTest(true);

        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(quarantineVulnerability))
                    .verifyComplete();

        ArgumentCaptor<Flux<SenderRecord<String, String, String>>> fluxCaptor = ArgumentCaptor.forClass(Flux.class);
        verify(kafkaSender).send(fluxCaptor.capture());

        StepVerifier.create(fluxCaptor.getValue().collectList())
                    .assertNext(senderRecords -> {
                        SenderRecord<String, String, String> record0 = senderRecords.get(0);
                        Header[] record0Headers = record0.headers().toArray();
                        assertEquals(DATAPLANE_ID, record0Headers[0].key());
                        assertEquals(DATA_PLANE, new String(record0Headers[0].value()));
                        assertEquals(TENANT_ID, record0Headers[1].key());
                        assertEquals(CS_TENANT_ID1, new String(record0Headers[1].value()));
                        assertEquals(CS_TENANT_ID1 + "d93735e3-48ec-5bac-8ea5-c535ff713051", record0.key());
                        assertTrue(isEqualIgnoringTimestamp("{\"timestamp\":\"2025-04-09T22:04:23.939449-07:00\",\"resources\":[{\"attributes\":{\"labels\":{\"set\":[\"18577348462923448\"],\"delete\":[]},\"external_data_reference\":\"d93735e3-48ec-5bac-8ea5-c535ff713051\"},\"resource_type\":\"workload\",\"resource_change_type\":\"update\"}]}", record0.value()));

                        SenderRecord<String, String, String> record1 = senderRecords.get(1);
                        Header[] record1Headers = record1.headers().toArray();
                        assertEquals(DATAPLANE_ID, record1Headers[0].key());
                        assertEquals(DATA_PLANE, new String(record1Headers[0].value()));
                        assertEquals(TENANT_ID, record1Headers[1].key());
                        assertEquals(CS_TENANT_ID2, new String(record1Headers[1].value()));
                        assertEquals(CS_TENANT_ID2 + "8c72e44b-7805-59ab-ae0a-7603aa4bba2e", record1.key());
                        assertTrue(isEqualIgnoringTimestamp("{\"timestamp\":\"2025-04-09T22:05:51.090706-07:00\",\"resources\":[{\"attributes\":{\"labels\":{\"set\":[\"18577348462923448\"],\"delete\":[]},\"external_data_reference\":\"8c72e44b-7805-59ab-ae0a-7603aa4bba2e\"},\"resource_type\":\"workload\",\"resource_change_type\":\"update\"}]}", record1.value()));
                    })
                    .verifyComplete();
    }

    @Test
    public void saveInventoryLabels_QuarantineVulnerability_NoCsTenantMapping_Success() {
        setUpQuarantineTest(false);

        when(inventoryLabelConfig.getCloudsecureTenantIdsByCloudSubscriptionId())
                .thenReturn(new HashMap<>());

        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(quarantineVulnerability))
                    .verifyComplete();

        ArgumentCaptor<Flux<SenderRecord<String, String, String>>> fluxCaptor = ArgumentCaptor.forClass(Flux.class);
        verify(kafkaSender).send(fluxCaptor.capture());

        StepVerifier.create(fluxCaptor.getValue().collectList())
                    .expectNext(Collections.emptyList())
                    .verifyComplete();
    }

    @Test
    public void saveInventoryLabels_NonQuarantineVulnerability1_Success() {
        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(nonQuarantineVulnerability1))
                    .verifyComplete();

        verifyNoInteractions(kafkaSender);
    }

    @Test
    public void saveInventoryLabels_NonQuarantineVulnerability2_Success() {
        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(nonQuarantineVulnerability2))
                    .verifyComplete();

        verifyNoInteractions(kafkaSender);
    }

    @Test
    public void saveInventoryLabels_NonQuarantineVulnerability3_Success() {
        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(nonQuarantineVulnerability3))
                    .verifyComplete();

        verifyNoInteractions(kafkaSender);
    }

    @Test
    public void saveInventoryLabels_NonQuarantineIssue_Success() {
        StepVerifier.create(inventoryLabelSenderService.sendInventoryLabels(nonQuarantineIssue))
                    .verifyComplete();

        verifyNoInteractions(kafkaSender);
    }

    private void setUpQuarantineTest(boolean applyQuarantineLabel) {
        when(findingsPersistenceConfig.getInventoryLabelConfig()).thenReturn(inventoryLabelConfig);
        HashMap<String, List<String>> csTenantIdsByCloudSubscriptionId = new HashMap<>();
        csTenantIdsByCloudSubscriptionId.put("fd72c36d-cdc1-4fe9-9c31-13f3e670b601",
                List.of(CS_TENANT_ID1,
                        CS_TENANT_ID2));
        csTenantIdsByCloudSubscriptionId.put( "fd72c36d-cdc1-4fe9-9c31-13f3e670b602",
                List.of("83f1b531-864b-4c02-9124-9de1645017c3",
                        "83f1b531-864b-4c02-9124-9de1645017c4"));
        when(inventoryLabelConfig.getCloudsecureTenantIdsByCloudSubscriptionId())
                .thenReturn(csTenantIdsByCloudSubscriptionId);
        when(kafkaSender.send(any())).thenReturn(Flux.just(senderResult));

        if (applyQuarantineLabel) {
            HashMap<String, String> quarantineLabelIdByCsTenantId = new HashMap<>();
            quarantineLabelIdByCsTenantId.put(CS_TENANT_ID1, QUARANTINE_LABEL_ID);
            quarantineLabelIdByCsTenantId.put(CS_TENANT_ID2, QUARANTINE_LABEL_ID);
            when(inventoryLabelConfig.getQuarantineLabelIdByCloudSecureTenantId())
                    .thenReturn(quarantineLabelIdByCsTenantId);
            when(findingsPersistenceConfig.getKafkaProducerConfig()).thenReturn(kafkaProducerConfig);
            when(kafkaProducerConfig.getTopic()).thenReturn("cs-sync-inventory2pce-v1");
        }
    }

    private boolean isEqualIgnoringTimestamp(String json1, String json2) {
        try {
            JsonNode node1 = objectMapper.readTree(json1).deepCopy();
            JsonNode node2 = objectMapper.readTree(json2).deepCopy();

            ((ObjectNode) node1).remove("timestamp");
            ((ObjectNode) node2).remove("timestamp");

            return node1.equals(node2);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
