logging:
  level:
    ROOT: INFO
server:
  port: 8080
spring:
  application:
    name: findings-persistence
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

  #  Postgres db config
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432/integrations
    username: citus
    password: DO_NOT_COMMIT
    properties:
      # refer to https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-PROTECTION for sslMode types
      sslMode: require # recommendation: "allow" if connecting to local postgres instance; "require" for Azure deployed postgres instances
retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

findings-persistence:
  inventory-label-config:
    cloudsecure-tenant-ids-by-cloud-subscription-id:
      fd72c36d-cdc1-4fe9-9c31-13f3e670b601:
        - 83f1b531-864b-4c02-9124-9de1645017c1
    quarantine-label-id-by-cloudsecure-tenant-id:
      83f1b531-864b-4c02-9124-9de1645017c1: 18577348462923448

  kafka-consumer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true
    topic: test-vulnerabilities
    groupId: findings-persistence
  kafka-producer-config:
    bootstrapServers: sunnyvale-enf-kafka-eventhub-ns.servicebus.windows.net:9093
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    isConnectionString: true
    topic: test-cs-sync-inventory2pce-v1

