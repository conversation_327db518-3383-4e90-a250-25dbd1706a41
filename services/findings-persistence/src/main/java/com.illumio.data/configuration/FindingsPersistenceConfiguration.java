package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "findings-persistence")
@Getter
@Setter
public class FindingsPersistenceConfiguration {

    private final KafkaConsumerConfig kafkaConsumerConfig = new KafkaConsumerConfig();
    private final KafkaProducerConfig kafkaProducerConfig = new KafkaProducerConfig();
    private final InventoryLabelConfig inventoryLabelConfig = new InventoryLabelConfig();
    private final RetryConfig retryConfig = new RetryConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
    }

    @Getter
    @Setter
    public static class InventoryLabelConfig {
        private HashMap<String, String> quarantineLabelIdByCloudSecureTenantId = new HashMap<>();
        private HashMap<String, List<String>> cloudsecureTenantIdsByCloudSubscriptionId = new HashMap<>();
    }

}
