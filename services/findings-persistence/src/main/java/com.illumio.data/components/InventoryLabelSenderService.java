package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.uuid.Generators;
import com.illumio.data.configuration.FindingsPersistenceConfiguration;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.illumio.data.util.DateTimeUtil.generateCurrentTimestampRfc3339;
import static com.illumio.data.util.FindingsPersistenceConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryLabelSenderService {

    private final KafkaSender<String, String> kafkaSender;
    private final FindingsPersistenceConfiguration findingsPersistenceConfig;
    private final ObjectMapper objectMapper;

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendInventoryLabels(final Finding finding) {
        if (!isQuarantineEligible(finding)) {
            return Mono.empty();
        }

        return kafkaSender
                .send(getInventoryLabelMessageRecords(finding))
                .onErrorResume(error -> {
                    log.error("Error sending inventory labels for finding={}", finding, error);
                    return Mono.empty();
                })
                .doOnComplete(() ->
                        log.debug("Successfully sent inventory labels for finding={} to Kafka", finding))
                .then();
    }

    private boolean isQuarantineEligible(final Finding finding) {
        if (!finding.getFindingsType().equals(FindingType.VULNERABILITY)) {
            return false;
        }
        final Vulnerability vulnerability = (Vulnerability) finding;
        return vulnerability.getStatus().equalsIgnoreCase(OPEN)
                && vulnerability.getMetadata().getSeverity().equals(CRITICAL)
                && vulnerability.getMetadata().getHasExploit();
    }

    @SneakyThrows
    private Flux<SenderRecord<String, String, String>> getInventoryLabelMessageRecords(final Finding finding) {
        return Flux.fromIterable(
                getCloudSecureTenantIds(finding)
                        .stream()
                        .map(csTenantId -> getInventoryLabelMessageRecord(finding, csTenantId))
                        .toList());
    }

    @SneakyThrows
    private SenderRecord<String, String, String> getInventoryLabelMessageRecord(final Finding finding, final String csTenantId) {
        final String topic = findingsPersistenceConfig.getKafkaProducerConfig().getTopic();
        final InventoryLabelMessage inventoryLabelMessage = getInventoryLabelMessage(finding, csTenantId);
        final String message = objectMapper.writeValueAsString(inventoryLabelMessage);
        final String partitionKey = getPartitionKey(inventoryLabelMessage, csTenantId);
        final ProducerRecord<String, String> producerRecord =
                new ProducerRecord<>(topic, partitionKey, message);
        producerRecord.headers().add(DATAPLANE_ID, DATA_PLANE.getBytes());
        producerRecord.headers().add(TENANT_ID, csTenantId.getBytes());
        return SenderRecord.create(producerRecord, null);
    }

    private InventoryLabelMessage getInventoryLabelMessage(final Finding finding, final String csTenantId) {
        final String quarantineLabelId = getQuarantineLabelId(csTenantId);
        if (StringUtils.isEmpty(quarantineLabelId)) {
            throw new IllegalArgumentException(
                    String.format("Quarantine label ID for csTenantId=%s cannot be empty or null.", csTenantId));
        }

        final InventoryLabelAttributes inventoryLabelAttributes =
                InventoryLabelAttributes.builder()
                                        .externalDataReference(getCloudSecureWorkloadId(finding, csTenantId))
                                        .labels(InventoryLabels.builder()
                                                               .set(Collections.singletonList(quarantineLabelId))
                                                               .delete(Collections.emptyList())
                                                               .build())
                                        .build();
        return InventoryLabelMessage.builder()
                                    .timestamp(generateCurrentTimestampRfc3339())
                                    .resources(Collections.singletonList(
                                            InventoryLabelResource.builder()
                                                                  .resourceType(WORKLOAD)
                                                                  .resourceChangeType(UPDATE)
                                                                  .attributes(inventoryLabelAttributes)
                                                                  .build()
                                    ))
                                    .build();
    }

    private String getQuarantineLabelId(final String csTenantId) {
        return Optional.ofNullable(findingsPersistenceConfig.getInventoryLabelConfig())
                .map(FindingsPersistenceConfiguration.InventoryLabelConfig::getQuarantineLabelIdByCloudSecureTenantId)
                .map(quarantineLabelIdByCloudSecureId -> quarantineLabelIdByCloudSecureId.get(csTenantId))
                .orElse(null);
    }

    private String getPartitionKey(final InventoryLabelMessage inventoryLabelMessage, final String csTenantId) {
        return csTenantId + inventoryLabelMessage.getResources().get(0).getAttributes().getExternalDataReference();
    }

    private List<String> getCloudSecureTenantIds(final Finding finding) {
        final Optional<String> findingResourceSubscriptionId = Optional.ofNullable(finding.getResource())
                                                                       .map(FindingResource::getSubscriptionId);
        if (findingResourceSubscriptionId.isEmpty()) {
            throw new IllegalArgumentException(
                    String.format("Finding %s does not have a resource with a subscription ID. " +
                            "Inventory labels will not be produced.", finding));
        }

        return Optional.ofNullable(findingsPersistenceConfig.getInventoryLabelConfig()
                                                            .getCloudsecureTenantIdsByCloudSubscriptionId()
                                                            .get(findingResourceSubscriptionId.get()))
                       .orElse(Collections.emptyList());
    }

    /**
     * Returns the workload ID that CloudSecure uses to identify workloads.
     * This only applies to non-shared resources. For shared resources (i.e. a shared VPC that can be on two AWS accounts),
     * there is different logic to generate the CloudSecure workload ID.
     *
     * TODO before LA/GA: identify if shared resources will be supported (RSA shows VM support only)
     */
    private String getCloudSecureWorkloadId(final Finding finding, final String csTenantId) {
        return Generators.nameBasedGenerator(UUID.fromString(csTenantId))
                         .generate(finding.getResource().getCloudProviderUniqueId())
                         .toString();
    }

}
