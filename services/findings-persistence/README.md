# Findings Persistence

The **findings-persistence** service is responsible for consuming and saving vulnerability meta data and workload_vulnerability data in the respective databases.
---

## Running Locally

### 1. Modify `application-local-eventhub.yml`
Ensure the following configurations are set:

1. **r2dbc database Config** - Postgres database config for connecting to integrations database
2  **Kafka consumer Config** – Kafka consumer config to consume from test-vulnerabilities queue

### 2. Run Locally Using IntelliJ

Use the following command to run the service from command line

- **Run Configuration**: export SPRING_CONFIG_LOCATION=<base_path_of_project>/integrations/services/findings-persistence/src/main/resources/application-local-eventhub.yml
  ./gradlew :service:findings-persistence:bootRun