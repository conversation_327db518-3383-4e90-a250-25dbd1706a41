plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(":commons:data-commons")
    implementation project(":commons:utility-commons")
    implementation platform("com.azure:azure-sdk-bom")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'io.projectreactor.kafka:reactor-kafka'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'io.r2dbc:r2dbc-postgresql'
    implementation 'org.postgresql:postgresql'


    //jackson
    implementation 'com.fasterxml.uuid:java-uuid-generator:5.1.0'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.FindingsPersistenceApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}
