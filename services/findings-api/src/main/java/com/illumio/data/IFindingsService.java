package com.illumio.data;

import com.illumio.data.components.model.FindingsRequest;
import com.illumio.data.model.FindingsResponse;
import com.illumio.data.model.GetColumnsMetadata200Response;
import reactor.core.publisher.Mono;

public interface IFindingsService {
    Mono<FindingsResponse> getFindingsDataByWorkloadId(FindingsRequest findingsRequest);
    Mono<FindingsResponse> searchFindingsDataByWorkloadId(FindingsRequest findingsRequest);
    Mono<GetColumnsMetadata200Response> getColumnsMetadata();
}
