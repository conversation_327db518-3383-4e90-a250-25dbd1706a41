package com.illumio.data;


import com.illumio.data.components.model.FindingsType;
import com.illumio.data.exception.BadRequestException;
import com.illumio.data.impl.IssuesServiceImpl;
import com.illumio.data.impl.VulnerabilityServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


import static com.illumio.data.components.helper.Constants.FindingsConstants.INVALID_FINDINGS_TYPE;

@Component
@RequiredArgsConstructor
public class FindingsServiceContext {
    private final VulnerabilityServiceImpl vulnerabilityService;
    private final IssuesServiceImpl issueService;

    public IFindingsService getService(String findingsType) {
        FindingsType type;
        try {
            type = FindingsType.valueOf(findingsType);
        } catch (IllegalArgumentException e) {
            throw new BadRequestException(INVALID_FINDINGS_TYPE + findingsType);
        }

        return switch (type) {
            case VULNERABILITY -> vulnerabilityService;
            case ISSUE -> issueService;
        };
    }
}