openapi: 3.0.3
info:
  title: Findings API Service
  description: APIs for Findings metadata based on cspId and subscriptionId passed from the UI
  version: 1.0.0
servers:
  - url: http://localhost:8084
tags:
  - name: findings
    description: APIs for Findings metadata based on cspId and subscriptionId
    externalDocs:
      description: Find out more
      url: https://confluence.illum.io/display/ARCH/%5BM1%5D+Integrations+framework
security:
  - ApiKeyAuth: []
paths:
  /api/v1/findings/workloads/{csp_id}/metadata:
    get:
      tags:
        - findings
      summary: Find findings by CspId based on type
      description: Returns findings metadata
      operationId: getFindingsDataByWorkloadId
      parameters:
        - name: csp_id
          in: path
          description: A unique identifier for the workload in CS.
          required: true
          schema:
            type: string

        - name: subscription_id
          in: query
          description: The subscription group the findings is part of.
          required: true
          schema:
            type: string

        - name: findings_type
          in: query
          description: The type of findings that is being queried - ISSUE, VULNERABILITY
          required: true
          schema:
            type: string
            enum: ["ISSUE", "VULNERABILITY"]
        - name: cloud_provider_name
          in: query
          description: Name of the cloud provider - AWS, AZURE, OCI, GCP
          required: true
          schema:
            type: string
            enum:
              - AWS
              - AZURE
              - OCI
              - GCP

        - name: service_type
          in: query
          description: The object_type that identifies the resource in AWS. Should be passed when the cloud provider is AWS.
          example: "ec2"
          schema:
            type: string

        - name: page
          in: query
          description: Page number for pagination, starts with 1.
          required: false
          schema:
            type: integer
            default: 1

        - name: limit
          in: query
          description: Number of items per page.
          required: false
          schema:
            type: integer
            default: 50

        - name: sort_by
          in: query
          description: Field to sortBy refer getColumns API for list of fields for vulnerability and issues.
          required: false
          schema:
            type: string
            enum:
              - cve_description
              - cvss_severity
              - status
              - vulnerable_component_name
              - cve_id
              - has_exploit
              - score
              - source_rule_names
              - severity
              - risks
        - name: sort_order
          in: query
          description: Order to sort by. Allowed values are ASC (ascending), DESC (descending).
          required: false
          schema:
            type: string
            enum:
              - ASC
              - DESC
            default: ASC
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FindingsResponse'
        '404':
          description: Workload Metadata Not Found
          content:
            application/json:
              schema:
                type: string
              example: "The specified workload was not found."

        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                type: string
              example: "The input provided is invalid."
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: string
              example: "An internal server error occurred. Please try again later."
        '401':
          description: Unauthorized - Missing or Invalid Authorization
          content:
            application/json:
              schema:
                type: string
              example: "Missing or invalid authorization token."

  /api/v1/findings/search/{csp_id}/metadata:
    post:
      tags:
        - findings
      summary: Search for findings metadata
      description: Search for findings metadata based on various criteria
      operationId: searchFindingsData
      parameters:
        - name: csp_id
          in: path
          description: A unique identifier for the workload in CS.
          required: true
          schema:
            type: string

        - name: subscription_id
          in: query
          description: The subscription group the call is part of.
          required: true
          schema:
            type: string

        - name: findings_type
          in: query
          description: The type of findings that is being queried - ISSUE, VULNERABILITY
          required: true
          schema:
            type: string
            enum: ["ISSUE", "VULNERABILITY"]

        - name: cloud_provider_name
          in: query
          description: Name of the cloud provider - AWS, AZURE, OCI, GCP
          required: true
          schema:
            type: string
            enum:
              - AWS
              - AZURE
              - OCI
              - GCP

        - name: service_type
          in: query
          description: The object_type that identifies the resource in AWS. Should be passed when the cloud provider is AWS.
          example: "ec2"
          schema:
            type: string

      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FindingsSearchRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FindingsResponse'
        '404':
          description: Workload Metadata Not Found
          content:
            application/json:
              schema:
                type: string
              example: "The specified workload was not found."
        '400':
          description: Bad Request - Invalid input
          content:
            application/json:
              schema:
                type: string
              example: "The input provided is invalid."
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: string
              example: "An internal server error occurred. Please try again later."
        '401':
          description: Unauthorized - Missing or Invalid Authorization
          content:
            application/json:
              schema:
                type: string
              example: "Missing or invalid authorization token."
  /api/v1/findings/columns/metadata:
    get:
      tags:
        - findings
      summary: Get columns metadata
      description: Returns metadata about the findings columns for the UI
      operationId: getColumnsMetadata
      parameters:
        - name: findings_type
          in: query
          description: The type of findings that is being queried - ISSUE, VULNERABILITY
          required: true
          schema:
            type: string
            enum: ["ISSUE", "VULNERABILITY"]
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  columns:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                          example: severity
                        type:
                          type: string
                          example: string
                        example:
                          type: string
                          example: high
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: string
              example: "An internal server error occurred. Please try again later."
        '401':
          description: Unauthorized - Missing or Invalid Authorization
          content:
            application/json:
              schema:
                type: string
              example: "Missing or invalid authorization token."
components:
  schemas:
    Vulnerability:
      type: object
      properties:
        cve_description:
          type: string
          example: Buffer overflow vulnerability
        cvss_severity:
          type: string
          example: HIGH
        status:
          type: string
          example: open
        vulnerable_component_name:
          type: string
          example: libXYZ
        cve_id:
          type: string
          example: CVE-2023-1234
        has_exploit:
          type: boolean
          example: true
        score:
          type: number
          example: 9.1

    Issues:
      type: object
      properties:
        source_rule_names:
          type: string
          example: Publicly exposed VM on port
        severity:
          type: string
          example: HIGH
        status:
          type: string
          example: OPEN
        risks:
          type: array
          items:
            type: string
          example:  ["EXTERNAL_EXPOSURE","UNPROTECTED_DATA"]

    FindingsResponse:
      type: object
      properties:
        page_number:
          description: Current pageNumber
          type: integer
          example: 1
        page_size:
          type: integer
          example: 10
        total_elements:
          type: integer
          example: 100
        total_pages:
          type: integer
          example: 10
        next_page:
          description: The nextPage number, if there are no more pages nextPage will be -1
          type: integer
          example: 2
        csp_id:
          type: string
          example: "workload-001"
        subscription_id:
          type: string
          example: "9123457225"
        findings_type:
          type: string
          example: "ISSUE"
        findings:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Vulnerability'
              - $ref: '#/components/schemas/Issues'

    FindingsSearchRequest:
      type: object
      properties:
        fields:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                enum:
                  - cve_description
                  - cvss_severity
                  - status
                  - vulnerable_component_name
                  - cve_id
                  - has_exploit
                  - score
                  - source_rule_names
                  - severity
                  - risks
                example: "cve_description"
              value:
                type: string
                example: "Description of the CVE"
          example:
            - key: "cve_description"
              value: "Buffer overflow vulnerability"
            - key: "cvss_severity"
              value: "HIGH"
            - key: "status"
              value: "OPEN"
        sort_by:
          type: string
          enum:
            - cve_description
            - cvss_severity
            - status
            - vulnerable_component_name
            - cve_id
            - has_exploit
            - score
            - source_rule_names
            - severity
            - risks
          example: "status"
        sort_order:
          type: string
          enum:
            - ASC
            - DESC
          example: "ASC"
          default: "ASC"
        limit:
          type: integer
          example: 10
          minimum: 1
          default: 50
        page:
          type: integer
          example: 1
          minimum: 1
          default: 1

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: x-cs-id