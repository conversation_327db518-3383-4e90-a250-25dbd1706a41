logging:
  level:
    ROOT: INFO
server:
  port: 8080
spring:
  application:
    name: findings-api
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  r2dbc:
    url: r2dbc:postgresql://c-integrationstest.mmtdd3hnha22tf.postgres.cosmos.azure.com:5432
    username: citus
    password: DO_NOT_COMMIT
    properties:
      sslMode: require
  springdoc:
    swagger-ui:
      path: /swagger-ui.html
retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2
census:
  auth:
    enabled: true
grpc:
  channel:
    host: DO_NOT_COMMIT
    port: 443
    enableMtls: false
    caCert:
    mtlsKey:
    mtlsCert:
  server:
    port: 9090
