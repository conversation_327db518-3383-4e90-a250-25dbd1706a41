package com.illumio.data.model.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.TenantOnboarding;
import com.illumio.data.model.TenantOnboardingRequest;
import lombok.experimental.UtilityClass;

@UtilityClass
public class TenantOnboardingRequestMapper {

    public TenantOnboardingEntity toTenantOnboarding(final TenantOnboardingRequest onboardingRequest,
                                                     final String credentialsVaultPath,
                                                     final ObjectMapper objectMapper) {
        final TenantOnboarding tenantOnboarding = TenantOnboarding.builder()
                               .tenantId(onboardingRequest.getTenantId())
                               .integration(onboardingRequest.getIntegration())
                               .credentialsVaultPath(credentialsVaultPath)
                               .configurations(onboardingRequest.getConfigurations())
                               .tenantMetadata(onboardingRequest.getTenantMetadata())
                               .build();
        return TenantOnboardingMapper.toTenantOnboardingEntity(tenantOnboarding, objectMapper);
    }

}
