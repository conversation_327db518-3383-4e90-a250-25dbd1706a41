package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.Integration;
import com.illumio.data.model.SyncTask;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
public class TaskSenderService {

    private final KafkaSender<String, String> kafkaLightTaskSender;
    private final KafkaSender<String, String> kafkaHeavyTaskSender;
    private final IntegrationsManagerConfig integrationsManagerConfig;
    private final ObjectMapper objectMapper;

    public TaskSenderService(@Qualifier("kafkaLightTaskSender") final KafkaSender<String, String> kafkaLightTaskSender,
                             @Qualifier("kafkaHeavyTaskSender") final KafkaSender<String, String> kafkaHeavyTaskSender,
                             final IntegrationsManagerConfig integrationsManagerConfig,
                             final ObjectMapper objectMapper) {
        this.kafkaLightTaskSender = kafkaLightTaskSender;
        this.kafkaHeavyTaskSender = kafkaHeavyTaskSender;
        this.integrationsManagerConfig = integrationsManagerConfig;
        this.objectMapper = objectMapper;
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendTask(final SyncTask syncTask) {
        return getSender(syncTask)
                .send(Mono.just(createRecord(syncTask)))
                .doOnComplete(() ->
                        log.debug("Successfully pushed heavy task to Kafka for integration={} and tenantId={}",
                                syncTask.getIntegration(), syncTask.getTenantId()))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createRecord(final SyncTask syncTask) {
        final String topic = getTopic(syncTask);
        final String message = objectMapper.writeValueAsString(syncTask);
        final String partitionKey = syncTask.getIntegration() + syncTask.getTenantId();
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, partitionKey, message);
        return SenderRecord.create(producerRecord, null);
    }

    private KafkaSender<String, String> getSender(final SyncTask task) {
        return isLightTask(task)
                ? kafkaLightTaskSender
                : kafkaHeavyTaskSender;
    }

    private String getTopic(final SyncTask task) {
        return isLightTask(task)
                ? integrationsManagerConfig.getKafkaLightTaskProducerConfig().getTopic()
                : integrationsManagerConfig.getKafkaHeavyTaskProducerConfig().getTopic();
    }

    private boolean isLightTask(final SyncTask task) {
        return task.getIntegration().equals(Integration.WIZ);
    }

}
