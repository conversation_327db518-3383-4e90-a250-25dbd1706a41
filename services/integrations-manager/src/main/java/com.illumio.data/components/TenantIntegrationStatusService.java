package com.illumio.data.components;

import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.model.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.IntegrationManagerConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


@Slf4j
@Component
@RequiredArgsConstructor
public class TenantIntegrationStatusService {

    private final LDService ldService;
    private final TaskDbService taskDbService;
    private final OnboardingDbService onboardingDbService;
    private final CredentialsService credentialsService;
    private final IntegrationsManagerConfig integrationsManagerConfig;

    public Mono<TenantIntegrationStatusResponse> fetchTenantIntegrationStatus(TenantIntegrationStatusRequest request) {
        log.debug("Fetching sync status for tenantId={} and integration={}",
                request.getTenantId(), request.getIntegration());

        return onboardingDbService.fetchTenantOnboarding(request.getTenantId(), request.getIntegration())
                                  .flatMap(tenantOnboarding -> {
                                      log.debug("Fetching credentials from vault path: {}", tenantOnboarding.getCredentialsVaultPath());
                                      return credentialsService.getCredentials(tenantOnboarding.getCredentialsVaultPath())
                                                               .flatMap(credentials -> taskDbService.fetchLastCompletedTask(request.getTenantId(), request.getIntegration())
                                                                .map(taskEntity -> buildTenantIntegrationStatusResponse(taskEntity, tenantOnboarding, credentials))
                                                                .switchIfEmpty(Mono.just(buildTenantIntegrationStatusResponse(null, tenantOnboarding, credentials))));
                                  })
                                  .switchIfEmpty(Mono.just(buildTenantIntegrationStatusResponse(null, null,null)))
                                  .zipWith((ldService.integrationIsEnabledForTenant(request.getTenantId(), request.getIntegration())),
                                          (statusResponse, integrationIsEnabled) ->
                                                  statusResponse.toBuilder().isEnabled(integrationIsEnabled).build())
                                  .doOnError(e -> {
                                      if (!(e instanceof ResponseStatusException)) {
                                          log.error("Error occurred while fetching onboarding sync status", e);
                                      }
                                  })
                                  .onErrorMap(e -> !(e instanceof ResponseStatusException),
                                          e -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                                                  "Error fetching onboarding sync status", e));
    }

    private TenantIntegrationStatusResponse buildTenantIntegrationStatusResponse(TaskStatusEntity lastCompletedTaskEntity,
                                                       TenantOnboarding tenantOnboarding,
                                                       Credentials credentials) {
        TenantIntegrationStatusResponse.TenantIntegrationStatusResponseBuilder builder =
                TenantIntegrationStatusResponse.builder()
                                               .isOnboarded(tenantOnboarding != null);

        if (tenantOnboarding == null) {
            builder.statusMessage(IntegrationManagerConstants.UI_TENANT_STATUS_NOT_ONBOARDED_MESSAGE);
        } else {
            builder.clientId(Optional.ofNullable(credentials)
                                     .map(Credentials::getClientId)
                                     .orElse(null))
                   .authURL(Optional.ofNullable(tenantOnboarding.getConfigurations())
                                    .map(TenantConfigurations::getAuthUrl)
                                    .orElse(null))
                   .apiURL(Optional.ofNullable(tenantOnboarding.getConfigurations())
                                   .map(TenantConfigurations::getApiUrl)
                                   .orElse(null));

            if (lastCompletedTaskEntity == null) {
                builder.status(UiTenantStatus.ONBOARDING)
                       .statusMessage(IntegrationManagerConstants.UI_TENANT_STATUS_ONBOARDING_MESSAGE)
                       .lastUpdateTime(DateTimeUtil.generateCurrentTimestamp());
            } else {
                builder.status(UiTenantStatus.valueOf(lastCompletedTaskEntity.getStatus().name()))
                       .statusMessage(lastCompletedTaskEntity.getStatusMessage())
                       .lastUpdateTime(DateTimeUtil.toString(lastCompletedTaskEntity.getEndTime()).orElse(null));
            }
        }

        return builder.build();
    }

    public Flux<IntegrationResponseItem> getEligibleIntegrations(final String tenantId) {
        return onboardingDbService.fetchTenantOnboardings(tenantId)
                .collectMap(TenantOnboarding::getIntegration)
                .flatMapMany(onboardingsByIntegration ->
                        Flux.fromIterable(getAllIntegrations())
                            .map(integrationResponseItem ->
                                integrationResponseItem.toBuilder()
                                                       .isOnboarded(onboardingsByIntegration
                                                               .containsKey(integrationResponseItem.getId()))
                                                       .build())
                                .filterWhen(integrationResponseItem ->
                                        ldService.integrationIsEnabledForTenant(tenantId, integrationResponseItem.getId()))
                );
    }

    private List<IntegrationResponseItem> getAllIntegrations() {
        return Arrays.stream(Integration.values()).map(integration ->
                      Optional.ofNullable(getIntegrationConfig(integration))
                              .map(integrationConfig ->
                                      IntegrationResponseItem.builder()
                                                             .id(integration)
                                                             .name(integrationConfig.getName())
                                                             .description(integrationConfig.getDescription())
                                                             .build())
                              .orElse(IntegrationResponseItem.builder()
                                                             .id(integration)
                                                             .build()))
              .filter(Objects::nonNull)
              .toList();
    }

    private IntegrationsManagerConfig.IntegrationConfig getIntegrationConfig(final Integration integration) {
        return Optional.ofNullable(integrationsManagerConfig.getIntegrationsConfig())
                .map(integrationsConfig ->
                        switch (integration) {
                            case WIZ -> integrationsConfig.getWiz();
                            case ARMIS -> integrationsConfig.getArmis();
                        })
                .orElse(null);
    }

}


