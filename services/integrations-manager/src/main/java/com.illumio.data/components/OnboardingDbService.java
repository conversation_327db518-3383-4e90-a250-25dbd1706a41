package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.mapper.TenantOnboardingMapper;
import com.illumio.data.model.mapper.TenantOnboardingRequestMapper;
import com.illumio.data.repositories.OnboardingRepository;
import com.illumio.data.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OnboardingDbService {

    private final OnboardingRepository onboardingRepository;
    private final ObjectMapper objectMapper;

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Boolean> isTenantOnboarded(TenantIntegrationPair tenantIntegrationPair) {
        return onboardingRepository.isTenantOnboarded(
                tenantIntegrationPair.getTenantId(), tenantIntegrationPair.getIntegration());
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> saveTenantOnboarding(final TenantOnboardingRequest onboardingRequest, final String credentialsVaultPath) {
        final TenantOnboardingEntity tenantOnboarding =
                TenantOnboardingRequestMapper.toTenantOnboarding(onboardingRequest, credentialsVaultPath, objectMapper);
        return onboardingRepository.save(tenantOnboarding)
                                   .doOnSuccess(success -> log.debug(
                                           "Successfully saved tenant onboarding metadata to database for tenantId={} and integration={}",
                                           onboardingRequest.getTenantId(),
                                           onboardingRequest.getIntegration()
                                   ))
                                   .then();
    }


    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Flux<TenantOnboarding> fetchTenantOnboardings(final String tenantId) {
        return onboardingRepository.findByTenantId(tenantId)
                                   .map(tenantOnboardingEntity -> TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity, objectMapper));
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<TenantOnboarding> fetchTenantOnboarding(String tenantId, Integration integration) {
        return onboardingRepository.findByTenantIdAndIntegration(tenantId, integration.name())
                                   .doOnSuccess(entity -> {
                                       if (entity != null) {
                                           log.debug("Successfully retrieved tenant onboarding entity for tenantId={} and integration={}",
                                                   tenantId, integration);
                                       } else {
                                           log.debug("No tenant onboarding entity found for tenantId={} and integration={}",
                                                   tenantId, integration);
                                       }
                                   })
                                   .doOnError(error -> log.error("Error fetching tenant onboarding entity for tenantId={} and integration={}",
                                           tenantId, integration, error))
                                   .map(tenantOnboardingEntity -> TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity, objectMapper));
    }

}