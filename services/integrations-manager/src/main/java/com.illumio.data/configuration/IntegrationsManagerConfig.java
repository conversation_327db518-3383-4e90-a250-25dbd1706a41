package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

import static com.illumio.data.util.IntegrationManagerConstants.INTEGRATIONS_MANAGER_CONFIG_PREFIX;

@Configuration
@ConfigurationProperties(prefix = INTEGRATIONS_MANAGER_CONFIG_PREFIX)
@Getter
@Setter
public class IntegrationsManagerConfig {

    private final IntegrationsConfig integrationsConfig = new IntegrationsConfig();
    private final RetryConfig retryConfig = new RetryConfig();
    private final KafkaProducerConfig kafkaLightTaskProducerConfig = new KafkaProducerConfig();
    private final KafkaProducerConfig kafkaHeavyTaskProducerConfig = new KafkaProducerConfig();

    @Configuration
    @Getter
    @Setter
    public static class RetryConfig {
        private Integer maxRetries = 3;
        private Duration minBackoff = Duration.ofSeconds(30);
    }

    @Configuration
    @Getter
    @Setter
    public static class IntegrationsConfig {
        private WizConfig wiz;
        private ArmisConfig armis;
    }

    @Configuration
    @Getter
    @Setter
    public static class IntegrationConfig {
        private String name;
        private String description;
        private String apiHost;
    }

    @Configuration
    @Getter
    @Setter
    public static class WizConfig extends IntegrationConfig {

    }

    @Configuration
    @Getter
    @Setter
    public static class ArmisConfig extends IntegrationConfig {

    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
    }

}
