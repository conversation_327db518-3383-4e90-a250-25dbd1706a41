package com.illumio.data.components;

import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.exception.BadRequestException;
import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.WizTenantConfigurations;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantValidationServiceTest {

    @Mock
    private LDService ldService;
    @Mock
    private OnboardingDbService onboardingDbService;
    @Mock
    private TokenGenerationService tokenGenerationService;
    @Mock
    private IntegrationsManagerConfig integrationsManagerConfig;
    @InjectMocks
    private TenantValidationService tenantValidationService;

    private TenantOnboardingRequest tenantOnboardingRequest;
    private Credentials credentials;
    private TokenRequest expectedTokenRequest;
    private Token tokenResponse;

    private static final String WIZ_AUTH_URL = "https://auth.app.wiz.io/oauth/token";
    private static final String WIZ_API_URL = "https://api.us41.app.wiz.io/graphql";
    private static final String ARMIS_AUTH_URL = "https://api.armis.com/auth";
    private static final String ARMIS_API_URL = "https://api.armis.com/api";

    @BeforeEach
    void setUp() {
        credentials = new Credentials();
        credentials.setClientId("test-client-id");
        credentials.setSecret("test-secret");

        tenantOnboardingRequest = new TenantOnboardingRequest();
        tenantOnboardingRequest.setTenantId("test-tenant");
        tenantOnboardingRequest.setCredentials(credentials);

        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .build();

        tokenResponse = new Token();
        tokenResponse.setAccessToken("test-token");
        IntegrationsManagerConfig.IntegrationsConfig integrationsConfig = new IntegrationsManagerConfig.IntegrationsConfig();
        IntegrationsManagerConfig.WizConfig wizConfig = new IntegrationsManagerConfig.WizConfig();
        wizConfig.setApiHost("app.wiz.io");
        integrationsConfig.setWiz(wizConfig);
        IntegrationsManagerConfig.ArmisConfig armisConfig = new IntegrationsManagerConfig.ArmisConfig();
        armisConfig.setApiHost("api.armis.com");
        integrationsConfig.setArmis(armisConfig);

        when(integrationsManagerConfig.getIntegrationsConfig()).thenReturn(integrationsConfig);
    }

    @Test
    void validateTenantOnboarding_Request_WhenNotOnboardedAndValidCredentialsAndUrls_ShouldReturnTrue() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());
        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(Integration.WIZ)
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(WIZ_AUTH_URL)
                                           .build();

        when(onboardingDbService.isTenantOnboarded(any(TenantIntegrationPair.class)))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));
        when(tokenGenerationService.getToken(eq(expectedTokenRequest)))
                .thenReturn(Mono.just(tokenResponse));

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest,Boolean.TRUE))
                    .expectNext(true)
                    .verifyComplete();

        verify(onboardingDbService, times(1))
                .isTenantOnboarded(argThat(arg ->
                        arg.getTenantId().equals(tenantOnboardingRequest.getTenantId())
                                && arg.getIntegration().equals(tenantOnboardingRequest.getIntegration())));
        verify(ldService).integrationIsEnabledForTenant(eq(tenantOnboardingRequest.getTenantId()),
                eq(tenantOnboardingRequest.getIntegration()));
        verify(tokenGenerationService, times(1)).getToken(eq(expectedTokenRequest));
    }



    @Test
    void validateTenantOnboarding_Request_WhenInvalidAuthUrl_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl("https://auth.invalid.com/token")
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Invalid authentication URL. URL must be properly formatted".equals(error.getMessage()))
                    .verify();
    }

    @Test
    void validateTenantOnboarding_Request_WhenInvalidApiUrl_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ); // Wiz
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl("notaurl")
                                                                         .build());

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Invalid API URL. URL must be properly formatted".equals(error.getMessage()))
                    .verify();
    }

    @Test
    void validateTenantOnboardingRequestDisabled_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());
        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(Integration.WIZ)
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(WIZ_AUTH_URL)
                                           .build();

        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(false));

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Integration is not enabled for tenant".equals(error.getMessage()))
                    .verify();

        verify(ldService).integrationIsEnabledForTenant(eq(tenantOnboardingRequest.getTenantId()),
                eq(tenantOnboardingRequest.getIntegration()));
    }

    @Test
    void validateTenantOnboarding_Request_WhenAlreadyOnboarded_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());
        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(Integration.WIZ)
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(WIZ_AUTH_URL)
                                           .build();

        when(onboardingDbService.isTenantOnboarded(any(TenantIntegrationPair.class)))
                .thenReturn(Mono.just(true));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Tenant is already onboarded to integration".equals(error.getMessage()))
                    .verify();

        verify(onboardingDbService, times(1))
                .isTenantOnboarded(argThat(arg ->
                        arg.getTenantId().equals(tenantOnboardingRequest.getTenantId())
                                && arg.getIntegration().equals(tenantOnboardingRequest.getIntegration())));
        verify(ldService).integrationIsEnabledForTenant(eq(tenantOnboardingRequest.getTenantId()),
                eq(tenantOnboardingRequest.getIntegration()));
    }

    @Test
    void validateTenantOnboarding_Request_Patch_Call_WhenAlreadyOnboarded_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());
        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(Integration.WIZ)
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(WIZ_AUTH_URL)
                                           .build();

        when(onboardingDbService.isTenantOnboarded(any(TenantIntegrationPair.class)))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));

    StepVerifier.create(
            tenantValidationService.validateTenantOnboardingRequest(
                tenantOnboardingRequest, Boolean.FALSE))
        .expectErrorMatches(
            error ->
                error instanceof BadRequestException
                    && "Tenant and integration not onboarded".equals(error.getMessage()))
        .verify();

        verify(onboardingDbService, times(1))
                .isTenantOnboarded(argThat(arg ->
                        arg.getTenantId().equals(tenantOnboardingRequest.getTenantId())
                                && arg.getIntegration().equals(tenantOnboardingRequest.getIntegration())));
        verify(ldService).integrationIsEnabledForTenant(eq(tenantOnboardingRequest.getTenantId()),
                eq(tenantOnboardingRequest.getIntegration()));
    }


    @Test
    void validateTenantOnboarding_Request_WhenInvalidCredentials_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(WIZ_AUTH_URL)
                                                                         .apiUrl(WIZ_API_URL)
                                                                         .build());
        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(Integration.WIZ)
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(WIZ_AUTH_URL)
                                           .build();

        when(onboardingDbService.isTenantOnboarded(any(TenantIntegrationPair.class)))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));
    final ExternalServiceException tokenException =
        new ExternalServiceException("Invalid credentials");
        when(tokenGenerationService.getToken(eq(expectedTokenRequest)))
                .thenReturn(Mono.error(tokenException));

        // Act & Assert
        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof ExternalServiceException &&
                            tokenException.getMessage().equals(error.getMessage()))
                    .verify();

        verify(onboardingDbService, times(1))
                .isTenantOnboarded(argThat(arg ->
                        arg.getTenantId().equals(tenantOnboardingRequest.getTenantId())
                                && arg.getIntegration().equals(tenantOnboardingRequest.getIntegration())));
        verify(tokenGenerationService, times(1)).getToken(eq(expectedTokenRequest));
    }


    @Test
    void validateTenantOnboarding_Request_Armis_WhenNotOnboardedAndValidCredentialsAndUrls_ShouldReturnTrue() {
        tenantOnboardingRequest.setIntegration(Integration.ARMIS);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(ARMIS_AUTH_URL)
                                                                         .apiUrl(ARMIS_API_URL)
                                                                         .build());

        expectedTokenRequest = TokenRequest.builder()
                                           .tenantId(tenantOnboardingRequest.getTenantId())
                                           .integration(tenantOnboardingRequest.getIntegration())
                                           .credentials(tenantOnboardingRequest.getCredentials())
                                           .authUrl(ARMIS_AUTH_URL)
                                           .build();

        when(onboardingDbService.isTenantOnboarded(any(TenantIntegrationPair.class)))
                .thenReturn(Mono.just(false));
        when(ldService.integrationIsEnabledForTenant(any(), any()))
                .thenReturn(Mono.just(true));
        when(tokenGenerationService.getToken(eq(expectedTokenRequest)))
                .thenReturn(Mono.just(tokenResponse));

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectNext(true)
                    .verifyComplete();

        verify(onboardingDbService, times(1))
                .isTenantOnboarded(argThat(arg ->
                        arg.getTenantId().equals(tenantOnboardingRequest.getTenantId())
                                && arg.getIntegration().equals(tenantOnboardingRequest.getIntegration())));
        verify(ldService).integrationIsEnabledForTenant(eq(tenantOnboardingRequest.getTenantId()),
                eq(tenantOnboardingRequest.getIntegration()));
        verify(tokenGenerationService, times(1)).getToken(eq(expectedTokenRequest));
    }


    @Test
    void validateTenantOnboarding_Request_Armis_WhenInvalidAuthUrl_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.ARMIS);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl("https://auth.invalid.com/token")
                                                                         .apiUrl(ARMIS_API_URL)
                                                                         .build());

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Invalid authentication URL. URL must be properly formatted".equals(error.getMessage()))
                    .verify();
    }

    @Test
    void validateTenantOnboarding_Request_Armis_WhenInvalidApiUrl_ShouldReturnFalse() {
        tenantOnboardingRequest.setIntegration(Integration.ARMIS);
        tenantOnboardingRequest.setConfigurations(WizTenantConfigurations.builder()
                                                                         .authUrl(ARMIS_AUTH_URL)
                                                                         .apiUrl("notaurl")
                                                                         .build());

        StepVerifier.create(tenantValidationService.validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE))
                    .expectErrorMatches(error -> error instanceof BadRequestException &&
                            "Invalid API URL. URL must be properly formatted".equals(error.getMessage()))
                    .verify();
    }
}