package com.illumio.data.model.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.WizTenantConfigurations;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class TenantOnboardingRequestDeserializerTest {

    @Test
    void testDeserializeWithWizIntegration() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(TenantOnboardingRequest.class, new TenantOnboardingRequestDeserializer());
        mapper.registerModule(module);

        TenantOnboardingRequest tenantOnboardingRequest = tenantOnboardingRequest(
                Integration.WIZ,
                WizTenantMetadata.builder()
                                 .cloudSecureTenantId("csTenantId")
                                 .build());
        String requestJson = mapper.writeValueAsString(tenantOnboardingRequest);
        TenantOnboardingRequest request = mapper.readValue(requestJson, TenantOnboardingRequest.class);

        assertEquals(tenantOnboardingRequest, request);
    }

    @Test
    void testDeserializeWithArmisIntegration() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(TenantOnboardingRequest.class, new TenantOnboardingRequestDeserializer());
        mapper.registerModule(module);

        TenantOnboardingRequest tenantOnboardingRequest = tenantOnboardingRequest(Integration.ARMIS, new EmptyTenantMetadata());
        String requestJson = mapper.writeValueAsString(tenantOnboardingRequest);
        TenantOnboardingRequest request = mapper.readValue(requestJson, TenantOnboardingRequest.class);

        assertEquals(tenantOnboardingRequest, request);
    }

    private TenantOnboardingRequest tenantOnboardingRequest(Integration integration, TenantMetadata tenantMetadata) {
        return TenantOnboardingRequest.builder()
                                      .tenantId("tenant123")
                                      .integration(integration)
                                      .credentials(Credentials.builder()
                                                              .clientId("clientId")
                                                              .secret("secret")
                                                              .build())
                                      .configurations(WizTenantConfigurations.builder()
                                                                             .authUrl("https://auth.app.wiz.io/oauth/token")
                                                                             .apiUrl("https://api.us41.app.wiz.io/graphql")
                                                                             .build())
                                      .tenantMetadata(tenantMetadata)
                                      .build();
    }
}