package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.model.Integration;
import com.illumio.data.model.SyncTask;

import com.illumio.data.model.TaskType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskSenderServiceTest {

    @Mock
    private KafkaSender<String, String> kafkaSender;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private IntegrationsManagerConfig taskManagerConfiguration;
    @Mock private IntegrationsManagerConfig.KafkaProducerConfig kafkaHeavyTaskProducerConfig;
    @Mock private IntegrationsManagerConfig.KafkaProducerConfig kafkaLightTaskProducerConfig;

    @InjectMocks
    private TaskSenderService taskSenderService;

    @Captor
    private ArgumentCaptor<Mono<SenderRecord<String, String, String>>> senderRecordCaptor;

    private static final String HEAVY_TOPIC = "heavy-topic";
    private static final String LIGHT_TOPIC = "light-topic";
    private static final String TASK_JSON = "{\"taskId\":\"123\"}";

    @BeforeEach
    void setUp() {
        when(kafkaSender.send(any())).thenReturn(Flux.empty());
    }

    @Test
    void sendTask_shouldSendArmisTaskToHeavyQueue() throws JsonProcessingException {
        SyncTask task = SyncTask.builder()
                                .taskId("123")
                                .tenantId("tenant-1")
                                .taskType(TaskType.GREENFIELD)
                                .integration(Integration.ARMIS)
                                .build();

        when(kafkaHeavyTaskProducerConfig.getTopic()).thenReturn(HEAVY_TOPIC);
        when(taskManagerConfiguration.getKafkaHeavyTaskProducerConfig()).thenReturn(kafkaHeavyTaskProducerConfig);

        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);

        StepVerifier.create(taskSenderService.sendTask(task))
                    .verifyComplete();


        verify(kafkaSender).send(senderRecordCaptor.capture());
        verify(taskManagerConfiguration).getKafkaHeavyTaskProducerConfig();
        verify(kafkaHeavyTaskProducerConfig).getTopic();
        verify(objectMapper).writeValueAsString(task);
        Mono<SenderRecord<String, String, String>> capturedMono = senderRecordCaptor.getValue();
        SenderRecord<String, String, String> senderRecord = capturedMono.block();
        assertEquals(HEAVY_TOPIC, senderRecord.topic());
        assertEquals(TASK_JSON, senderRecord.value());

    }

    @Test
    void sendTask_shouldSendWizTaskToLightQueue() throws JsonProcessingException {

        SyncTask task = SyncTask.builder()
                                .taskId("456")
                                .tenantId("tenant-2")
                                .taskType(TaskType.GREENFIELD)
                                .integration(Integration.WIZ)
                                .build();

        when(kafkaLightTaskProducerConfig.getTopic()).thenReturn(LIGHT_TOPIC);
        when(taskManagerConfiguration.getKafkaLightTaskProducerConfig()).thenReturn(kafkaLightTaskProducerConfig);

        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);


        StepVerifier.create(taskSenderService.sendTask(task))
                    .verifyComplete();


        verify(kafkaSender).send(senderRecordCaptor.capture());
        verify(taskManagerConfiguration).getKafkaLightTaskProducerConfig();
        verify(kafkaLightTaskProducerConfig).getTopic();
        verify(objectMapper).writeValueAsString(task);


        Mono<SenderRecord<String, String, String>> capturedMono = senderRecordCaptor.getValue();
        SenderRecord<String, String, String> senderRecord = capturedMono.block();
        assertEquals(LIGHT_TOPIC, senderRecord.topic());
        assertEquals(TASK_JSON, senderRecord.value());

    }


    @Test
    void sendTask_shouldHandleKafkaSendFailure() throws JsonProcessingException {
        SyncTask task = SyncTask.builder()
                                .taskId("101")
                                .tenantId("tenant-4")
                                .taskType(TaskType.GREENFIELD)
                                .integration(Integration.ARMIS)
                                .build();

        when(kafkaHeavyTaskProducerConfig.getTopic()).thenReturn(HEAVY_TOPIC);
        when(taskManagerConfiguration.getKafkaHeavyTaskProducerConfig()).thenReturn(kafkaHeavyTaskProducerConfig);

        RuntimeException kafkaException = new RuntimeException("Kafka send error");
        when(objectMapper.writeValueAsString(task)).thenReturn(TASK_JSON);
        when(kafkaSender.send(any())).thenReturn(Flux.error(kafkaException));

        StepVerifier.create(taskSenderService.sendTask(task))
                    .expectErrorMatches(throwable -> throwable == kafkaException)
                    .verify();

        verify(kafkaSender).send(any());
        verify(objectMapper).writeValueAsString(task);
    }

}

