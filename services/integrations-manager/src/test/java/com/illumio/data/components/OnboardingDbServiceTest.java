package com.illumio.data.components;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.*;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.mapper.TenantOnboardingMapper;
import com.illumio.data.model.mapper.TenantOnboardingRequestMapper;
import com.illumio.data.model.wiz.WizTenantConfigurations;
import com.illumio.data.repositories.OnboardingRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Collections;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class OnboardingDbServiceTest {

    @Mock private OnboardingRepository onboardingRepository;

    private OnboardingDbService onboardingDbService;

    private TenantIntegrationPair tenantIntegrationPair;
    private TenantOnboardingRequest tenantOnboardingRequest;
    private TenantOnboardingEntity tenantOnboardingEntity;
    private TenantOnboardingEntity tenantOnboardingEntity2;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String credentialsVaultPath = "secret/path/to/credentials";

    private static final WizTenantConfigurations WIZ_TENANT_CONFIGS = WizTenantConfigurations.builder()
                                                                                             .authUrl("https://auth.app.wiz.io/oauth/token")
                                                                                             .apiUrl("https://api.us41.app.wiz.io/graphql")
                                                                                             .build();
    private static final TenantMetadata WIZ_TENANT_METADATA = WizTenantMetadata.builder()
                                                                               .cloudSecureTenantId("ea20bb4e-d76f-4fc4-86ba-48dcc86db9da")
                                                                               .build();

    @BeforeEach
    void setUp() {
        onboardingDbService = new OnboardingDbService(onboardingRepository, objectMapper);

        tenantIntegrationPair = new TenantIntegrationPair("tenant123", Integration.WIZ);

        tenantOnboardingRequest = new TenantOnboardingRequest();
        tenantOnboardingRequest.setTenantId("tenant123");
        tenantOnboardingRequest.setIntegration(Integration.WIZ);
        tenantOnboardingRequest.setConfigurations(WIZ_TENANT_CONFIGS);
        tenantOnboardingRequest.setTenantMetadata(WIZ_TENANT_METADATA);

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> tenantMetadataMap = objectMapper.convertValue(WIZ_TENANT_METADATA, new TypeReference<>() {});
        tenantOnboardingEntity = new TenantOnboardingEntity(
                "tenant123",
                Integration.WIZ,
                credentialsVaultPath,
                WIZ_TENANT_CONFIGS,
                tenantMetadataMap
        );

        tenantOnboardingEntity2 = new TenantOnboardingEntity(
                "tenant123",
                Integration.ARMIS,
                credentialsVaultPath,
                WIZ_TENANT_CONFIGS,
                Collections.emptyMap()
        );

    }

    @Test
    void isEligibleToOnboard_WhenEligible_ShouldReturnTrue() {
        // Arrange
        when(onboardingRepository.isTenantOnboarded(
                tenantIntegrationPair.getTenantId(),
                tenantIntegrationPair.getIntegration()))
                .thenReturn(Mono.just(true));

        // Act & Assert
        StepVerifier.create(onboardingDbService.isTenantOnboarded(tenantIntegrationPair))
                .expectNext(true)
                .verifyComplete();

        verify(onboardingRepository).isTenantOnboarded(
                tenantIntegrationPair.getTenantId(),
                tenantIntegrationPair.getIntegration());
    }

    @Test
    void isEligibleToOnboard_WhenNotEligible_ShouldReturnFalse() {
        // Arrange
        when(onboardingRepository.isTenantOnboarded(
                tenantIntegrationPair.getTenantId(),
                tenantIntegrationPair.getIntegration()))
                .thenReturn(Mono.just(false));

        // Act & Assert
        StepVerifier.create(onboardingDbService.isTenantOnboarded(tenantIntegrationPair))
                .expectNext(false)
                .verifyComplete();

        verify(onboardingRepository).isTenantOnboarded(
                tenantIntegrationPair.getTenantId(),
                tenantIntegrationPair.getIntegration());
    }

    @Test
    void saveTenantOnboarding_ShouldSaveEntityAndReturnVoid() {
        // Arrange
        when(onboardingRepository.save(any(TenantOnboardingEntity.class)))
                .thenReturn(Mono.just(tenantOnboardingEntity));

        // Act & Assert
        StepVerifier.create(onboardingDbService.saveTenantOnboarding(tenantOnboardingRequest, credentialsVaultPath))
                    .verifyComplete();

        ArgumentCaptor<TenantOnboardingEntity> captor = ArgumentCaptor.forClass(TenantOnboardingEntity.class);
        verify(onboardingRepository).save(captor.capture());

        TenantOnboardingEntity savedEntity = captor.getValue();
        assertEquals(TenantOnboardingRequestMapper.toTenantOnboarding(tenantOnboardingRequest, credentialsVaultPath, new ObjectMapper()),
                savedEntity);
    }



    @Test
    void saveTenantOnboarding_WhenRepositoryFails_ShouldPropagateError() {
        // Arrange
        RuntimeException testException = new RuntimeException("Test error");
        when(onboardingRepository.save(any(TenantOnboardingEntity.class)))
                .thenReturn(Mono.error(testException));

        // Act & Assert
        StepVerifier.create(onboardingDbService.saveTenantOnboarding(tenantOnboardingRequest, credentialsVaultPath))
                    .expectError(RuntimeException.class)
                    .verify();

        verify(onboardingRepository, times(1)).save(any(TenantOnboardingEntity.class));
    }


    @Test
    void fetchTenantOnboarding_success() {
        when(onboardingRepository.findByTenantId(tenantOnboardingRequest.getTenantId()))
                .thenReturn(Flux.just(tenantOnboardingEntity, tenantOnboardingEntity2));

        StepVerifier.create(onboardingDbService.fetchTenantOnboardings(tenantOnboardingRequest.getTenantId()))
                .expectNextMatches(next -> next.equals(TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity, objectMapper)))
                .expectNextMatches(next -> next.equals(TenantOnboardingMapper.toTenantOnboarding(tenantOnboardingEntity2, objectMapper)))
                .verifyComplete();

        verify(onboardingRepository).findByTenantId(eq(tenantOnboardingEntity.getTenantId()));
    }

    @Test
    void fetchTenantOnboarding_propagatesError() {
        when(onboardingRepository.findByTenantId(tenantOnboardingRequest.getTenantId()))
                .thenReturn(Flux.error(new RuntimeException("Error")));

        StepVerifier.create(onboardingDbService.fetchTenantOnboardings(tenantOnboardingRequest.getTenantId()))
                    .expectError()
                    .verify();

        verify(onboardingRepository).findByTenantId(eq(tenantOnboardingEntity.getTenantId()));
    }

}

