package com.illumio.data.components;

import com.illumio.data.configuration.IntegrationsManagerConfig;
import com.illumio.data.exception.BadRequestException;
import com.illumio.data.model.*;
import com.illumio.data.util.AuthCommonConstants;
import com.illumio.data.model.Credentials;
import com.illumio.data.model.IntegrationCredentials;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TenantOnboardingRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantOnboardingServiceTest {

    @Mock private TenantValidationService tenantValidationService;
    @Mock private CredentialsService credentialsService;
    @Mock private TaskSenderService taskSenderService;
    @Mock private OnboardingDbService onboardingDbService;
    @Mock private Credentials credentials;
    @Mock private IntegrationsManagerConfig integrationsManagerConfig;
    @Mock private IntegrationsManagerConfig.RetryConfig retryConfig;

    @InjectMocks private TenantOnboardingService tenantOnboardingService;

    private TenantOnboardingRequest tenantOnboardingRequest;
    private static final String WIZ_VAULT_PATH =
            AuthCommonConstants.getCredentialsVaultPath( "TENANT", Integration.WIZ);


    @BeforeEach
    void setUp() {
        tenantOnboardingRequest = new TenantOnboardingRequest();
        tenantOnboardingRequest.setTenantId("test-tenant");
        tenantOnboardingRequest.setIntegration(Integration.WIZ);

        credentials.setClientId("test-client-id");
        credentials.setSecret("test-secret");
        tenantOnboardingRequest.setCredentials(credentials);
    }

    @Test
    void onboardTenant_WhenValidationSucceeds_ShouldCompleteSuccessfully() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class),  anyBoolean()))
                .thenReturn(Mono.just(true));

        when(credentialsService.storeSensitiveDetails(any(IntegrationCredentials.class)))
                .thenReturn(Mono.just(WIZ_VAULT_PATH));

        when(taskSenderService.sendTask(any(SyncTask.class)))
                .thenReturn(Mono.empty());

        when(onboardingDbService.saveTenantOnboarding(
                eq(tenantOnboardingRequest),
                anyString()
        )).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, true))
                .verifyComplete();

        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, true);
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verify(onboardingDbService).saveTenantOnboarding(eq(tenantOnboardingRequest), anyString());
        ArgumentCaptor<SyncTask> syncTaskCaptor = ArgumentCaptor.forClass(SyncTask.class);
        verify(taskSenderService).sendTask(syncTaskCaptor.capture());
        SyncTask capturedSyncTask = syncTaskCaptor.getValue();
        assertThat(capturedSyncTask).isNotNull();
        assertThat(capturedSyncTask.getTenantId()).isEqualTo(tenantOnboardingRequest.getTenantId());
        assertThat(capturedSyncTask.getIntegration()).isEqualTo(tenantOnboardingRequest.getIntegration());
        assertThat(capturedSyncTask.getTaskType()).isEqualTo(TaskType.GREENFIELD);

}

    @Test
    void patchOnboardTenant_WhenValidationSucceeds_ShouldCompleteSuccessfully() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class),  anyBoolean()))
                .thenReturn(Mono.just(true));

        when(credentialsService.storeSensitiveDetails(any(IntegrationCredentials.class)))
                .thenReturn(Mono.just(WIZ_VAULT_PATH));

        when(onboardingDbService.saveTenantOnboarding(
                eq(tenantOnboardingRequest),
                anyString()
        )).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, Boolean.FALSE))
                    .verifyComplete();

        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.FALSE);
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verify(onboardingDbService).saveTenantOnboarding(eq(tenantOnboardingRequest), anyString());

    }

    @Test
    void onboardTenant_WhenValidationFails_ShouldCompleteWithoutProcessing() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class), anyBoolean()))
                .thenReturn(Mono.error(new BadRequestException("Invalid onboarding")));

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, Boolean.TRUE))
                .verifyError(BadRequestException.class);
        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE);
        verifyNoInteractions(credentialsService);
        verifyNoInteractions(onboardingDbService);
        verifyNoInteractions(taskSenderService);
    }

    @Test
    void onboardTenant_WhenCredentialsServiceFails_ShouldPropagateError() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class), anyBoolean()))
                .thenReturn(Mono.just(true));

        RuntimeException testException = new RuntimeException("Credentials service error");
        when(credentialsService.storeSensitiveDetails(any(IntegrationCredentials.class)))
                .thenReturn(Mono.error(testException));

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, Boolean.TRUE))
                .expectErrorMatches(error -> error instanceof RuntimeException &&
                        "Credentials service error".equals(error.getMessage()))
                .verify();

        // Verify interactions
        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE);
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verifyNoMoreInteractions(credentialsService);
        verifyNoInteractions(onboardingDbService);
        verifyNoInteractions(taskSenderService);
    }

    @Test
    void onboardTenant_WhenDbServiceFails_ShouldPropagateError() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class), anyBoolean()))
                .thenReturn(Mono.just(true));

        when(credentialsService.storeSensitiveDetails(any(IntegrationCredentials.class)))
                .thenReturn(Mono.just(WIZ_VAULT_PATH));

        when(taskSenderService.sendTask(any(SyncTask.class)))
                .thenReturn(Mono.empty());

        RuntimeException testException = new RuntimeException("DB service error");
        when(onboardingDbService.saveTenantOnboarding(any(), anyString()))
                .thenReturn(Mono.error(testException));

        when(credentialsService.deleteSensitiveDetails(eq(WIZ_VAULT_PATH))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, Boolean.TRUE))
                .expectErrorMatches(error -> error instanceof RuntimeException &&
                        "DB service error".equals(error.getMessage()))
                .verify();
        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE);
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verify(taskSenderService).sendTask(any(SyncTask.class));
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verify(onboardingDbService).saveTenantOnboarding(any(), anyString());
        verify(credentialsService).deleteSensitiveDetails(eq(WIZ_VAULT_PATH));
    }

    @Test
    void onboardTenant_WhenHeavyTaskSenderFails_ShouldPropagateError() {
        // Arrange
        when(tenantValidationService.validateTenantOnboardingRequest(any(TenantOnboardingRequest.class), anyBoolean()))
                .thenReturn(Mono.just(true));

        when(credentialsService.storeSensitiveDetails(any(IntegrationCredentials.class)))
                .thenReturn(Mono.just(WIZ_VAULT_PATH));

        RuntimeException testException = new RuntimeException("Kafka sender error");
        when(taskSenderService.sendTask(any(SyncTask.class)))
                .thenReturn(Mono.error(testException));

        when(credentialsService.deleteSensitiveDetails(eq(WIZ_VAULT_PATH))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(tenantOnboardingService.onboardTenant(tenantOnboardingRequest, Boolean.TRUE))
                .expectErrorMatches(error -> error instanceof RuntimeException &&
                        "Kafka sender error".equals(error.getMessage()))
                .verify();
        verify(tenantValidationService).validateTenantOnboardingRequest(tenantOnboardingRequest, Boolean.TRUE);
        verify(credentialsService).storeSensitiveDetails(any(IntegrationCredentials.class));
        verify(taskSenderService).sendTask(any(SyncTask.class));
        verify(credentialsService).deleteSensitiveDetails(eq(WIZ_VAULT_PATH));
        verifyNoInteractions(onboardingDbService);
    }

}
