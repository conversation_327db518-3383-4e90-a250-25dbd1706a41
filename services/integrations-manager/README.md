# Integrations Manager

The **Integrations Manager** allows for managing of integrationSQLEntity configurations as well as managing tenant-integration configurations.
---

## Running Locally

### 1. Modify `application-local-eventhub.yml`
Ensure the following configurations are set:

1. **Azure Cosmos Config** - Cosmos Database containing Integrations and Onboarding tables
2. **Vault Config** - Customer Secrets Vault
3. **Kafka Producer Config** – Kafka Producer writing to Heavy Task Queue

### 2. Run Locally Using IntelliJ

To run the application from within IntelliJ, use the following run configuration:

- **Run Configuration**: `integrations-manager/IntegrationsManagerApplication+LocalEventHub`