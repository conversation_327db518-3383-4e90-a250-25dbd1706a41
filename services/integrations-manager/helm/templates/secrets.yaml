apiVersion: v1
kind: Secret
metadata:
  name: {{ include "IntegrationsManager.fullname" . }}-env-secrets
  labels:
    {{- include "IntegrationsManager.labels" . | nindent 4 }}
type: Opaque
stringData:
  SPRING_CLOUD_AZURE_COSMOS_KEY: {{ .Values.spring.cloud.azure.cosmos.key }}
  SPRING_R2DBC_PASSWORD: {{ .Values.spring.r2dbc.password }}
  VAULT_TOKEN: {{ .Values.vault.token }}
  LAUNCHDARKLY_SDKKEY: {{ .Values.launchDarkly.sdkKey }}
  INTEGRATIONSMANAGER_KAFKALIGHTTASKPRODUCERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.integrationsManager.kafkaLightTaskProducerConfig.connectionString }}";
  INTEGRATIONSMANAGER_KAFKAHEAVYTASKPRODUCERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.integrationsManager.kafkaHeavyTaskProducerConfig.connectionString }}";