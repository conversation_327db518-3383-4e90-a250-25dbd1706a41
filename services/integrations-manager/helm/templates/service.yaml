apiVersion: v1
kind: Service
metadata:
  name: {{ include "IntegrationsManager.name" . }}
  labels:
    {{- include "IntegrationsManager.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "IntegrationsManager.selectorLabels" . | nindent 4 }}
