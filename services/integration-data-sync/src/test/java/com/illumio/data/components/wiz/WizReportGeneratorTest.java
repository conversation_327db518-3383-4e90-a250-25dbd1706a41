package com.illumio.data.components.wiz;

import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.Integration;
import com.illumio.data.model.wiz.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.*;

import static com.illumio.data.util.DataSyncConstants.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WizReportGeneratorTest {

    @Mock private WebClient webClient;
    @Mock private WebClient.RequestBodyUriSpec requestBodyUriSpec;
    @Mock private WebClient.RequestBodySpec requestBodySpec;
    @Mock private WebClient.RequestHeadersSpec requestHeadersSpec;
    @Mock private WebClient.ResponseSpec responseSpec;

    @InjectMocks private WizReportGenerator wizReportGenerator;

    private static final String REPORT_ID = "1cbff9e1-bc58-427f-8b24-6b8df7db962d";
    private static final String PROJECT_ID = "87bd90a6-ed0e-519f-89a2-9e84814810e4";
    private static final WizCreateReportResponse CREATE_REPORT_RESPONSE = WizCreateReportResponse
            .builder()
            .data(WizCreateReportResponseData.builder().createReport(
                                                     WizCreateReportResponseCreatedReport.builder().report(
                                                                                                 WizCreateReportResponseReport.builder()
                                                                                                                              .id(REPORT_ID)
                                                                                                                              .build())
                                                                                         .build())
                                             .build())
            .build();
    private static final String WIZ_API_URL = "https://api.us41.app.wiz.io/graphql";

    @BeforeEach
    void setUp() {
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(any(String.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.header(any(String.class), any(String.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(any(Map.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
    }

    @Test
    void testStartReportGeneration_Vulnerabilities_withProjectIdScoped_Success() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class)).thenReturn(Mono.just(CREATE_REPORT_RESPONSE));

        final WizDataPullTask dataPullTask =
                dataPullTask(FindingType.VULNERABILITY, PROJECT_ID);
        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask))
                    .expectNext(reportIdentifier(REPORT_ID, FindingType.VULNERABILITY))
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec).bodyValue(argThat(body -> {
            final Map<String, Object> queryVars =
                    (Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) body)
                            .get(REQUEST_BODY_VAR_VARIABLES))
                            .get(QUERY_VAR_REPORT_PARAMS);
            return StringUtils.isNotBlank((String) queryVars.get(QUERY_VAR_REPORT_NAME))
                    && queryVars.get(QUERY_VAR_REPORT_TYPE).equals(WIZ_REPORT_TYPE_VULNERABILITIES)
                    && queryVars.get(QUERY_VAR_REPORT_FORMAT).equals(QUERY_VAL_REPORT_FORMAT)
                    && queryVars.get(QUERY_VAR_REPORT_INCREMENTAL).equals(false)
                    && queryVars.get(QUERY_VAR_PROJECT_ID).equals(PROJECT_ID)
                    && queryVars.get(QUERY_VAR_REPORT_COLUMNS).equals(WIZ_VULNERABILITY_REPORT_COLUMNS)
                    && !queryVars.containsKey(QUERY_VAR_REPORT_ISSUE_PARAMS);
        }));
        verify(requestBodySpec).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testStartReportGeneration_Issues_withProjectIdScoped_Success() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class)).thenReturn(Mono.just(CREATE_REPORT_RESPONSE));

        final WizDataPullTask dataPullTask =
                dataPullTask(FindingType.ISSUE, PROJECT_ID);
        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask))
                    .expectNext(reportIdentifier(REPORT_ID, FindingType.ISSUE))
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec).bodyValue(argThat(body -> {
            final Map<String, Object> queryVars =
                    (Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) body)
                            .get(REQUEST_BODY_VAR_VARIABLES))
                            .get(QUERY_VAR_REPORT_PARAMS);
            return StringUtils.isNotBlank((String) queryVars.get(QUERY_VAR_REPORT_NAME))
                    && queryVars.get(QUERY_VAR_REPORT_TYPE).equals(WIZ_REPORT_TYPE_ISSUES)
                    && queryVars.get(QUERY_VAR_REPORT_FORMAT).equals(QUERY_VAL_REPORT_FORMAT)
                    && queryVars.get(QUERY_VAR_REPORT_INCREMENTAL).equals(false)
                    && queryVars.get(QUERY_VAR_PROJECT_ID).equals(PROJECT_ID)
                    && queryVars.get(QUERY_VAR_REPORT_ISSUE_PARAMS)
                                .equals(Collections.singletonMap(QUERY_VAR_REPORT_TYPE, QUERY_VAL_REPORT_ISSUE_PARAMS_TYPE))
                    && !queryVars.containsKey(QUERY_VAR_REPORT_COLUMNS);
        }));
        verify(requestBodySpec).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testStartReportGeneration_Vulnerabilities_noProjectIdScope_Success() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class)).thenReturn(Mono.just(CREATE_REPORT_RESPONSE));

        final WizDataPullTask dataPullTask =
                dataPullTask(FindingType.VULNERABILITY, null);
        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask))
                    .expectNext(reportIdentifier(REPORT_ID, FindingType.VULNERABILITY))
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec).bodyValue(argThat(body -> {
            final Map<String, Object> queryVars =
                    (Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) body)
                            .get(REQUEST_BODY_VAR_VARIABLES))
                            .get(QUERY_VAR_REPORT_PARAMS);
            return StringUtils.isNotBlank((String) queryVars.get(QUERY_VAR_REPORT_NAME))
                    && queryVars.get(QUERY_VAR_REPORT_TYPE).equals(WIZ_REPORT_TYPE_VULNERABILITIES)
                    && queryVars.get(QUERY_VAR_REPORT_FORMAT).equals(QUERY_VAL_REPORT_FORMAT)
                    && queryVars.get(QUERY_VAR_REPORT_INCREMENTAL).equals(false)
                    && queryVars.get(QUERY_VAR_REPORT_COLUMNS).equals(WIZ_VULNERABILITY_REPORT_COLUMNS)
                    && !queryVars.containsKey(QUERY_VAR_REPORT_ISSUE_PARAMS);
        }));
        verify(requestBodySpec).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testStartReportGeneration_Issues_noProjectIdScope_Success() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class)).thenReturn(Mono.just(CREATE_REPORT_RESPONSE));

        final WizDataPullTask dataPullTask =
                dataPullTask(FindingType.ISSUE, null);
        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask))
                    .expectNext(reportIdentifier(REPORT_ID, FindingType.ISSUE))
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec).bodyValue(argThat(body -> {
            final Map<String, Object> queryVars =
                    (Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) body)
                            .get(REQUEST_BODY_VAR_VARIABLES))
                            .get(QUERY_VAR_REPORT_PARAMS);
            return StringUtils.isNotBlank((String) queryVars.get(QUERY_VAR_REPORT_NAME))
                    && queryVars.get(QUERY_VAR_REPORT_TYPE).equals(WIZ_REPORT_TYPE_ISSUES)
                    && queryVars.get(QUERY_VAR_REPORT_FORMAT).equals(QUERY_VAL_REPORT_FORMAT)
                    && queryVars.get(QUERY_VAR_REPORT_INCREMENTAL).equals(false)
                    && queryVars.get(QUERY_VAR_REPORT_ISSUE_PARAMS)
                                .equals(Collections.singletonMap(QUERY_VAR_REPORT_TYPE, QUERY_VAL_REPORT_ISSUE_PARAMS_TYPE))
                    && !queryVars.containsKey(QUERY_VAR_REPORT_COLUMNS);
        }));
        verify(requestBodySpec).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testStartReportGeneration_Failure() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class))
                .thenReturn(Mono.error(new ExternalServiceException("API Failure")));

        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask(FindingType.VULNERABILITY, null)))
                    .expectErrorMatches(throwable -> throwable instanceof ExternalServiceException
                            && throwable.getMessage().equals("API Failure"))
                    .verify();
    }

    @Test
    void testStartReportGeneration_errorsInResponse() {
        when(responseSpec.bodyToMono(WizCreateReportResponse.class))
                .thenReturn(Mono.just(WizCreateReportResponse.builder()
                                                             .data(null)
                                                             .errors(List.of(
                                                                     WizError.builder().message("err1").build(),
                                                                     WizError.builder().message("err2").build()))
                                                             .build()));

        StepVerifier.create(wizReportGenerator.startReportGeneration(dataPullTask(FindingType.VULNERABILITY, null)))
                    .expectErrorMatches(throwable -> throwable instanceof ExternalServiceException
                            && throwable.getMessage().equals("Wiz error(s): err1, err2"))
                    .verify();
    }

    private WizDataPullTask dataPullTask(final FindingType findingType, final String projectId) {
        return WizDataPullTask.builder()
                              .integration(Integration.WIZ)
                              .findingType(findingType)
                              .projectId(projectId)
                              .apiUrl(WIZ_API_URL)
                              .authToken("test-token")
                              .build();
    }

    private WizReportIdentifier reportIdentifier(final String reportId, final FindingType findingType) {
        return WizReportIdentifier.builder()
                                  .findingType(findingType)
                                  .reportId(reportId)
                                  .build();
    }

}
