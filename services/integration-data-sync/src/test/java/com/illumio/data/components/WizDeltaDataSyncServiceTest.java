package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.components.producer.FindingsSenderService;
import com.illumio.data.components.wiz.WizDeltaDataPuller;

import com.illumio.data.components.wiz.WizDeltaDataSyncService;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.ArgumentMatchers.any;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@Slf4j
@ExtendWith(MockitoExtension.class)
class WizDeltaDataSyncServiceTest {

  @Mock private TokenService tokenService;
  @Mock private WizDeltaDataPuller wizDeltaDataPuller;
  @Mock private FindingsSenderService findingsSenderService;
  @Mock private TaskStatusSenderService taskStatusSenderService;
  @Mock private DataSyncFailureHandler dataSyncFailureHandler;
  @Mock private ObjectMapper objectMapper;

  @InjectMocks private WizDeltaDataSyncService wizDeltaDataSyncService;

  private SyncTask syncTask;
  private WizTokenResponse tokenResponse;
  private Token token = new Token();
  private WizResource resource;
  private WizVulnerabilityFinding wizVulnerabilityFinding;
  private WizIssueFinding wizIssueFinding;
  private Vulnerability expectedVulnerability;
  private Issue expectedIssue;
  private String expectedSuccessMessage;

  @Mock
  private Runnable mockRunnable;

  @BeforeEach
  void setUp() {
      when(dataSyncFailureHandler.getRetrySpec(any())).thenReturn(Retry.backoff(2, Duration.ofMillis(1)));

    syncTask = SyncTask.builder()
                       .taskId("1")
                       .integration(Integration.WIZ)
                       .tenantId("tenant-1")
                       .credentialsVaultPath("vault-path")
                       .taskType(TaskType.DELTA)
                       .build();
    tokenResponse = new WizTokenResponse();
    tokenResponse.setAccessToken("access-token");
    tokenResponse.setRefreshToken("refresh-token");
    tokenResponse.setTokenType("token-type");
    tokenResponse.setExpiresIn(6000000);
      token.setAccessToken(tokenResponse.getAccessToken());


      resource = new WizResource();
    resource.setName("vulnerable-asset");
    resource.setCloudPlatform("AWS");
    resource.setType("vulnerable-asset-type");
    resource.setProviderUniqueId("provider-unique-id");
    resource.setSubscriptionExternalId("subscription-external-id");

    wizVulnerabilityFinding = new WizVulnerabilityFinding();
    wizVulnerabilityFinding.setResource(resource);
    wizVulnerabilityFinding.setCveDescription("description");
    wizVulnerabilityFinding.setCveName("cve-name");
    wizVulnerabilityFinding.setCvssSeverity("severity");
    wizVulnerabilityFinding.setStatus("status");
    wizVulnerabilityFinding.setScore(9.1);
    wizVulnerabilityFinding.setExploitabilityScore(8.4);
    wizVulnerabilityFinding.setImpactScore(7.7);
    wizVulnerabilityFinding.setHasExploit(true);
    wizVulnerabilityFinding.setHasCisaKevExploit(true);

    wizIssueFinding = WizIssueFinding.builder()
                                     .id("ffcd8d33-1b9f-4684-b6cf-83c8ec745a3e")
                                     .status("OPEN")
                                     .severity("INFORMATIONAL")
                                     .sourceRules(List.of(
                    WizSourceRule.builder()
                                 .name("GuardDuty is not enabled in an active region")
                                 .risks(new String[] { "RELIABILITY_IMPACT" })
                                 .build(),
                    WizSourceRule.builder()
                                 .name("Publicly exposed control plane of an EKS cluster")
                                 .risks(new String[] { "EXTERNAL_EXPOSURE", "INSECURE_KUBERNETES_CLUSTER" })
                                 .build()
            ))
                                     .resource(WizResource.builder()
                                       .name("us-east-1 (211125710713)")
                                       .type("REGION")
                                       .providerUniqueId("211125710713/us-east-1")
                                       .subscriptionExternalId("211125710713")
                                       .cloudPlatform("AWS")
                                       .cloudProviderURL("")
                                       .build())
                                     .build();

    expectedVulnerability =
            Vulnerability.builder()
                         .tenantId(syncTask.getTenantId())
                         .integration(syncTask.getIntegration())
                         .status(wizVulnerabilityFinding.getStatus())
                         .resource(FindingResource.builder()
                                                  .name(wizVulnerabilityFinding.getResource().getName())
                                                  .type(wizVulnerabilityFinding.getResource().getType())
                                                  .cloudPlatform(wizVulnerabilityFinding.getResource().getCloudPlatform())
                                                  .subscriptionId(wizVulnerabilityFinding.getResource().getSubscriptionExternalId())
                                                  .cloudProviderUniqueId(wizVulnerabilityFinding.getResource().getProviderUniqueId())
                                                  .build())
                         .metadata(VulnerabilityMetadata.builder()
                                                        .cveName(wizVulnerabilityFinding.getCveName())
                                                        .cveDescription(wizVulnerabilityFinding.getCveDescription())
                                                        .severity(wizVulnerabilityFinding.getSeverity())
                                                        .cvssSeverity(wizVulnerabilityFinding.getCvssSeverity())
                                                        .score(wizVulnerabilityFinding.getScore())
                                                        .exploitabilityScore(wizVulnerabilityFinding.getExploitabilityScore())
                                                        .impactScore(wizVulnerabilityFinding.getImpactScore())
                                                        .firstDetectedAt(wizVulnerabilityFinding.getFirstDetectedAt())
                                                        .lastDetectedAt(wizVulnerabilityFinding.getLastDetectedAt())
                                                        .hasExploit(wizVulnerabilityFinding.getHasExploit())
                                                        .hasCisaKevExploit(wizVulnerabilityFinding.getHasCisaKevExploit())
                                                        .vulnerableComponentName(wizVulnerabilityFinding.getVulnerableComponentName())
                                                        .build())
                                         .build();

    expectedIssue = Issue.builder()
                         .id(wizIssueFinding.getId())
                         .tenantId(syncTask.getTenantId())
                         .integration(syncTask.getIntegration())
                         .status(wizIssueFinding.getStatus())
                         .resource(
                                 FindingResource.builder()
                                                .name(wizIssueFinding.getResource().getName())
                                                .type(wizIssueFinding.getResource().getType())
                                                .cloudProviderUniqueId(wizIssueFinding.getResource().getProviderUniqueId())
                                                .subscriptionId(wizIssueFinding.getResource().getSubscriptionExternalId())
                                                .cloudPlatform(wizIssueFinding.getResource().getCloudPlatform())
                                                .build()
                         )
                         .metadata(IssueMetadata.builder()
                                                .sourceRuleNames(wizIssueFinding.getSourceRules().get(0).getName()
                                                        + ", " + wizIssueFinding.getSourceRules().get(1).getName())
                                                .severity(wizIssueFinding.getSeverity())
                                                .risks(Stream.concat(
                                                        Arrays.stream(wizIssueFinding.getSourceRules().get(0).getRisks()),
                                                        Arrays.stream(wizIssueFinding.getSourceRules().get(1).getRisks()))
                                                             .toList())
                                                .build())
                         .build();

    expectedSuccessMessage = "Success";
  }

  @Test
  void handleSyncTask_shouldSendFindingsAndStatusMessages_AWS() {
    // Mock the dependencies
      setUpSuccessFlowProps();
      when(tokenService.getAuthToken(eq(syncTask))).thenReturn(Mono.just(token));
    when(wizDeltaDataPuller.pullData(any()))
            .thenReturn(Flux.just(wizVulnerabilityFinding))
            .thenReturn(Flux.just(wizIssueFinding))
            .thenReturn(Flux.just(wizVulnerabilityFinding))
            .thenReturn(Flux.just(wizIssueFinding));
    when(findingsSenderService.sendFindings(any())).thenReturn(Mono.empty());
    when(taskStatusSenderService.sendTaskStatusMessage(any(), any(), any())).thenReturn(Mono.empty());

    // Execute the method
    StepVerifier.create(wizDeltaDataSyncService.handleSyncTask(syncTask,mockRunnable)).verifyComplete();

    // Verify interactions
    ArgumentCaptor<Flux<Finding>> fluxCaptor = ArgumentCaptor.forClass(Flux.class);
    verify(findingsSenderService).sendFindings(fluxCaptor.capture());

    Flux<Finding> capturedFlux = fluxCaptor.getValue();

    StepVerifier.create(capturedFlux.collectList())
                .assertNext(findings -> {
                    assertThat(findings).containsExactlyInAnyOrder(expectedIssue, expectedVulnerability);
                  })
                  .verifyComplete();

    verify(tokenService).getAuthToken(eq(syncTask));
    verify(wizDeltaDataPuller, times(2)).pullData(any());
    verify(findingsSenderService).sendFindings(any());
    verifyTaskStatusMessagesForSuccessFlow();
    verify(mockRunnable).run();
  }


    @Test
    void handleSyncTask_shouldSendFindingsAndStatusMessages_Azure() {
        String cloudPlatform = "Azure";
        String cloudResourceId = "/subscriptions/a057717b-0fd9-4818-a2fb-ea5078e476ce/resourcegroups/autoupdate/providers/microsoft.compute/virtualmachines/update1";
        String cloudProviderUrl = "https://portal.azure.com/#@azuredemoillum.onmicrosoft.com/resource/" + cloudResourceId;
        wizVulnerabilityFinding.getResource().setCloudPlatform(cloudPlatform);
        wizVulnerabilityFinding.getResource().setCloudProviderURL(cloudProviderUrl);
        wizIssueFinding.getResource().setCloudPlatform(cloudPlatform);
        wizIssueFinding.getResource().setCloudProviderURL(cloudProviderUrl);

        FindingResource expectedVulnerabilityResource = expectedVulnerability.getResource();
        expectedVulnerabilityResource.setCloudPlatform(cloudPlatform);
        expectedVulnerabilityResource.setCloudProviderUniqueId(cloudResourceId);
        expectedVulnerability.setResource(expectedVulnerability.getResource());

        FindingResource expectedIssueResource = expectedIssue.getResource();
        expectedIssueResource.setCloudPlatform(cloudPlatform);
        expectedIssueResource.setCloudProviderUniqueId(cloudResourceId);
        expectedIssue.setResource(expectedIssue.getResource());

        setUpSuccessFlowProps();
        when(tokenService.getAuthToken(eq(syncTask))).thenReturn(Mono.just(token));
        when(wizDeltaDataPuller.pullData(any()))
                .thenReturn(Flux.just(wizVulnerabilityFinding))
                .thenReturn(Flux.just(wizIssueFinding));
        when(findingsSenderService.sendFindings(any())).thenReturn(Mono.empty());
        when(taskStatusSenderService.sendTaskStatusMessage(any(), any(), any())).thenReturn(Mono.empty());

        // Execute the method
        StepVerifier.create(wizDeltaDataSyncService.handleSyncTask(syncTask,mockRunnable)).verifyComplete();

        // Verify interactions
        ArgumentCaptor<Flux<Finding>> fluxCaptor = ArgumentCaptor.forClass(Flux.class);
        verify(findingsSenderService).sendFindings(fluxCaptor.capture());

        Flux<Finding> capturedFlux = fluxCaptor.getValue();
        StepVerifier.create(capturedFlux.collectList())
                    .assertNext(findings -> {
                        assertThat(findings).containsExactlyInAnyOrder(expectedIssue, expectedVulnerability);
                    })
                    .verifyComplete();

        verify(tokenService).getAuthToken(eq(syncTask));
        verify(wizDeltaDataPuller, times(2)).pullData(any());
        verify(findingsSenderService).sendFindings(any());
        // TODO: verify using success props in both cases
        verifyTaskStatusMessagesForSuccessFlow();
        verify(mockRunnable).run();
    }


    @Test
    void handleSyncTask_shouldPropagateErrorOnDataPullFailure() {
        String cloudPlatform = "Azure";
        String cloudResourceId = "/subscriptions/a057717b-0fd9-4818-a2fb-ea5078e476ce/resourcegroups/autoupdate/providers/microsoft.compute/virtualmachines/update1";
        String cloudProviderUrl = "https://portal.azure.com/#@azuredemoillum.onmicrosoft.com/resource/" + cloudResourceId;
        wizVulnerabilityFinding.getResource().setCloudPlatform(cloudPlatform);
        wizVulnerabilityFinding.getResource().setCloudProviderURL(cloudProviderUrl);
        wizIssueFinding.getResource().setCloudPlatform(cloudPlatform);
        wizIssueFinding.getResource().setCloudProviderURL(cloudProviderUrl);

        FindingResource expectedVulnerabilityResource = expectedVulnerability.getResource();
        expectedVulnerabilityResource.setCloudPlatform(cloudPlatform);
        expectedVulnerabilityResource.setCloudProviderUniqueId(cloudResourceId);
        expectedVulnerability.setResource(expectedVulnerability.getResource());

        FindingResource expectedIssueResource = expectedIssue.getResource();
        expectedIssueResource.setCloudPlatform(cloudPlatform);
        expectedIssueResource.setCloudProviderUniqueId(cloudResourceId);
        expectedIssue.setResource(expectedIssue.getResource());

        // Mock the dependencies
        when(tokenService.getAuthToken(eq(syncTask))).thenReturn(Mono.just(token));
        when(wizDeltaDataPuller.pullData(any()))
                .thenReturn(Flux.error(new RuntimeException("Data pull error")));
        when(findingsSenderService.sendFindings(argThat(this::isErrorFlux))).thenReturn(Mono.error(new RuntimeException("Data pull error")));
        when(taskStatusSenderService.sendTaskStatusMessage(any(), any(), any())).thenReturn(Mono.empty());

        // Execute the method
        StepVerifier.create(wizDeltaDataSyncService.handleSyncTask(syncTask,mockRunnable))
                    .verifyError(RuntimeException.class);

        verify(tokenService).getAuthToken(syncTask);
        verify(wizDeltaDataPuller, times(2)).pullData(any());
        verify(findingsSenderService).sendFindings(any());
        verifySideEffectsForFailureFlow();
        verify(mockRunnable).run();
    }

    private boolean isErrorFlux(Flux<?> flux) {
      try {
          StepVerifier.create(flux)
                      .expectError()
                      .verify();
          return true;
      } catch (Exception ex) {
          return false;
      }
    }

    @Test
    void handleSyncTask_shouldPropagateErrorOnFindingsSendFailure() {
        // Mock the dependencies to simulate a transient error
        when(tokenService.getAuthToken(eq(syncTask))).thenReturn(Mono.just(token));
        when(findingsSenderService.sendFindings(any())).thenReturn(Mono.error(new RuntimeException("Transient error")));

        when(taskStatusSenderService.sendTaskStatusMessage(any(), any(), any())).thenReturn(Mono.empty());

        // Execute the method
        StepVerifier.create(wizDeltaDataSyncService.handleSyncTask(syncTask, mockRunnable))
                    .expectError(RuntimeException.class)
                    .verify();

        verifySideEffectsForFailureFlow();
    }

    @SneakyThrows
    private void setUpSuccessFlowProps() {
        when(objectMapper.writeValueAsString(any())).thenReturn(expectedSuccessMessage);
        // TODO: verify that expectedSuccessStatusMessage has correct number of vulnerabilities and issues
    }

    private void verifyTaskStatusMessagesForSuccessFlow() {
        InOrder inOrder = inOrder(taskStatusSenderService);

        verifyTaskStatusMessageIsSentInOrder(inOrder, TaskStatusType.STARTED, "");
        verifyTaskStatusMessageIsSentInOrder(inOrder, TaskStatusType.SUCCESS, expectedSuccessMessage);
    }

    private void verifySideEffectsForFailureFlow() {
        verify(taskStatusSenderService, times(1)).sendTaskStatusMessage(
                argThat(sentTaskStatus ->
                        sentTaskStatus.getTaskId().equals(syncTask.getTaskId())
                                && sentTaskStatus.getTenantId().equals(syncTask.getTenantId())
                                && sentTaskStatus.getIntegration().equals(syncTask.getIntegration())
                                && sentTaskStatus.getTaskType().equals(syncTask.getTaskType())),
                eq(TaskStatusType.STARTED),
                eq(""));
        verify(dataSyncFailureHandler, times(1)).getRetrySpec(eq(syncTask));
    }

    private void verifyTaskStatusMessageIsSentInOrder(InOrder inOrder, TaskStatusType taskStatusType, String statusMessage) {
        inOrder.verify(taskStatusSenderService, times(1)).sendTaskStatusMessage(argThat(sentTaskStatus ->
                sentTaskStatus.getTaskId().equals(syncTask.getTaskId())
                        && sentTaskStatus.getTenantId().equals(syncTask.getTenantId())
                        && sentTaskStatus.getIntegration().equals(syncTask.getIntegration())
                        && sentTaskStatus.getTaskType().equals(syncTask.getTaskType())),
                eq(taskStatusType),
                eq(statusMessage));
    }

}