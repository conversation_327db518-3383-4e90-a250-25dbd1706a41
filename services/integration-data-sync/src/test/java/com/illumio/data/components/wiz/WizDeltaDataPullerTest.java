package com.illumio.data.components.wiz;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.wiz.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WizDeltaDataPullerTest {

    @Mock private WebClient webClient;
    @Mock private WebClient.RequestBodyUriSpec requestBodyUriSpec;
    @Mock private WebClient.RequestBodySpec requestBodySpec;
    @Mock private WebClient.RequestHeadersSpec requestHeadersSpec;
    @Mock private WebClient.ResponseSpec responseSpec;
    @Mock private DataSyncConfig dataSyncConfig;

    @InjectMocks private WizDeltaDataPuller wizDeltaDataPuller;

    private final WizDataPullTask vulnerabilitiesDataPullTask = dataPullTask(FindingType.VULNERABILITY);
    private final WizDataPullTask issuesDataPullTask = dataPullTask(FindingType.ISSUE);

    private static final String WIZ_API_URL = "https://api.us41.app.wiz.io/graphql";

    @BeforeEach
    void setUp() {
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri(any(String.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.header(any(String.class), any(String.class))).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(any(Map.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Test
    void testFetchFindingsPage_Vulnerabilities_Success_onePage() {
        WizVulnerabilityFinding vulnerabilityFinding = vulnerabilitiesFinding("CVE-1");
        when(responseSpec.bodyToMono(WizVulnerabilitiesFindingsResponse.class)).thenReturn(Mono.just(mockVulnerabilitiesResponse(vulnerabilityFinding, false)));

        StepVerifier.create(wizDeltaDataPuller.pullData(vulnerabilitiesDataPullTask))
                    .expectNext(vulnerabilityFinding)
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec, times(1)).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testFetchFindingsPage_Vulnerabilities_Success_twoPages() {
        WizVulnerabilityFinding vulnerabilityFinding1 = vulnerabilitiesFinding("CVE-1");
        WizVulnerabilityFinding vulnerabilityFinding2 = vulnerabilitiesFinding("CVE-2");

        when(responseSpec.bodyToMono(WizVulnerabilitiesFindingsResponse.class))
                .thenReturn(Mono.just(mockVulnerabilitiesResponse(vulnerabilityFinding1, true)))
                .thenReturn(Mono.just(mockVulnerabilitiesResponse(vulnerabilityFinding2, false)));

        StepVerifier.create(wizDeltaDataPuller.pullData(vulnerabilitiesDataPullTask))
                    .expectNext(vulnerabilityFinding1)
                    .expectNext(vulnerabilityFinding2)
                    .verifyComplete();

        verify(requestBodyUriSpec, times(2)).uri(eq(WIZ_API_URL));
        verify(requestBodySpec, times(2)).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testFetchFindingsPage_Vulnerabilities_Failure() {
        when(responseSpec.bodyToMono(WizVulnerabilitiesFindingsResponse.class)).thenReturn(Mono.error(new RuntimeException("API Failure")));

        StepVerifier.create(wizDeltaDataPuller.pullData(vulnerabilitiesDataPullTask))
                    .expectErrorMatches(throwable -> throwable instanceof RuntimeException && throwable.getMessage().equals("API Failure"))
                    .verify();
    }

    @Test
    void testFetchFindingsPage_Vulnerabilities_EmptyResponse() {
        WizVulnerabilitiesFindingsResponse emptyResponse = new WizVulnerabilitiesFindingsResponse();
        when(responseSpec.bodyToMono(WizVulnerabilitiesFindingsResponse.class)).thenReturn(Mono.just(emptyResponse));

        StepVerifier.create(wizDeltaDataPuller.pullData(vulnerabilitiesDataPullTask))
                    .expectNextCount(0)
                    .verifyComplete();
    }

    @Test
    void testFetchFindingsPage_Issues_Success_onePage() {
        WizIssueFinding issueFinding = issueFinding("GuardDuty is not enabled in an active region", "Publicly exposed control plane of an EKS cluster");
        when(responseSpec.bodyToMono(WizIssuesFindingsResponse.class)).thenReturn(Mono.just(mockIssuesResponse(issueFinding, false)));

        StepVerifier.create(wizDeltaDataPuller.pullData(issuesDataPullTask))
                    .expectNext(issueFinding)
                    .verifyComplete();

        verify(requestBodyUriSpec).uri(eq(WIZ_API_URL));
        verify(requestBodySpec, times(1)).header(eq("Authorization"), eq("Bearer test-token"));
    }

    @Test
    void testFetchFindingsPage_Issues_Success_twoPages() {
        WizIssueFinding issueFinding1 = issueFinding("GuardDuty is not enabled in an active region", "Publicly exposed control plane of an EKS cluster");
        WizIssueFinding issueFinding2 = issueFinding("GuardDuty2 is not enabled in an active region", "Publicly exposed data plane of an EKS cluster");

        when(responseSpec.bodyToMono(WizIssuesFindingsResponse.class))
                .thenReturn(Mono.just(mockIssuesResponse(issueFinding1, true)))
                .thenReturn(Mono.just(mockIssuesResponse(issueFinding2, false)));

        StepVerifier.create(wizDeltaDataPuller.pullData(issuesDataPullTask))
                    .expectNext(issueFinding1)
                    .expectNext(issueFinding2)
                    .verifyComplete();

        verify(requestBodyUriSpec, times(2)).uri(eq(WIZ_API_URL));
        verify(requestBodySpec, times(2)).header(eq("Authorization"), eq("Bearer test-token"));
    }

    private WizDataPullTask dataPullTask(final FindingType findingType) {
        return WizDataPullTask.builder()
                              .findingType(findingType)
                              .apiUrl(WIZ_API_URL)
                              .authToken("test-token")
                              .build();
    }

    private WizVulnerabilityFinding vulnerabilitiesFinding(final String cveName) {
        return WizVulnerabilityFinding.builder()
                                      .cveName(cveName)
                                      .cveDescription("An out-of-bounds read/write vulnerability was found in e2fsprogs 1.46.5. This issue leads to a segmentation fault and possibly arbitrary code execution via a specially crafted filesystem.")
                                      .severity("MEDIUM")
                                      .cvssSeverity("HIGH")
                                      .score(7.8)
                                      .exploitabilityScore(1.8)
                                      .impactScore(5.9)
                                      .firstDetectedAt("2023-05-10T18:07:07.210127Z")
                                      .lastDetectedAt("2025-03-29T15:35:00Z")
                                      .hasExploit(false)
                                      .hasCisaKevExploit(false)
                                      .vulnerableComponentName("libss")
                                      .resource(
                                              WizResource.builder()
                                                                .name("saki-RH8-ven-lab02")
                                                                .type("VIRTUAL_MACHINE")
                                                                .providerUniqueId("arn:aws:ec2:us-east-1:931505774614:instance/i-089e095a7a5de1a4f")
                                                                .cloudProviderURL("https://us-east-1.console.aws.amazon.com/ec2/v2/home?region=us-east-1#InstanceDetails:instanceId=i-089e095a7a5de1a4f")
                                                                .cloudPlatform("AWS")
                                                                .subscriptionExternalId("931505774614")
                                                                .build())
                                      .build();
    }

    private WizVulnerabilitiesFindingsResponse mockVulnerabilitiesResponse(final WizVulnerabilityFinding vulnerabilityFinding, final boolean nextPage) {
        return WizVulnerabilitiesFindingsResponse.builder()
                                                 .data(WizVulnerabilitiesData.builder()
                                                                     .vulnerabilityFindings(WizVulnerabilityFindings.builder()
                                                                                                                    .nodes(Collections.singletonList(vulnerabilityFinding))
                                                                                                                    .pageInfo(WizPageInfo.builder()
                                                                                                                                         .hasNextPage(nextPage)
                                                                                                                                         .endCursor(nextPage ? "endCursor" : null)
                                                                                                                                         .build()).build())
                                                                     .build())
                                                 .build();
    }

    private WizIssueFinding issueFinding(final String sourceRule1Name, final String sourceRule2Name) {
        return WizIssueFinding.builder()
                              .id("ffcd8d33-1b9f-4684-b6cf-83c8ec745a3e")
                              .status("OPEN")
                              .severity("INFORMATIONAL")
                              .sourceRules(List.of(
                                       WizSourceRule.builder()
                                                    .name(sourceRule1Name)
                                                    .risks(new String[] { "RELIABILITY_IMPACT" })
                                                    .build(),
                                       WizSourceRule.builder()
                                                    .name(sourceRule2Name)
                                                    .risks(new String[] { "EXTERNAL_EXPOSURE", "INSECURE_KUBERNETES_CLUSTER" })
                                                    .build()
                               ))
                              .resource(WizResource.builder()
                                                          .name("us-east-1 (211125710713)")
                                                          .type("REGION")
                                                          .providerUniqueId("211125710713/us-east-1")
                                                          .subscriptionExternalId("211125710713")
                                                          .cloudPlatform("AWS")
                                                          .cloudProviderURL("")
                                                          .build())
                              .build();
    }

    private WizIssuesFindingsResponse mockIssuesResponse(final WizIssueFinding issueFinding, final boolean nextPage) {
        return WizIssuesFindingsResponse.builder()
                                        .data(WizIssuesData.builder()
                                                   .issues(WizIssuesFindings.builder()
                                                                              .nodes(Collections.singletonList(issueFinding))
                                                                              .pageInfo(WizPageInfo.builder()
                                                                                                   .hasNextPage(nextPage)
                                                                                                   .endCursor(nextPage ? "endCursor" : null)
                                                                                                   .build()).build())
                                                   .build())
                                        .build();
    }

}
