package com.illumio.data.components.producer;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.model.*;
import com.illumio.data.util.Util;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskStatusSenderServiceTest {

    @Mock
    private KafkaSender<String, String> kafkaSender;

    @Mock
    private DataSyncConfig dataSyncConfig;

    @Mock
    private DataSyncConfig.KafkaProducerConfig kafkaProducerConfig;

    private ObjectMapper objectMapper;
    private TaskStatusSenderService taskStatusSenderService;

    private static final TaskStatus TASK_STATUS = TaskStatus.builder()
                                                            .taskId("1")
                                                            .integration(Integration.WIZ)
                                                            .tenantId("abc")
                                                            .statusType(TaskStatusType.STARTED)
                                                            .statusUpdateTime(Util.generateCurrentTimestamp())
                                                            .statusMessage("")
                                                            .taskType(TaskType.DELTA)
                                                            .build();
    private static final String KAFKA_TOPIC = "task-status-update-queue";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        taskStatusSenderService = new TaskStatusSenderService(kafkaSender, dataSyncConfig);

        when(dataSyncConfig.getKafkaTaskStatusProducerConfig()).thenReturn(kafkaProducerConfig);
        when(kafkaProducerConfig.getTopic()).thenReturn(KAFKA_TOPIC);
    }

    @Test
    @SneakyThrows
    void sendTaskStatusMessage_shouldHandleKafkaSendSuccess() {

        when(kafkaSender.send(any())).thenReturn(Flux.empty());

        StepVerifier.create(taskStatusSenderService.sendTaskStatusMessage(TASK_STATUS))
                .verifyComplete();

        ArgumentCaptor<Publisher<? extends SenderRecord<String, String, String>>> captor =
                ArgumentCaptor.forClass((Class) Publisher.class);

        verify(kafkaSender).send(captor.capture());

        verify(dataSyncConfig).getKafkaTaskStatusProducerConfig();
        verify(kafkaProducerConfig).getTopic();

        Publisher<? extends SenderRecord<String, String, String>> capturedPublisher = captor.getValue();

        assertNotNull(capturedPublisher);
        Flux.from(capturedPublisher)
            .collectList()
            .blockOptional()
            .ifPresent(records -> {
                assertEquals(1, records.size());
                SenderRecord<String, String, String> capturedRecord = records.get(0);
                assertEquals(KAFKA_TOPIC, capturedRecord.topic());
                try {
                    assertEquals(objectMapper.writeValueAsString(TASK_STATUS), capturedRecord.value());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    @Test
    void sendTaskStatusMessage_shouldHandleKafkaSendError() {
        when(kafkaSender.send(any())).thenReturn(Flux.error(new RuntimeException("Kafka error")));

        StepVerifier.create(taskStatusSenderService.sendTaskStatusMessage(TASK_STATUS))
                .expectError(RuntimeException.class)
                .verify();

        verify(kafkaSender).send(any());
    }
}