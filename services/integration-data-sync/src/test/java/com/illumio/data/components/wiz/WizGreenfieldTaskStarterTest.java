package com.illumio.data.components.wiz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Comparator;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WizGreenfieldTaskStarterTest {

    @Mock private TokenService tokenService;
    @Mock private WizProjectsDataPuller wizProjectsDataPuller;
    @Mock private WizReportGenerator wizReportGenerator;
    @Mock private TaskStatusSenderService taskStatusSenderService;
    @Mock private DataSyncFailureHandler dataSyncFailureHandler;
    @Mock private DataSyncConfig dataSyncConfig;
    @Mock private Runnable onTaskStartedSend;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private WizGreenfieldAsyncTaskStarter wizGreenfieldAsyncTaskStarter;

    private static final String REPORT_ID1 = "1cbff9e1-bc58-427f-8b24-6b8df7db962d";
    private static final String REPORT_ID2 = "2cbff9e1-bc58-427f-8b24-6b8df7db962d";
    private static final String REPORT_ID3 = "3cbff9e1-bc58-427f-8b24-6b8df7db962d";
    private static final String REPORT_ID4 = "4cbff9e1-bc58-427f-8b24-6b8df7db962d";
    private static final WizReportIdentifier REPORT_IDENTIFIER1 =
            wizReportIdentifier(REPORT_ID1, FindingType.VULNERABILITY);
    private static final WizReportIdentifier REPORT_IDENTIFIER2 =
            wizReportIdentifier(REPORT_ID2, FindingType.ISSUE);
    private static final WizReportIdentifier REPORT_IDENTIFIER3 =
            wizReportIdentifier(REPORT_ID3, FindingType.VULNERABILITY);
    private static final WizReportIdentifier REPORT_IDENTIFIER4 =
            wizReportIdentifier(REPORT_ID4, FindingType.ISSUE);
    private static final String PROJECT_ID1 = "87bd90a6-ed0e-519f-89a2-9e84814810e4";
    private static final String PROJECT_ID2 = "28a933d2-e304-52a2-a6a5-43f312b27b0b";
    private static final String TOKEN_SCOPED_PROJECTS = "eyJraWQiOiI0bVBCeXVBcldTUlwvNDAwMGpvTFlIbGdXVVVwWjgySThPeWVBMXJDOEk1dz0iLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IR8WlDZUldiuw1tr1uW1D41HAsDGL7ifDHFefR7uIRcHIizuwnzcDVpi8wddUGMltyP_3WcE-GOD9DbxQnmgM-gfX2sKehbpmBHUywyy4DJmZ6i28ai5KlOJXgFBappDqLzBd_iG_msHlEeeG2BHxVmEDvTdSj3_sclU2vHoFsldvRNx41NjUuVB_ERs6uvVp6-2vYrR1C_DniGQ_8XQuhAwuP16Xx2_pMk2Wx-71BrVX-ix2FfTm9YQyVYva5rWrAKEnVZQLwSLidO_IzTTmg0TOgdoNUKtZIAdAxsiIcHxNbqsOCcJe225OCCiKBtFbYa4BdglsBhausSichbW1Q";
    private static final String TOKEN_ALL_PROJECTS = "eyJraWQiOiJEXC9ZNE1HUWE1UDNCZXp2Mk9SdmptU3VzYUpLbndrVXNoSmoxVXhnOStrRT0iLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.08WdbDobOYXIeXGCpaBjCLB-f_djgFDEYLDvA8X7SGNeS_B9iggzBF-3O3y_0YkVBHBlMf0PoOwTf4uv5EohCzVPNF-ndHBMhEwr3IFyPVgcrVYS9qRnYg5lZkZ30DnAT6L25736jK8iJDNKEMqDm9Pnfwo5v3yeY-txTN-xd7OBf_PQT_AbClaxzLtjugm0X5MkaN9kzEXCIdC_6D56M1cg9rREhWPerm9AQ7iR3SnTEQHJmDM-3ZAXaY838ir7YlIF47Bq8e7Pp8Kscw3-gZA2pU2kGK61EAupswEnu5-Nv6Vhpz0KiMw4da7pB8S_jxErHve1iPpkjkDuGIj2sQ";
    private static final String WIZ_API_URL = "https://api.us41.app.wiz.io/graphql";

    @BeforeEach
    void setUp() {
        wizGreenfieldAsyncTaskStarter = new WizGreenfieldAsyncTaskStarter(tokenService, wizProjectsDataPuller, wizReportGenerator,
                taskStatusSenderService, objectMapper, dataSyncFailureHandler, dataSyncConfig);

        when(dataSyncFailureHandler.getRetrySpec(any())).thenReturn(Retry.backoff(2, Duration.ofMillis(1)));
    }

    @Test
    void testStartGreenfieldAsyncTask_ScopedProjects_Success() {
        final SyncTask syncTask = SyncTask.builder()
                                          .taskId("1")
                                          .integration(Integration.WIZ)
                                          .tenantId("tenant-1")
                                          .credentialsVaultPath("vault-path")
                                          .taskType(TaskType.GREENFIELD)
                                          .taskStatus(TaskStatusType.SCHEDULED_ASYNC)
                                          .tenantConfigurations(WizTenantConfigurations.builder()
                                                                                       .apiUrl(WIZ_API_URL)
                                                                                       .build())
                                          .priorSuccessfulSync(null)
                                          .build();
        when(taskStatusSenderService.sendTaskStatusMessage(eq(syncTask), eq(TaskStatusType.STARTED_ASYNC), eq("")))
                .thenReturn(Mono.empty());
        when(tokenService.getAuthToken(eq(syncTask)))
                .thenReturn(Mono.just(Token.builder()
                                           .accessToken(TOKEN_SCOPED_PROJECTS)
                                           .build()));
        when(wizProjectsDataPuller.pullData(eq(wizDataPullTask(TOKEN_SCOPED_PROJECTS, null, null))))
                .thenReturn(Flux.just(WizProjectsResponseNode.builder().id(PROJECT_ID1).build(),
                        WizProjectsResponseNode.builder().id(PROJECT_ID2).build()));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_SCOPED_PROJECTS, PROJECT_ID1, FindingType.VULNERABILITY))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID1, FindingType.VULNERABILITY)));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_SCOPED_PROJECTS, PROJECT_ID1, FindingType.ISSUE))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID2, FindingType.ISSUE)));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_SCOPED_PROJECTS, PROJECT_ID2, FindingType.VULNERABILITY))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID3, FindingType.VULNERABILITY)));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_SCOPED_PROJECTS, PROJECT_ID2, FindingType.ISSUE))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID4, FindingType.ISSUE)));

        when(taskStatusSenderService.sendTaskStatusMessage(
                eq(syncTask), eq(TaskStatusType.AWAITING_ASYNC),
                argThat(statusMessage -> {
                    try {
                        final WizAwaitingAsyncStatusMessage awaitingAsyncStatusMessage = objectMapper.readValue(statusMessage, WizAwaitingAsyncStatusMessage.class);
                        final List<WizReportIdentifier> expectedReportIdentifiers =
                                List.of(REPORT_IDENTIFIER1, REPORT_IDENTIFIER2, REPORT_IDENTIFIER3, REPORT_IDENTIFIER4);
                        return awaitingAsyncStatusMessage.getReportIdentifiers().stream().sorted(Comparator.comparing(WizReportIdentifier::getReportId)).toList()
                                                         .equals(expectedReportIdentifiers.stream().sorted(Comparator.comparing(WizReportIdentifier::getReportId)).toList());
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })))
                .thenReturn(Mono.empty());

        StepVerifier.create(wizGreenfieldAsyncTaskStarter.startGreenfieldAsyncTask(syncTask, onTaskStartedSend))
                    .verifyComplete();

        verify(taskStatusSenderService, times(2)).sendTaskStatusMessage(any(SyncTask.class), any(TaskStatusType.class), anyString());
        verify(tokenService).getAuthToken(eq(syncTask));
        verify(wizProjectsDataPuller).pullData(any(WizDataPullTask.class));
        verify(wizReportGenerator, times(4)).startReportGeneration(any(WizDataPullTask.class));
        verify(onTaskStartedSend).run();
    }

    @Test
    void testStartGreenfieldAsyncTask_AllProjects_Success() {
        final SyncTask syncTask = SyncTask.builder()
                                          .taskId("1")
                                          .integration(Integration.WIZ)
                                          .tenantId("tenant-1")
                                          .credentialsVaultPath("vault-path")
                                          .taskType(TaskType.GREENFIELD)
                                          .taskStatus(TaskStatusType.SCHEDULED_ASYNC)
                                          .tenantConfigurations(WizTenantConfigurations.builder()
                                                                                       .apiUrl(WIZ_API_URL)
                                                                                       .build())
                                          .priorSuccessfulSync(null)
                                          .build();
        when(taskStatusSenderService.sendTaskStatusMessage(eq(syncTask), eq(TaskStatusType.STARTED_ASYNC), eq("")))
                .thenReturn(Mono.empty());
        when(tokenService.getAuthToken(eq(syncTask)))
                .thenReturn(Mono.just(Token.builder()
                                           .accessToken(TOKEN_ALL_PROJECTS)
                                           .build()));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_ALL_PROJECTS, null, FindingType.VULNERABILITY))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID1, FindingType.VULNERABILITY)));
        when(wizReportGenerator.startReportGeneration(eq(wizDataPullTask(TOKEN_ALL_PROJECTS, null, FindingType.ISSUE))))
                .thenReturn(Mono.just(wizReportIdentifier(REPORT_ID2, FindingType.ISSUE)));

        when(taskStatusSenderService.sendTaskStatusMessage(
                eq(syncTask), eq(TaskStatusType.AWAITING_ASYNC),
                argThat(statusMessage -> {
                    try {
                        final WizAwaitingAsyncStatusMessage awaitingAsyncStatusMessage = objectMapper.readValue(statusMessage, WizAwaitingAsyncStatusMessage.class);
                        final List<WizReportIdentifier> expectedReportIdentifiers =
                                List.of(REPORT_IDENTIFIER1, REPORT_IDENTIFIER2);
                        return awaitingAsyncStatusMessage.getReportIdentifiers().stream().sorted(Comparator.comparing(WizReportIdentifier::getReportId)).toList()
                                                         .equals(expectedReportIdentifiers.stream().sorted(Comparator.comparing(WizReportIdentifier::getReportId)).toList());
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })))
                .thenReturn(Mono.empty());

        StepVerifier.create(wizGreenfieldAsyncTaskStarter.startGreenfieldAsyncTask(syncTask, onTaskStartedSend))
                    .verifyComplete();

        verify(taskStatusSenderService, times(2)).sendTaskStatusMessage(any(SyncTask.class), any(TaskStatusType.class), anyString());
        verify(tokenService).getAuthToken(eq(syncTask));
        verify(wizReportGenerator, times(2)).startReportGeneration(any(WizDataPullTask.class));
        verify(onTaskStartedSend).run();
        verifyNoInteractions(wizProjectsDataPuller);
    }

    private WizDataPullTask wizDataPullTask(final String token, final String projectId, final FindingType findingType) {
        return  WizDataPullTask.builder()
                               .integration(Integration.WIZ)
                               .authToken(token)
                               .apiUrl(WIZ_API_URL)
                               .priorSuccessfulSync(null)
                               .findingType(findingType)
                               .projectId(projectId)
                               .build();
    }

    private static WizReportIdentifier wizReportIdentifier(final String reportId, final FindingType findingType) {
        return WizReportIdentifier.builder()
                                  .reportId(reportId)
                                  .findingType(findingType)
                                  .build();
    }

}
