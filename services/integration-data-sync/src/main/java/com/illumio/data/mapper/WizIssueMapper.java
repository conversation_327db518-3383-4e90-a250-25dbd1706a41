package com.illumio.data.mapper;

import com.illumio.data.model.Integration;
import com.illumio.data.model.Issue;
import com.illumio.data.model.IssueMetadata;
import com.illumio.data.model.wiz.WizIssueFinding;
import com.illumio.data.model.wiz.WizSourceRule;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WizIssueMapper {

    public static Issue toIssue(WizIssueFinding wizIssueFinding, String tenantId, Integration integration) {
        return Issue.builder()
                    .id(wizIssueFinding.getId())
                    .status(wizIssueFinding.getStatus())
                    .tenantId(tenantId)
                    .integration(integration)
                    .resource(WizResourceMapper.toFindingResource(wizIssueFinding.getResource()))
                    .metadata(mapIssueMetadata(wizIssueFinding))
                    .build();
    }

    private static IssueMetadata mapIssueMetadata(WizIssueFinding wizIssueFinding) {
        final List<WizSourceRule> sourceRules = Optional.ofNullable(wizIssueFinding.getSourceRules())
                                                        .orElseGet(Collections::emptyList);
        String sourceRuleNames = sourceRules.stream()
                                            .map(WizSourceRule::getName)
                                            .collect(Collectors.joining(", "));
        List<String> risks = sourceRules.stream()
                                        .flatMap(sourceRule -> Arrays.stream(
                                                Optional.ofNullable(sourceRule.getRisks())
                                                        .orElse(new String[0])))
                                        .toList();
        return IssueMetadata.builder()
                            .sourceRuleNames(sourceRuleNames)
                            .severity(wizIssueFinding.getSeverity())
                            .risks(risks)
                            .build();
    }
}