package com.illumio.data.components.strategy;

import com.illumio.data.components.strategy.impl.ArmisInventoryStrategy;
import com.illumio.data.components.strategy.impl.WizFindingsStrategy;
import com.illumio.data.model.Integration;
import org.springframework.stereotype.Service;

@Service
public class FetchIntegrationsDataStrategyFactory {
    public static FetchIntegrationsDataStrategy getStrategy(Integration integration) {
    return switch (integration) {
      case WIZ -> new WizFindingsStrategy();
      case ARMIS -> new ArmisInventoryStrategy();
    };
    }
}
