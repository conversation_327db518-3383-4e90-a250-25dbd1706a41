package com.illumio.data.components.wiz;

import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.wiz.*;
import com.illumio.data.util.GraphQLQueryLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.util.DataSyncConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizReportStatusChecker {

    private final WebClient webClient;

    @LogReactiveExecutionTime
    public Mono<WizReportStatus> checkReportStatus(final WizReportStatusCheckTask reportStatusCheckTask) {
        final String query = GraphQLQueryLoader.loadQuery(QUERY_WIZ_GREENFIELD_REPORT_DOWNLOAD_FILE_PATH);
        final Map<String, Object> queryVars = getQueryVars(reportStatusCheckTask);

        final String authHeaderVal = String.format("%s %s", REQUEST_HEADER_AUTH_BEARER, reportStatusCheckTask.getAuthToken());
        return webClient.post()
                .uri(reportStatusCheckTask.getApiUrl())
                .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.AUTHORIZATION, authHeaderVal)
                .bodyValue(
                        Map.of(
                                REQUEST_BODY_VAR_QUERY, query,
                                REQUEST_BODY_VAR_VARIABLES, queryVars))
                .retrieve()
                .bodyToMono(WizReportDownloadUrlResponse.class)
                .map(this::reportStatusFromWizResponse);
    }

    private Map<String, Object> getQueryVars(final WizReportStatusCheckTask reportStatusCheckTask) {
        return Collections.singletonMap(QUERY_VAR_REPORT_ID, reportStatusCheckTask.getReportIdentifier().getReportId());
    }

    private WizReportStatus reportStatusFromWizResponse(final WizReportDownloadUrlResponse wizResponse) {
        final Optional<WizReportDownloadUrlResponseLastRun> lastRun =
                Optional.ofNullable(wizResponse)
                        .map(WizReportDownloadUrlResponse::getData)
                        .map(WizReportDownloadUrlResponseData::getReport)
                        .map(WizReportDownloadUrlResponseReport::getLastRun);
        return WizReportStatus.builder()
                              .status(lastRun.map(WizReportDownloadUrlResponseLastRun::getStatus).orElse(null))
                              .url(lastRun.map(WizReportDownloadUrlResponseLastRun::getUrl).orElse(null))
                              .build();
    }

}
