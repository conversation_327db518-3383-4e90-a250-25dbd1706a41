package com.illumio.data.util;

import lombok.experimental.UtilityClass;

import java.time.Duration;

@UtilityClass
public class DataSyncConstants {

    public static final String REQUEST_BODY_VAR_QUERY = "query";
    public static final String REQUEST_BODY_VAR_VARIABLES = "variables";

    public static final String REQUEST_HEADER_AUTH_BEARER = "Bearer";

    public static final String QUERY_VAR_FIRST = "first";
    public static final String QUERY_VAR_AFTER = "after";
    public static final String QUERY_VAR_FILTER_BY = "filterBy";
    public static final String QUERY_VAR_CREATED_AT = "createdAt";
    public static final String QUERY_VAR_UPDATED_AT = "updatedAt";
    public static final String QUERY_VAR_REPORT_COLUMNS = "columnSelection";
    public static final String QUERY_VAR_REPORT_FORMAT = "format";
    public static final String QUERY_VAL_REPORT_FORMAT = "JSON";
    public static final String QUERY_VAR_REPORT_INCREMENTAL = "incremental";
    public static final String QUERY_VAR_REPORT_ISSUE_PARAMS = "issueParams";
    public static final String QUERY_VAL_REPORT_ISSUE_PARAMS_TYPE = "DETAILED";
    public static final String QUERY_VAR_REPORT_NAME = "name";
    public static final String QUERY_VAR_REPORT_TYPE = "type";
    public static final String QUERY_VAR_REPORT_ID = "reportId";
    public static final String QUERY_VAR_REPORT_PARAMS = "reportParams";
    public static final String QUERY_VAR_PROJECT_ID = "projectId";

    public static final String QUERIES_CLASSPATH_FOLDER = "graphql";
    public static final String QUERY_WIZ_GREENFIELD_REPORT_CREATE_FILE_PATH = "wizGreenfieldReportCreateQuery.graphql";
    public static final String QUERY_WIZ_GREENFIELD_REPORT_DOWNLOAD_FILE_PATH = "wizGreenfieldReportDownloadUrlQuery.graphql";
    public static final String QUERY_WIZ_PROJECTS_FILE_PATH = "wizProjectsQuery.graphql";
    public static final String QUERY_WIZ_ISSUES_DELTA_FILE_PATH = "wizIssuesDeltaQuery.graphql";
    public static final String QUERY_WIZ_VULNERABILITIES_DELTA_FILE_PATH = "wizVulnerabilitiesDeltaQuery.graphql";

    public static final String WIZ_REPORT_TYPE_ISSUES = "ISSUES";
    public static final String WIZ_REPORT_TYPE_VULNERABILITIES = "VULNERABILITIES";
    public static final String WIZ_REPORT_STATUS_COMPLETED = "COMPLETED";
    public static final String[] WIZ_VULNERABILITY_REPORT_COLUMNS = new String[] {
            "CVEDescription", "name", "status", "CVSSSeverity", "exploitabilityScore", "firstDetectedAt",
        "hasCisaKevExploit", "hasExploit", "lastDetectedAt", "impactScore", "score", "severity", "detailedName",
        "vulnerableAsset.name", "vulnerableAsset.type", "vulnerableAsset.providerUniqueId",
        "vulnerableAsset.subscriptionExternalId", "vulnerableAsset.cloudPlatform", "vulnerableAsset.cloudProviderURL" };
    public static final String WIZ_JWT_TOKEN_PROJECT_IDS_FIELD = "productIds";
    public static final String WIZ_JWT_TOKEN_PROJECT_IDS_ALL_PROJECTS = "*";

    public static final int WIZ_DEFAULT_PROJECTS_PAGE_SIZE = 500;
    public static final int WIZ_MAX_PROJECTS_PAGE_SIZE = 500;
    public static final int WIZ_DEFAULT_FINDINGS_PAGE_SIZE = 50;
    public static final int WIZ_MAX_FINDINGS_PAGE_SIZE = 1000;
    public static final Duration WIZ_DEFAULT_SYNC_DATA_WINDOW_LENGTH_FALLBACK = Duration.ofDays(1);
    public static final Duration WIZ_DEFAULT_MIN_TIME_BETWEEN_REPORT_CREATION_CALLS = Duration.ofSeconds(1);

    public static final String AZURE = "Azure";
    public static final String EXTRACT_AZURE_ID_REGEX = "/resource/";
}
