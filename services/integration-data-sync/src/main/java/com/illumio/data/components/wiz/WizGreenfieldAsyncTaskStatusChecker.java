package com.illumio.data.components.wiz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.WizAwaitingAsyncStatusMessage;
import com.illumio.data.model.wiz.WizReportIdentifier;
import com.illumio.data.model.wiz.WizReportStatus;
import com.illumio.data.model.wiz.WizReportStatusCheckTask;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.illumio.data.util.DataSyncConstants.WIZ_REPORT_STATUS_COMPLETED;
import static com.illumio.data.util.Util.serializeWizReadyAsyncStatusMessage;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizGreenfieldAsyncTaskStatusChecker {

    private final TokenService tokenService;
    private final WizReportStatusChecker wizReportStatusChecker;
    private final TaskStatusSenderService taskStatusSenderService;
    private final DataSyncFailureHandler dataSyncFailureHandler;
    private final ObjectMapper objectMapper;

    @LogReactiveExecutionTime
    public Mono<Void> checkGreenfieldAsyncTaskStatus(SyncTask syncTask, Runnable onTaskHandleStart) {
        final List<WizReportIdentifier> reportIdentifiers =
                getReportIdentifiersFromWizAwaitingAsyncStatusMessage(syncTask.getStatusMessage(), objectMapper);
        return tokenService.getAuthToken(syncTask)
                           .flatMapMany(token -> getReportStatusCheckTasks(syncTask, reportIdentifiers, token))
                           .flatMap(wizReportStatusChecker::checkReportStatus)
                           .collectList()
                           .doOnSubscribe(ignore -> onTaskHandleStart.run())
                           .flatMap(reportStatusResponses ->
                                   handleReportStatusResponses(reportStatusResponses, syncTask, reportIdentifiers))
                           .retryWhen(dataSyncFailureHandler.getRetrySpec(syncTask))
                           .then();
    }

    private Flux<WizReportStatusCheckTask> getReportStatusCheckTasks(final SyncTask syncTask,
                                                                     final List<WizReportIdentifier> reportIdentifiers,
                                                                     final Token token) {
        return Flux.fromIterable(reportIdentifiers
                .stream()
                .map(reportIdentifier -> getReportStatusCheckTask(syncTask, reportIdentifier, token))
                .toList());
    }

    private WizReportStatusCheckTask getReportStatusCheckTask(final SyncTask syncTask,
                                                              final WizReportIdentifier reportIdentifier,
                                                              final Token token) {
        return WizReportStatusCheckTask.builder()
                                       .integration(syncTask.getIntegration())
                                       .apiUrl(Optional.ofNullable(syncTask.getTenantConfigurations())
                                                       .map(TenantConfigurations::getApiUrl)
                                                       .orElse(null))
                                       .authToken(token.getAccessToken())
                                       .priorSuccessfulSync(syncTask.getPriorSuccessfulSync())
                                       .reportIdentifier(reportIdentifier)
                                       .build();
    }

    private Mono<Void> handleReportStatusResponses(final List<WizReportStatus> reportStatuses,
                                             final SyncTask syncTask,
                                             final List<WizReportIdentifier> reportIdentifiers) {
        final List<WizReportStatus> readyReportStatuses = reportStatuses
                .stream()
                .filter(reportStatus ->
                        WIZ_REPORT_STATUS_COMPLETED.equals(reportStatus.getStatus())
                                && Objects.nonNull(reportStatus.getUrl()))
                .toList();
        final long readyReportCount = readyReportStatuses.size();
        final long totalReportCount = reportStatuses.size() - readyReportCount;
        if (readyReportCount == reportStatuses.size()) {
            return taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.READY_ASYNC,
                    serializeWizReadyAsyncStatusMessage(reportIdentifiers, objectMapper));
        } else {
            log.debug("Not all reports are ready for syncTask={}. readyReportCount={}, totalReportCount={}",
                    syncTask, readyReportCount, totalReportCount);
            return taskStatusSenderService.sendTaskStatusMessage(
                    syncTask, TaskStatusType.AWAITING_ASYNC,
                    Util.serializeWizAwaitingAsyncStatusMessage(reportIdentifiers, objectMapper));
        }
    }

    @SneakyThrows
    private List<WizReportIdentifier> getReportIdentifiersFromWizAwaitingAsyncStatusMessage(
            final String wizAwaitingAsyncStatusMessage, final ObjectMapper objectMapper) {
        return objectMapper
                .readValue(wizAwaitingAsyncStatusMessage, WizAwaitingAsyncStatusMessage.class)
                .getReportIdentifiers();
    }

}