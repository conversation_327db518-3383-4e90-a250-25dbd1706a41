package com.illumio.data.components.wiz;

import com.illumio.data.components.IDataPuller;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import com.illumio.data.util.DataSyncConstants;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.GraphQLQueryLoader;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


import java.time.Duration;
import java.time.Instant;
import java.util.*;

import static com.illumio.data.util.DataSyncConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizDeltaDataPuller implements IDataPuller<WizFinding, WizDataPullTask> {

    private final WebClient webClient;
    private final DataSyncConfig dataSyncConfig;

    @Override
    @LogReactiveExecutionTime
    public Flux<WizFinding> pullData(final WizDataPullTask dataPullTask) {
        final Class<WizFindingsResponse<WizFinding>> responseClass = getResponseClass(dataPullTask);
        return fetchFindingsPage(dataPullTask, responseClass, null)
                .expand(response -> {
                    final Optional<String> nextCursor = response.getNextCursor();
                    return nextCursor.map(cursor -> fetchFindingsPage(dataPullTask, responseClass, cursor))
                                     .orElseGet(Mono::empty);
                })
                .flatMap(response -> Flux.fromIterable(response.getFindings()));
    }

    private <T extends IFindingsPaginatedResponse<WizFinding>> Mono<T> fetchFindingsPage(final WizDataPullTask dataPullTask, final Class<T> responseClass, final String cursor) {
        final String query = getGraphqlQuery(dataPullTask);
        final Map<String, Object> queryVars = getQueryVars(dataPullTask, cursor);

        final String authHeaderVal = String.format("%s %s", REQUEST_HEADER_AUTH_BEARER, dataPullTask.getAuthToken());
    return webClient
        .post()
        .uri(dataPullTask.getApiUrl())
        .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        .header(HttpHeaders.AUTHORIZATION, authHeaderVal)
        .bodyValue(
            Map.of(
                REQUEST_BODY_VAR_QUERY, query,
                REQUEST_BODY_VAR_VARIABLES, queryVars))
        .retrieve()
        .bodyToMono(responseClass)
        .onErrorResume(
            WebClientResponseException.class,
            ex -> {
              if ( ex.getStatusCode() == HttpStatus.OK) {
                final String errorMessage = String.format("Error observed in WebClient call: %s", Objects.requireNonNull(ex.getRootCause()).getMessage());
                return Mono.error(new Exception(errorMessage));
              }
              return Mono.error(ex);
            });
    }

    private String getGraphqlQuery(final WizDataPullTask dataPullTask) {
        return switch(dataPullTask.getFindingType()) {
            case VULNERABILITY -> GraphQLQueryLoader.loadQuery(DataSyncConstants.QUERY_WIZ_VULNERABILITIES_DELTA_FILE_PATH);
            case ISSUE -> GraphQLQueryLoader.loadQuery(DataSyncConstants.QUERY_WIZ_ISSUES_DELTA_FILE_PATH);
        };
    }

    private Map<String, Object> getQueryVars(final WizDataPullTask dataPullTask, final String cursor) {
        final Map<String, String> afterFilter = Map.of(
                // only return findings first detected after cutoff time
                QUERY_VAR_AFTER, getDataStartCutoffTime(dataPullTask)
        );

        final Map<String, Object> filterBy = new HashMap<>();
        switch (dataPullTask.getFindingType()) {
            case ISSUE -> filterBy.put(QUERY_VAR_CREATED_AT, afterFilter);
            case VULNERABILITY -> filterBy.put(QUERY_VAR_UPDATED_AT, afterFilter);
        }

        final Map<String, Object> queryVars = new HashMap<>(Map.of(
                QUERY_VAR_FIRST, getPageSize(),
                QUERY_VAR_FILTER_BY, filterBy
        ));
        if (cursor != null) {
            queryVars.put(QUERY_VAR_AFTER, cursor);
        }
        return queryVars;
    }


    private int getPageSize() {
        final int maxConfigDefinedPageSize = Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                .map(DataSyncConfig.IntegrationsConfig::getWiz)
                .map(DataSyncConfig.IntegrationsConfig.WizConfig::getFindingsApisMaxPageSize)
                .orElse(WIZ_DEFAULT_FINDINGS_PAGE_SIZE);
        return Integer.min(WIZ_MAX_FINDINGS_PAGE_SIZE, maxConfigDefinedPageSize);
    }

    private String getDataStartCutoffTime(final DataPullTask dataPullTask) {
        final Duration syncDataWindowLengthFallback = Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                .map(DataSyncConfig.IntegrationsConfig::getWiz)
                .map(DataSyncConfig.IntegrationsConfig.WizConfig::getSyncDataWindowLengthFallback)
                .orElse(WIZ_DEFAULT_SYNC_DATA_WINDOW_LENGTH_FALLBACK);
        return Optional.ofNullable(dataPullTask.getPriorSuccessfulSync())
                .map(TaskBasicMetadata::getStartTime)
                .orElse(DateTimeUtil.generateTimestamp(Instant.now().minus(onboardSyncDataWindowLength)));
    }

    @SuppressWarnings("unchecked")
    private <T extends WizFindingsResponse<? extends WizFinding>> Class<T> getResponseClass(final WizDataPullTask dataPullTask) {
        return (Class<T>) switch(dataPullTask.getFindingType()) {
            case VULNERABILITY -> WizVulnerabilitiesFindingsResponse.class;
            case ISSUE -> WizIssuesFindingsResponse.class;
        };
    }

}
