package com.illumio.data.model.wiz;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Collections;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WizReportIssue {

    @JsonProperty("Issue ID")
    private String id;
    @JsonProperty("Title")
    private String sourceRuleName;
    @JsonProperty("Risks")
    private String sourceRuleRisks;
    @JsonProperty("Severity")
    private String severity;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("Resource Name")
    private String resourceName;
    @JsonProperty("Resource Type")
    private String resourceType;
    @JsonProperty("Provider ID")
    private String resourceProviderUniqueId;
    @JsonProperty("Subscription ID")
    private String resourceSubscriptionExternalId;
    @JsonProperty("Resource Platform")
    private String resourceCloudPlatform;
    @JsonProperty("Cloud Provider URL")
    private String resourceCloudProviderUrl;

    public WizIssueFinding toIssueFinding() throws JsonProcessingException {
        return WizIssueFinding.builder()
                .id(id)
                .severity(severity)
                .status(status)
                .sourceRules(Collections.singletonList(
                        WizSourceRule.builder()
                                     .name(sourceRuleName)
                                     .risks(Optional.ofNullable(sourceRuleRisks)
                                                    .map(sourceRuleRisks -> sourceRuleRisks.split(", "))
                                                    .orElse(new String[0]))
                                     .build()))
                .resource(WizResource.builder()
                                     .name(resourceName)
                                     .type(resourceType)
                                     .providerUniqueId(resourceProviderUniqueId)
                                     .subscriptionExternalId(resourceSubscriptionExternalId)
                                     .cloudPlatform(resourceCloudPlatform)
                                     .cloudProviderURL(resourceCloudProviderUrl)
                                     .build())
                .build();
    }
}