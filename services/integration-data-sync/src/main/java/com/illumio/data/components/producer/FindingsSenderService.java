package com.illumio.data.components.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.Finding;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

import static com.illumio.data.util.Constants.FINDING_TYPE;

@Slf4j
@Service
public class FindingsSenderService {

    private final KafkaSender<String, String> kafkaSender;
    private final DataSyncConfig dataSyncConfig;
    private final ObjectMapper objectMapper;

    public FindingsSenderService(@Qualifier("kafkaFindingsSender") final KafkaSender<String, String> kafkaSender,
                                 final DataSyncConfig dataSyncConfig,
                                 final ObjectMapper objectMapper) {
        this.kafkaSender = kafkaSender;
        this.dataSyncConfig = dataSyncConfig;
        this.objectMapper = objectMapper;
    }

    @LogReactiveExecutionTime
    public Mono<Void> sendFindings(final Flux<Finding> findings) {
        return kafkaSender
                .send(findings.map(this::senderRecord))
                .doOnError(throwable ->
                        log.error("Encountered error while sending finding to Kafka", throwable))
                .doOnComplete(() ->
                        log.debug("Successfully sent finding to Kafka"))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> senderRecord(final Finding finding) {
        final String topic = dataSyncConfig.getKafkaFindingsProducerConfig().getTopic();
        final String message = objectMapper.writeValueAsString(finding);
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, message);
        producerRecord.headers().add(FINDING_TYPE, finding.getFindingsType().toString().getBytes());
        return SenderRecord.create(producerRecord, null);
    }

}
