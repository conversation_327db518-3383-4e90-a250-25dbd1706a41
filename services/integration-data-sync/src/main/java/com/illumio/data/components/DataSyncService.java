package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.RetryConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.mapper.WizIssueMapper;
import com.illumio.data.mapper.WizVulnerabilityMapper;
import com.illumio.data.model.TokenRequest;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.components.producer.FindingsSenderService;
import com.illumio.data.components.wiz.WizDataPuller;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.WizDataPullTask;
import com.illumio.data.model.wiz.WizFinding;
import com.illumio.data.model.wiz.WizIssueFinding;
import com.illumio.data.model.wiz.WizVulnerabilityFinding;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncService {

    private final CredentialsService credentialsService;
    private final TokenGenerationService tokenGenerationService;
    private final WizDataPuller wizDataPuller;
    private final FindingsSenderService findingsSenderService;
    private final TaskStatusSenderService taskStatusSenderService;
    private final RetryConfig retryConfig;

    @LogReactiveExecutionTime
    public Mono<Void> handleSyncTask(SyncTask syncTask, Runnable onTaskStartedUpdateSent) {
        return sendTaskStatusMessage(syncTask, TaskStatusType.STARTED, "")
                   .doOnSuccess(v -> onTaskStartedUpdateSent.run())
                   .thenMany(getFindingsData(syncTask))
                   .transform(findings -> processAndSendFindings(syncTask, findings))
                   .then();
    }

    private Mono<Void> processAndSendFindings(SyncTask syncTask, Flux<Finding> findings) {
        final ConcurrentHashMap<FindingType, AtomicLong> findingsUpdated = new ConcurrentHashMap<>();

        return findings
                .doOnNext(finding -> findingsUpdated
                        .computeIfAbsent(finding.getFindingsType(), k -> new AtomicLong(0))
                        .incrementAndGet())
                .transform(findingsSenderService::sendFindings)
                .doOnComplete(() -> sendSuccessTaskStatusMessage(syncTask, findingsUpdated))
                .retryWhen(
                        Retry.backoff(retryConfig.getSystemOperations().getMaxRetries(),
                                     retryConfig.getSystemOperations().getMinBackoff())
                             .doBeforeRetry(retrySignal -> {
                                 log.warn("Error occurred during process to perform data sync for taskId={}, tenantId={}, integration={}. retryNumber={}, Retrying...",
                                         syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), retrySignal.totalRetries(), retrySignal.failure());
                                 sendTaskStatusMessage(syncTask, TaskStatusType.RETRY, retrySignal.failure().getMessage())
                                         .subscribe();
                             })
                             .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                                 log.error("Error occurred during process to perform data sync for taskId={}, tenantId={}, integration={}. retryNumber={}, retries exhausted.",
                                         syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), retrySignal.totalRetries(), retrySignal.failure());
                                 sendTaskStatusMessage(syncTask, TaskStatusType.FAIL, retrySignal.failure().getMessage())
                                         .subscribe();
                                 return retrySignal.failure();
                             }))
                .then();
    }

    private Flux<Finding> getFindingsData(final SyncTask syncTask) {
        return getAuthToken(syncTask)
                .flatMapMany(token -> pullIntegrationData(syncTask, token))
                .map(finding ->
                        switch (finding.getFindingsType()) {
                            case VULNERABILITY ->
                                    WizVulnerabilityMapper.toVulnerability((WizVulnerabilityFinding) finding,
                                            syncTask.getTenantId(), syncTask.getIntegration());
                            case ISSUE ->
                                    WizIssueMapper.toIssue((WizIssueFinding) finding,
                                            syncTask.getTenantId(), syncTask.getIntegration());
                });
    }

    private Mono<Token> getAuthToken(final SyncTask syncTask) {
        return credentialsService
                .getCredentials(syncTask.getCredentialsVaultPath())
                .map(credentials -> TokenRequest.builder()
                                                .tenantId(syncTask.getTenantId())
                                                .integration(syncTask.getIntegration())
                                                .credentials(credentials)
                                                .authUrl(Optional.ofNullable(syncTask.getTenantConfigurations())
                                                                 .map(TenantConfigurations::getAuthUrl)
                                                                 .orElse(null))
                                                .build())
                .flatMap(tokenGenerationService::getToken);
    }

    private Flux<WizFinding> pullIntegrationData(final SyncTask syncTask, final Token token) {
        final WizDataPullTask vulnerabilitiesDataPullTask = getDataPullTask(FindingType.VULNERABILITY, syncTask, token);
        final WizDataPullTask issuesDataPullTask = getDataPullTask(FindingType.ISSUE, syncTask, token);
        return wizDataPuller.pullData(vulnerabilitiesDataPullTask)
                            .concatWith(wizDataPuller.pullData(issuesDataPullTask));
    }

    private WizDataPullTask getDataPullTask(final FindingType findingType, final SyncTask syncTask, final Token token) {
        return WizDataPullTask.builder()
                              .integration(syncTask.getIntegration())
                              .apiUrl(Optional.ofNullable(syncTask.getTenantConfigurations())
                                              .map(TenantConfigurations::getApiUrl)
                                              .orElse(null))
                              .authToken(token.getAccessToken())
                              .priorSuccessfulSync(syncTask.getPriorSuccessfulSync())
                              .findingType(findingType)
                              .build();
    }

    private Mono<Void> sendTaskStatusMessage(final SyncTask syncTask, final TaskStatusType taskStatus, final String statusMessage) {
        return taskStatusSenderService.sendTaskStatusMessage(
                TaskStatus.builder()
                          .taskId(syncTask.getTaskId())
                          .integration(syncTask.getIntegration())
                          .tenantId(syncTask.getTenantId())
                          .taskType(syncTask.getTaskType())
                          .statusType(taskStatus)
                          .statusMessage(statusMessage)
                          .statusUpdateTime(DateTimeUtil.generateCurrentTimestamp())
                          .build());
    }

    private void sendSuccessTaskStatusMessage(final SyncTask syncTask, ConcurrentMap<FindingType, AtomicLong> findingsUpdated) {
        long vulnerabilitiesUpdated = findingsUpdated.getOrDefault(FindingType.VULNERABILITY, new AtomicLong(0)).get();
        long issuesUpdated = findingsUpdated.getOrDefault(FindingType.ISSUE, new AtomicLong(0)).get();
        sendTaskStatusMessage(syncTask, TaskStatusType.SUCCESS, serializeWizSuccessStatusMessage(vulnerabilitiesUpdated, issuesUpdated))
                .subscribe();
        log.debug("Successfully completed data sync for taskId={}, tenantId={}, integration={} with {} vulnerabilities, {} issues...",
                syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), vulnerabilitiesUpdated, issuesUpdated);
    }

    @SneakyThrows
    private static String serializeWizSuccessStatusMessage(final Long vulnerabilitiesUpdated, final Long issuesUpdated) {
        ObjectMapper objectMapper = new ObjectMapper();
        WizSuccessStatusMessage wizSuccessStatusMessage =
                WizSuccessStatusMessage.builder()
                                       .vulnerabilitiesUpdated(vulnerabilitiesUpdated)
                                       .issuesUpdated(issuesUpdated)
                                       .build();
        return objectMapper.writeValueAsString(wizSuccessStatusMessage);
    }

}