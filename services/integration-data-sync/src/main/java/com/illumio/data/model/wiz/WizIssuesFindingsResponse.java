package com.illumio.data.model.wiz;

import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WizIssuesFindingsResponse extends WizFindingsResponse<WizIssueFinding> {

    private WizIssuesData data;

    @Override
    public Optional<String> getNextCursor() {
        return Optional.ofNullable(getData())
                       .map(WizIssuesData::getIssues)
                       .map(WizIssuesFindings::getPageInfo)
                       .filter(WizPageInfo::isHasNextPage)
                       .map(WizPageInfo::getEndCursor);
    }

    @Override
    public List<WizIssueFinding> getFindings() {
        return Optional.ofNullable(getData())
                       .map(WizIssuesData::getIssues)
                       .map(WizIssuesFindings::getNodes)
                       .orElseGet(Collections::emptyList);
    }

}