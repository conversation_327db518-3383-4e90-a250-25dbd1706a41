package com.illumio.data.components;

import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.configuration.RetryConfig;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskStatusType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.util.retry.Retry;

@Slf4j
@Component
@RequiredArgsConstructor
public class DataSyncFailureHandler {

    @Autowired
    private TaskStatusSenderService taskStatusSenderService;
    @Autowired
    private RetryConfig retryConfig;

    public reactor.util.retry.Retry getRetrySpec(final SyncTask syncTask) {
        return Retry.backoff(retryConfig.getSystemOperations().getMaxRetries(),
                            retryConfig.getSystemOperations().getMinBackoff())
                    .doBeforeRetry(retrySignal -> {
                        log.warn("Error occurred during process to perform data sync for taskId={}, tenantId={}, integration={}. retryNumber={}, Retrying...",
                                syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), retrySignal.totalRetries(), retrySignal.failure());
                        taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.RETRY, retrySignal.failure().getMessage())
                                               .subscribe();
                    })
                    .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                        log.error("Error occurred during process to perform data sync for taskId={}, tenantId={}, integration={}. retryNumber={}, retries exhausted.",
                                syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), retrySignal.totalRetries(), retrySignal.failure());
                        taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.FAIL, retrySignal.failure().getMessage())
                                               .subscribe();
                        return retrySignal.failure();
                    });
    }

}
