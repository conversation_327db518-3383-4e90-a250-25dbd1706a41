package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "integration-data-sync")
@Getter
@Setter
public class DataSyncConfig {

    private final IntegrationsConfig integrationsConfig = new IntegrationsConfig();
    private final KafkaProducerConfig kafkaTaskStatusProducerConfig = new KafkaProducerConfig();
    private final KafkaProducerConfig kafkaFindingsProducerConfig = new KafkaProducerConfig();
    private TaskConsumerQueueMode taskConsumerMode = TaskConsumerQueueMode.LIGHT;
    private final KafkaConsumerConfig kafkaLightTaskConsumerConfig = new KafkaConsumerConfig();
    private final KafkaConsumerConfig kafkaHeavyTaskConsumerConfig = new KafkaConsumerConfig();
    private final RetryConfig dataSyncRetryConfig = new RetryConfig();

    public enum TaskConsumerQueueMode {
        LIGHT,
        HEAVY
    }

    @Getter
    @Setter
    public static class IntegrationsConfig {
        private final WizConfig wiz = new WizConfig();

        @Getter
        @Setter
        public static class WizConfig {
            private Integer projectsApiMaxPageSize;
            private Integer findingsApisMaxPageSize;
            private Duration syncDataWindowLengthFallback;
            private Duration minTimeBetweenReportCreationCalls;
        }
    }

    @Getter
    @Setter
    public static class RetryConfig {
        private Integer maxRetries = 50;
        private Duration minBackoff = Duration.ofSeconds(10);
    }

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
    }

}


