package com.illumio.data.model.wiz;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.illumio.data.model.FindingType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class WizFinding {

    private String status;
    @JsonProperty("vulnerableAsset")
    private WizResource resource;

    public abstract FindingType getFindingsType();

}