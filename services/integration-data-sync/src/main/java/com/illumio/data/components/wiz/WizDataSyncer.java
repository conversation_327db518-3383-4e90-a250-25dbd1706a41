package com.illumio.data.components.wiz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.DataSyncer;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.FindingsSenderService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.mapper.WizIssueMapper;
import com.illumio.data.mapper.WizVulnerabilityMapper;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
public abstract class WizDataSyncer extends DataSyncer<WizFinding, Finding, ConcurrentHashMap<FindingType, AtomicLong>> {

    private final FindingsSenderService findingsSenderService;

    public WizDataSyncer(final TokenService tokenService,
                         final TaskStatusSenderService taskStatusSenderService,
                         final DataSyncFailureHandler dataSyncFailureHandler,
                         final ObjectMapper objectMapper,
                         final FindingsSenderService findingsSenderService) {
        super(tokenService,
                taskStatusSenderService,
                dataSyncFailureHandler,
                objectMapper,
                ConcurrentHashMap::new,
                WizDataSyncer::successSummaryStatsAppender,
                WizDataSyncer::successStatusMessageGenerator);
        this.findingsSenderService = findingsSenderService;
    }

    @Override
    public Finding mapDataPoint(final WizFinding finding, final SyncTask syncTask) {
        return switch (finding.getFindingsType()) {
            case VULNERABILITY -> WizVulnerabilityMapper.toVulnerability((WizVulnerabilityFinding) finding,
                    syncTask.getTenantId(), syncTask.getIntegration());
            case ISSUE -> WizIssueMapper.toIssue((WizIssueFinding) finding,
                    syncTask.getTenantId(), syncTask.getIntegration());
        };
    }

    @Override
    public Mono<Void> consumeData(Flux<Finding> dataFlux) {
        return findingsSenderService.sendFindings(dataFlux);
    }

    private static void successSummaryStatsAppender(final ConcurrentHashMap<FindingType, AtomicLong> findingsUpdatedAccumulator,
                                                    final Finding additionalFinding) {
        findingsUpdatedAccumulator
                .computeIfAbsent(additionalFinding.getFindingsType(), k -> new AtomicLong(0))
                .incrementAndGet();
    }

    @SneakyThrows
    private static String successStatusMessageGenerator(final ConcurrentHashMap<FindingType, AtomicLong> findingsUpdated,
                                                        final ObjectMapper objectMapper) {
        final AtomicLong defaultFindingUpdatedCount = new AtomicLong(0);
        final long vulnerabilitiesUpdated = findingsUpdated.getOrDefault(FindingType.VULNERABILITY, defaultFindingUpdatedCount).get();
        final long issuesUpdated = findingsUpdated.getOrDefault(FindingType.ISSUE, defaultFindingUpdatedCount).get();
        final WizSuccessStatusMessage wizSuccessStatusMessage =
                WizSuccessStatusMessage.builder()
                                       .vulnerabilitiesUpdated(vulnerabilitiesUpdated)
                                       .issuesUpdated(issuesUpdated)
                                       .build();
        return objectMapper.writeValueAsString(wizSuccessStatusMessage);
    }

}