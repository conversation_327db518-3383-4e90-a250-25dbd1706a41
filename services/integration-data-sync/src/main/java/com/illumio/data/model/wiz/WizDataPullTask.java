package com.illumio.data.model.wiz;

import com.illumio.data.model.DataPullTask;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TenantConfigurations;
import com.illumio.data.model.Token;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

import java.util.Optional;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WizDataPullTask extends DataPullTask {

    private FindingType findingType;
    private String projectId;

    public static WizDataPullTask fromSyncTask(final SyncTask syncTask,
                                                final Token token,
                                                @Nullable final FindingType findingType,
                                                @Nullable final String projectId) {
        return WizDataPullTask.builder()
                              .integration(syncTask.getIntegration())
                              .apiUrl(Optional.ofNullable(syncTask.getTenantConfigurations())
                                              .map(TenantConfigurations::getApiUrl)
                                              .orElse(null))
                              .authToken(token.getAccessToken())
                              .priorSuccessfulSync(syncTask.getPriorSuccessfulSync())
                              .findingType(findingType)
                              .projectId(projectId)
                              .build();
    }

}
