package com.illumio.data.components.wiz;


import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.client.ApiClient;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.ApiRequest;
import com.illumio.data.model.DataPullTask;
import com.illumio.data.model.GraphQLReuqest;
import com.illumio.data.model.TaskBasicMetadata;
import com.illumio.data.model.wiz.WizDataPullTask;
import com.illumio.data.util.DataSyncConstants;
import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.GraphQLQueryLoader;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.util.DataSyncConstants.*;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizDataPullerRefactored {

    private final ApiClient apiClient;
    private final DataSyncConfig dataSyncConfig;

    @LogReactiveExecutionTime
    public Flux<JsonNode> pullData(final WizDataPullTask dataPullTask) {
        return fetchFindingsPage(dataPullTask, null)
                .expand(response -> {
                    final Optional<String> nextCursor = extractNextCursor(response);
                    return nextCursor.map(cursor -> fetchFindingsPage(dataPullTask, cursor))
                                     .orElseGet(Mono::empty);
                });
    }

    private Mono<JsonNode> fetchFindingsPage(final WizDataPullTask dataPullTask, final String cursor) {
        final String query = getGraphqlQuery(dataPullTask);
        final Map<String, Object> queryVars = getQueryVars(dataPullTask, cursor);

        // GraphQL is just a POST request with a specific body structure
        GraphQLReuqest graphQLBody = GraphQLReuqest.builder()
                                                   .query(query)
                                                   .variables(queryVars)
                                                   .build();

        ApiRequest request = ApiRequest.builder()
                                       .url(getApiUrl(dataPullTask))
                                       .httpMethod(HttpMethod.POST)
                                       .headers(Map.of(
                                               AUTHORIZATION, String.format("%s %s", REQUEST_HEADER_AUTH_BEARER, dataPullTask.getAuthToken())
                                       ))
                                       .body(graphQLBody)
                                       .build();

        return apiClient.execute(request)
                        .flatMap(response -> {
                            if (response.isSuccess()) {
                                return Mono.just(response.getDataAsJsonNode());
                            } else {
                                return Mono.error(new Exception(
                                        String.format("API call failed with status %d: %s",
                                                response.getStatusCode(),
                                                response.getErrorMessage())
                                ));
                            }
                        });
    }

    private Optional<String> extractNextCursor(JsonNode response) {
        try {
            // Navigate through the response structure to find the cursor
            JsonNode dataNode = response.path("data");
            if (!dataNode.isMissingNode()) {
                // Get the first field under data (e.g., "vulnerabilities" or "issues")
                Iterator<Map.Entry<String, JsonNode>> fields = dataNode.fields();
                if (fields.hasNext()) {
                    JsonNode resultNode = fields.next().getValue();
                    JsonNode pageInfo = resultNode.path("pageInfo");

                    if (pageInfo.path("hasNextPage").asBoolean(false)) {
                        JsonNode endCursor = pageInfo.path("endCursor");
                        if (!endCursor.isMissingNode() && !endCursor.isNull()) {
                            return Optional.of(endCursor.asText());
                        }
                    }
                }
            }
        } catch (Exception e) { //TODO: remove this
            log.error("Error extracting next cursor from response", e);
        }
        return Optional.empty();
    }

    private String getApiUrl(final DataPullTask dataPullTask) {
        return Optional.ofNullable(dataPullTask.getApiUrl())
                       .orElseThrow(() -> new IllegalArgumentException(String.format(
                               "No API URL specified for data pull for integration=%s.",
                               dataPullTask.getIntegration())));
    }

    private String getGraphqlQuery(final WizDataPullTask dataPullTask) {
        return switch(dataPullTask.getFindingType()) {
            case VULNERABILITY -> GraphQLQueryLoader.loadQuery(DataSyncConstants.QUERY_VULNERABILITIES_DELTA_FILE_PATH);
            case ISSUE -> GraphQLQueryLoader.loadQuery(DataSyncConstants.QUERY_ISSUES_DELTA_FILE_PATH);
        };
    }

    private Map<String, Object> getQueryVars(final WizDataPullTask dataPullTask, final String cursor) {
        final Map<String, String> afterFilter = Map.of(
                QUERY_VAR_AFTER, getDataStartCutoffTime(dataPullTask)
        );

        final Map<String, Object> filterBy = new HashMap<>();
        switch (dataPullTask.getFindingType()) {
            case ISSUE -> filterBy.put(QUERY_VAR_CREATED_AT, afterFilter);
            case VULNERABILITY -> filterBy.put(QUERY_VAR_UPDATED_AT, afterFilter);
        }

        final Map<String, Object> queryVars = new HashMap<>(Map.of(
                QUERY_VAR_FIRST, getPageSize(),
                QUERY_VAR_FILTER_BY, filterBy
        ));
        if (cursor != null) {
            queryVars.put(QUERY_VAR_AFTER, cursor);
        }
        return queryVars;
    }

    private int getPageSize() {
        final int maxConfigDefinedPageSize = Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                                                     .map(DataSyncConfig.IntegrationsConfig::getWiz)
                                                     .map(DataSyncConfig.IntegrationsConfig.WizConfig::getFindingsApisMaxPageSize)
                                                     .orElse(WIZ_DEFAULT_PAGE_SIZE);
        return Integer.min(WIZ_MAX_PAGE_SIZE, maxConfigDefinedPageSize);
    }

    private String getDataStartCutoffTime(final DataPullTask dataPullTask) {
        final Duration onboardSyncDataWindowLength = Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                                                             .map(DataSyncConfig.IntegrationsConfig::getWiz)
                                                             .map(DataSyncConfig.IntegrationsConfig.WizConfig::getOnboardSyncDataWindowLength)
                                                             .orElse(WIZ_DEFAULT_ONBOARD_SYNC_DATA_WINDOW_LENGTH);
        return Optional.ofNullable(dataPullTask.getPriorSuccessfulSync())
                       .map(TaskBasicMetadata::getStartTime)
                       .orElse(DateTimeUtil.generateTimestamp(Instant.now().minus(onboardSyncDataWindowLength)));
    }



}
