package com.illumio.data.components.wiz;

import com.illumio.data.components.IDataPuller;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.DataPullTask;
import com.illumio.data.model.wiz.*;
import com.illumio.data.util.GraphQLQueryLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

import static com.illumio.data.util.DataSyncConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizProjectsDataPuller implements IDataPuller<WizProjectsResponseNode, DataPullTask> {

    private final WebClient webClient;
    private final DataSyncConfig dataSyncConfig;

    @Override
    @LogReactiveExecutionTime
    public Flux<WizProjectsResponseNode> pullData(final DataPullTask dataPullTask) {
        return fetchProjectsPage(dataPullTask, null)
                .expand(response -> {
                    final Optional<String> nextCursor = getNextCursor(response);
                    return nextCursor.map(cursor -> fetchProjectsPage(dataPullTask, cursor))
                                     .orElseGet(Mono::empty);
                })
                .flatMap(response -> Flux.fromIterable(getProjects(response)));
    }

    private Mono<WizProjectsResponse> fetchProjectsPage(final DataPullTask dataPullTask, final String cursor) {
        final String query = GraphQLQueryLoader.loadQuery(QUERY_WIZ_PROJECTS_FILE_PATH);
        final Map<String, Object> queryVars = getQueryVars(cursor);

        final String authHeaderVal = String.format("%s %s", REQUEST_HEADER_AUTH_BEARER, dataPullTask.getAuthToken());
        return webClient.post()
                .uri(dataPullTask.getApiUrl())
                .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.AUTHORIZATION, authHeaderVal)
                .bodyValue(
                        Map.of(
                                REQUEST_BODY_VAR_QUERY, query,
                                REQUEST_BODY_VAR_VARIABLES, queryVars))
                .retrieve()
                .bodyToMono(WizProjectsResponse.class);
    }

    private Map<String, Object> getQueryVars(final String cursor) {
        final Map<String, Object> queryVars = new HashMap<>(
                Collections.singletonMap(QUERY_VAR_FIRST, getPageSize()));
        if (cursor != null) {
            queryVars.put(QUERY_VAR_AFTER, cursor);
        }
        return queryVars;
    }

    private int getPageSize() {
        final int maxConfigDefinedPageSize = Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                .map(DataSyncConfig.IntegrationsConfig::getWiz)
                .map(DataSyncConfig.IntegrationsConfig.WizConfig::getProjectsApiMaxPageSize)
                .orElse(WIZ_DEFAULT_PROJECTS_PAGE_SIZE);
        return Integer.min(WIZ_MAX_PROJECTS_PAGE_SIZE, maxConfigDefinedPageSize);
    }

    private Optional<String> getNextCursor(final WizProjectsResponse projectsResponse) {
        return Optional.ofNullable(projectsResponse)
                       .map(WizProjectsResponse::getData)
                       .map(WizProjectsResponseData::getProjects)
                       .map(WizProjectsResponseProjects::getPageInfo)
                       .filter(WizPageInfo::isHasNextPage)
                       .map(WizPageInfo::getEndCursor);
    }

    private List<WizProjectsResponseNode> getProjects(final WizProjectsResponse projectsResponse) {
        return Optional.ofNullable(projectsResponse)
                       .map(WizProjectsResponse::getData)
                       .map(WizProjectsResponseData::getProjects)
                       .map(WizProjectsResponseProjects::getNodes)
                       .orElseGet(Collections::emptyList);
    }

}
