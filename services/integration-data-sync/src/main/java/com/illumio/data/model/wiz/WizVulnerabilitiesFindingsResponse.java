package com.illumio.data.model.wiz;

import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WizVulnerabilitiesFindingsResponse extends WizFindingsResponse<WizVulnerabilityFinding> {

    private WizVulnerabilitiesData data;

    @Override
    public Optional<String> getNextCursor() {
        return Optional.ofNullable(getData())
                .map(WizVulnerabilitiesData::getVulnerabilityFindings)
                .map(WizVulnerabilityFindings::getPageInfo)
                .filter(WizPageInfo::isHasNextPage)
                .map(WizPageInfo::getEndCursor);
    }

    @Override
    public List<WizVulnerabilityFinding> getFindings() {
        return Optional.ofNullable(getData())
                       .map(WizVulnerabilitiesData::getVulnerabilityFindings)
                       .map(WizVulnerabilityFindings::getNodes)
                       .orElseGet(Collections::emptyList);
    }

}