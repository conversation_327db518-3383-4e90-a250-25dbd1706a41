package com.illumio.data.components.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.wiz.WizDeltaDataSyncService;
import com.illumio.data.components.wiz.WizGreenfieldAsyncTaskStarter;
import com.illumio.data.components.wiz.WizGreenfieldAsyncTaskStatusChecker;
import com.illumio.data.components.wiz.WizGreenfieldDataSyncService;
import com.illumio.data.model.SyncTask;
import com.illumio.data.util.Util;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskReceiverService {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final WizGreenfieldAsyncTaskStarter wizGreenfieldAsyncTaskStarter;
    private final WizGreenfieldAsyncTaskStatusChecker wizGreenfieldAsyncTaskStatusChecker;
    private final WizGreenfieldDataSyncService wizGreenfieldDataSyncService;
    private final WizDeltaDataSyncService wizDeltaDataSyncService;
    private final ObjectMapper objectMapper;

    @PostConstruct
    public void consumeTaskMessages() {
        kafkaReceiver.receive()
                     .doOnNext(taskRecord -> log.debug("Received task message: key={}, value={}",
                             taskRecord.key(), taskRecord.value()))
                     .flatMap(taskRecord -> {
                         final SyncTask syncTask = syncTaskFromKafkaRecord(taskRecord);
                         return handleSyncTask(syncTask, () -> taskRecord.receiverOffset().acknowledge())
                                 .onErrorResume(e -> {
                                     log.error("Error occurred while consuming task message for syncTask={}. " +
                                             "Continuing with consumption of next task...", syncTask, e);
                                     return Mono.empty();
                                 });
                     })
                     .subscribe();
    }

    @SneakyThrows
    private SyncTask syncTaskFromKafkaRecord(final ReceiverRecord<String, String> record) {
        return objectMapper.readValue(record.value(), SyncTask.class);
    }

    private Mono<Void> handleSyncTask(final SyncTask syncTask, final Runnable kafkaRecordAcknowledger) {
        return switch (syncTask.getIntegration()) {
            case WIZ ->
                    switch (syncTask.getTaskStatus()) {
                        case SCHEDULED ->
                                switch (syncTask.getTaskType()) {
                                    case GREENFIELD -> wizGreenfieldDataSyncService.handleSyncTask(syncTask, kafkaRecordAcknowledger);
                                    case DELTA -> wizDeltaDataSyncService.handleSyncTask(syncTask, kafkaRecordAcknowledger);
                                };
                        case SCHEDULED_ASYNC -> wizGreenfieldAsyncTaskStarter.startGreenfieldAsyncTask(syncTask, kafkaRecordAcknowledger);
                        case CHECKING_STATUS_ASYNC -> wizGreenfieldAsyncTaskStatusChecker.checkGreenfieldAsyncTaskStatus(syncTask, kafkaRecordAcknowledger);
                        default -> Mono.error(new IllegalArgumentException(String.format("Sync task received with unsupported taskStatus=%s on integration WIZ", syncTask.getTaskStatus())));
                    };
            case ARMIS -> Mono.error(new IllegalArgumentException("Sync task received for unsupported integration ARMIS"));
        };
    }

}
