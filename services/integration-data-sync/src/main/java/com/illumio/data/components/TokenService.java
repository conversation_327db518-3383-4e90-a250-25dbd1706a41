package com.illumio.data.components;

import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TenantConfigurations;
import com.illumio.data.model.Token;
import com.illumio.data.model.TokenRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class TokenService {

    private final CredentialsService credentialsService;
    private final TokenGenerationService tokenGenerationService;

    public Mono<Token> getAuthToken(final SyncTask syncTask) {
        return credentialsService
                .getCredentials(syncTask.getCredentialsVaultPath())
                .map(credentials -> TokenRequest.builder()
                                                .tenantId(syncTask.getTenantId())
                                                .integration(syncTask.getIntegration())
                                                .credentials(credentials)
                                                .authUrl(Optional.ofNullable(syncTask.getTenantConfigurations())
                                                                 .map(TenantConfigurations::getAuthUrl)
                                                                 .orElse(null))
                                                .build())
                .flatMap(tokenGenerationService::getToken);
    }

}
