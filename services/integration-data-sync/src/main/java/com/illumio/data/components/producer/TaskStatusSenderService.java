package com.illumio.data.components.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.TaskStatus;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
public class TaskStatusSenderService {

    private final KafkaSender<String, String> kafkaSender;
    private final DataSyncConfig dataSyncConfig;

    public TaskStatusSenderService(@Qualifier("kafkaTaskStatusSender") KafkaSender<String, String> kafkaSender,
                                   DataSyncConfig dataSyncConfig) {
        this.kafkaSender = kafkaSender;
        this.dataSyncConfig = dataSyncConfig;
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendTaskStatusMessage(final TaskStatus taskStatus) {
        return kafkaSender
                .send(Mono.just(senderRecord(taskStatus)))
                .doOnError(throwable ->
                        log.error("Could not send Kafka Task Status Message for taskId={}, tenantId={}, integration={}. updatedStatus={}",
                                taskStatus.getTaskId(), taskStatus.getTenantId(), taskStatus.getIntegration(), taskStatus.getStatusType(), throwable))
                .doOnComplete(() ->
                        log.debug("Successfully sent Kafka Task Status Message for taskId={}, tenantId={}, integration={}. updatedStatus={}",
                                taskStatus.getTaskId(), taskStatus.getTenantId(), taskStatus.getIntegration(), taskStatus.getStatusType()))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> senderRecord(final TaskStatus taskStatus) {
        final String topic = dataSyncConfig.getKafkaTaskStatusProducerConfig().getTopic();
        final String message = new ObjectMapper().writeValueAsString(taskStatus);
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, message);
        return SenderRecord.create(producerRecord, null);
    }

}
