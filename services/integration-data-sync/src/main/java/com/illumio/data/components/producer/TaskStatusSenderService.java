package com.illumio.data.components.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskStatus;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.util.Util;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
public class TaskStatusSenderService {

    private final KafkaSender<String, String> kafkaSender;
    private final DataSyncConfig dataSyncConfig;
    private final ObjectMapper objectMapper;

    public TaskStatusSenderService(@Qualifier("kafkaTaskStatusSender") final KafkaSender<String, String> kafkaSender,
                                   final DataSyncConfig dataSyncConfig,
                                   final ObjectMapper objectMapper) {
        this.kafkaSender = kafkaSender;
        this.dataSyncConfig = dataSyncConfig;
        this.objectMapper = objectMapper;
    }

    @RetryReactiveOnError
    @LogReactiveExecutionTime
    public Mono<Void> sendTaskStatusMessage(final TaskStatus taskStatus) {
        return kafkaSender
                .send(Mono.just(senderRecord(taskStatus)))
                .doOnError(throwable ->
                        log.error("Could not send Kafka Task Status Message for taskStatus={}", taskStatus, throwable))
                .doOnComplete(() ->
                        log.debug("Successfully sent Kafka Task Status Message for taskStatus={}", taskStatus))
                .then();
    }

    public Mono<Void> sendTaskStatusMessage(final SyncTask syncTask, final TaskStatusType statusType, final String statusMessage) {
        return sendTaskStatusMessage(TaskStatus.builder()
                                               .taskId(syncTask.getTaskId())
                                               .integration(syncTask.getIntegration())
                                               .tenantId(syncTask.getTenantId())
                                               .taskType(syncTask.getTaskType())
                                               .statusType(statusType)
                                               .statusMessage(statusMessage)
                                               .statusUpdateTime(Util.generateCurrentTimestamp())
                                               .build());
    }

    @SneakyThrows
    private SenderRecord<String, String, String> senderRecord(final TaskStatus taskStatus) {
        final String topic = dataSyncConfig.getKafkaTaskStatusProducerConfig().getTopic();
        final String message = objectMapper.writeValueAsString(taskStatus);
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, message);
        return SenderRecord.create(producerRecord, null);
    }

}
