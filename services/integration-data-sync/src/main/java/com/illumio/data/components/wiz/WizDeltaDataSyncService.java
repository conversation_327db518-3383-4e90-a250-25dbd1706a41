package com.illumio.data.components.wiz;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.FindingsSenderService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.Token;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.wiz.WizDataPullTask;
import com.illumio.data.model.wiz.WizFinding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Slf4j
@Service
public class WizDeltaDataSyncService extends WizDataSyncer {

    private final WizDeltaDataPuller wizDeltaDataPuller;

    @Autowired
    public WizDeltaDataSyncService(final TokenService tokenService,
                                   final TaskStatusSenderService taskStatusSenderService,
                                   final DataSyncFailureHandler dataSyncFailureHandler,
                                   final ObjectMapper objectMapper,
                                   final FindingsSenderService findingsSenderService,
                                   final WizDeltaDataPuller wizDeltaDataPuller) {
        super(tokenService, taskStatusSenderService, dataSyncFailureHandler, objectMapper, findingsSenderService);
        this.wizDeltaDataPuller = wizDeltaDataPuller;
    }

    @Override
    public Flux<WizFinding> getIntegrationData(final SyncTask syncTask, final Token token) {
        final WizDataPullTask vulnerabilitiesDataPullTask =
                WizDataPullTask.fromSyncTask(syncTask, token, FindingType.VULNERABILITY, null);
        final WizDataPullTask issuesDataPullTask =
                WizDataPullTask.fromSyncTask(syncTask, token, FindingType.ISSUE, null);
        return wizDeltaDataPuller.pullData(vulnerabilitiesDataPullTask)
                                 .concatWith(wizDeltaDataPuller.pullData(issuesDataPullTask));
    }

}