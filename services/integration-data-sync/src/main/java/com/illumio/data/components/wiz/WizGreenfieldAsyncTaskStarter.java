package com.illumio.data.components.wiz;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DataSyncFailureHandler;
import com.illumio.data.components.TokenService;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.configuration.DataSyncConfig;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.*;
import com.illumio.data.model.wiz.*;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.util.DataSyncConstants.WIZ_DEFAULT_MIN_TIME_BETWEEN_REPORT_CREATION_CALLS;
import static com.illumio.data.util.DataSyncConstants.WIZ_JWT_TOKEN_PROJECT_IDS_ALL_PROJECTS;
import static com.illumio.data.util.DataSyncConstants.WIZ_JWT_TOKEN_PROJECT_IDS_FIELD;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizGreenfieldAsyncTaskStarter {

    private final TokenService tokenService;
    private final WizProjectsDataPuller wizProjectsDataPuller;
    private final WizReportGenerator wizReportGenerator;
    private final TaskStatusSenderService taskStatusSenderService;
    private final ObjectMapper objectMapper;
    private final DataSyncFailureHandler dataSyncFailureHandler;
    private final DataSyncConfig dataSyncConfig;

    @LogReactiveExecutionTime
    public Mono<Void> startGreenfieldAsyncTask(final SyncTask syncTask, final Runnable onTaskStartedUpdateSent) {
        return taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.STARTED_ASYNC, "")
                                      .doOnSuccess(v -> onTaskStartedUpdateSent.run())
                                      .then(tokenService.getAuthToken(syncTask))
                                      .flatMapMany(token -> getReportDataPullTasks(syncTask, token))
                                      // delay between each call to prevent rate limiting errors if many reports generated at once
                                      .delayElements(getMinTimeBetweenReportCreationCalls())
                                      .flatMap(wizReportGenerator::startReportGeneration)
                                      .collectList()
                                      .flatMap(wizReportIdentifiers ->
                                              taskStatusSenderService.sendTaskStatusMessage(
                                                      syncTask, TaskStatusType.AWAITING_ASYNC,
                                                      Util.serializeWizAwaitingAsyncStatusMessage(wizReportIdentifiers, objectMapper)))
                                      .retryWhen(dataSyncFailureHandler.getRetrySpec(syncTask))
                                      .then();
    }

    private Flux<WizDataPullTask> getReportDataPullTasks(final SyncTask syncTask, final Token token) {
        final WizDataPullTask baseDataPullTask =
                WizDataPullTask.fromSyncTask(syncTask, token, null, null);
        final WizDataPullTask vulnerabilitiesDataPullTask = baseDataPullTask.toBuilder()
                                                                          .findingType(FindingType.VULNERABILITY)
                                                                          .build();
        final WizDataPullTask issuesDataPullTask = baseDataPullTask.toBuilder()
                                                                          .findingType(FindingType.ISSUE)
                                                                          .build();
        if (wizTokenHasAccessToAllProjects(token, objectMapper)) {
            return Flux.just(vulnerabilitiesDataPullTask, issuesDataPullTask);
        } else {
            // Request projects directly from Wiz API as the token based approach is not documented and hence no contractual guarantee
            return wizProjectsDataPuller.pullData(baseDataPullTask)
                                        .map(WizProjectsResponseNode::getId)
                                        .flatMap(projectId -> Flux.just(
                                                vulnerabilitiesDataPullTask.toBuilder()
                                                                .projectId(projectId)
                                                                .build(),
                                                issuesDataPullTask.toBuilder()
                                                                .projectId(projectId)
                                                                .build()));
        }
    }

    @SneakyThrows
    public boolean wizTokenHasAccessToAllProjects(final Token token, final ObjectMapper objectMapper) {
        return Optional.ofNullable(token.getAccessToken())
                       .map(JWT::decode)
                       .map(DecodedJWT::getPayload)
                       .map(payload -> WIZ_JWT_TOKEN_PROJECT_IDS_ALL_PROJECTS.equals(
                               getProjectIdsFromWizTokenPayload(payload, objectMapper)))
                       .orElse(false);

    }

    @SneakyThrows
    private String getProjectIdsFromWizTokenPayload(final String tokenPayload, final ObjectMapper objectMapper) {
        final String decodedTokenPayload = new String(Base64.getDecoder().decode(tokenPayload));
        final Map<String, Object> tokenPayloadMap = objectMapper.readValue(decodedTokenPayload, Map.class);
        return tokenPayloadMap.get(WIZ_JWT_TOKEN_PROJECT_IDS_FIELD).toString();
    }

    private Duration getMinTimeBetweenReportCreationCalls() {
        return Optional.ofNullable(dataSyncConfig.getIntegrationsConfig())
                .map(DataSyncConfig.IntegrationsConfig::getWiz)
                .map(DataSyncConfig.IntegrationsConfig.WizConfig::getMinTimeBetweenReportCreationCalls)
                .orElse(WIZ_DEFAULT_MIN_TIME_BETWEEN_REPORT_CREATION_CALLS);
    }

}