package com.illumio.data.components.wiz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.wiz.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizGreenfieldDataPuller {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    @SneakyThrows
    @LogReactiveExecutionTime
    public Flux<WizFinding> pullData(@NonNull final WizReportUrl reportUrl) {
        if (Objects.isNull(reportUrl.getUrl())
                || Objects.isNull(reportUrl.getFindingType())) {
            return Flux.error(new IllegalArgumentException("Report URL must have both a URL and findings type associated."));
        }

        return webClient.get()
                        .uri(new URI(reportUrl.getUrl()))
                        .retrieve()
                        .onStatus(HttpStatusCode::isError, response ->
                                response.bodyToMono(String.class)
                                        .flatMap(body -> Mono.error(new ExternalServiceException(body))))
                        .bodyToFlux(DataBuffer.class)
                        .map(dataBuffer -> {
                            byte[] bytes = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(bytes);
                            DataBufferUtils.release(dataBuffer);
                            return new String(bytes, StandardCharsets.UTF_8);
                        })
                        .transform(this::splitReportByLines)
                        .transform(reportLines -> parseReportLines(reportLines, reportUrl.getFindingType()));
    }

    private Flux<String> splitReportByLines(final Flux<String> reportChunks) {
        StringBuilder buffer = new StringBuilder();

        return reportChunks.flatMapSequential(chunk -> {
            List<String> lines = new ArrayList<>();
            buffer.append(chunk);

            int lineEnd;
            while ((lineEnd = buffer.indexOf("\n")) >= 0) {
                String line = buffer.substring(0, lineEnd);
                lines.add(line);
                buffer.delete(0, lineEnd + 1);
            }

            return Flux.fromIterable(lines);
        });
    }

    private Flux<WizFinding> parseReportLines(final Flux<String> reportLineItems, final FindingType findingType) {
        return Flux.create(sink -> reportLineItems.subscribe(
                line -> {
                    try {
                        WizFinding finding = switch (findingType) {
                            case VULNERABILITY -> objectMapper.readValue(line, WizVulnerabilityFinding.class);
                            case ISSUE -> objectMapper.readValue(line, WizReportIssue.class).toIssueFinding();
                        };
                        sink.next(finding);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException("Received finding from Wiz does not conform to expected schema.");
                    }
                },
                sink::error,
                sink::complete
        ));
    }

}
