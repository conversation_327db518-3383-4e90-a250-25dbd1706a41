package com.illumio.data.components.wiz;

import com.illumio.data.exception.ExternalServiceException;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.FindingType;
import com.illumio.data.model.wiz.*;
import com.illumio.data.util.GraphQLQueryLoader;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.illumio.data.util.DataSyncConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class WizReportGenerator {

    private final WebClient webClient;

    @LogReactiveExecutionTime
    public Mono<WizReportIdentifier> startReportGeneration(final WizDataPullTask dataPullTask) {
        final String query = GraphQLQueryLoader.loadQuery(QUERY_WIZ_GREENFIELD_REPORT_CREATE_FILE_PATH);
        final Map<String, Object> queryVars = getQueryVars(dataPullTask);

        final String authHeaderVal = String.format("%s %s", REQUEST_HEADER_AUTH_BEARER, dataPullTask.getAuthToken());
        return webClient.post()
                        .uri(dataPullTask.getApiUrl())
                        .header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                        .header(HttpHeaders.AUTHORIZATION, authHeaderVal)
                        .bodyValue(
                                Map.of(
                                        REQUEST_BODY_VAR_QUERY, query,
                                        REQUEST_BODY_VAR_VARIABLES, queryVars))
                        .retrieve()
                        .onStatus(HttpStatusCode::isError, response ->
                                response.bodyToMono(String.class)
                                        .flatMap(body -> Mono.error(new ExternalServiceException(body))))
                        .bodyToMono(WizCreateReportResponse.class)
                        .flatMap(report -> {
                            Optional<String> errorMessage = extractErrorsFromResponse(report);
                            return errorMessage
                                    .<Mono<? extends WizCreateReportResponse>>map(
                                            msg -> Mono.error(new ExternalServiceException(msg)))
                                    .orElseGet(() -> Mono.just(report));
                        })
                        .map(createReportResponse ->
                                reportIdentifierFromWizResponse(createReportResponse, dataPullTask.getFindingType()));
    }

    private Map<String, Object> getQueryVars(final WizDataPullTask dataPullTask) {
        final String reportName = String.format("%s-%s", dataPullTask.getFindingType(), Util.generateCurrentTimestamp());
        final String reportType = switch (dataPullTask.getFindingType()) {
            case ISSUE -> WIZ_REPORT_TYPE_ISSUES;
            case VULNERABILITY -> WIZ_REPORT_TYPE_VULNERABILITIES;
        };
        final HashMap<String, Object> reportParams = new HashMap<>(Map.of(
                QUERY_VAR_REPORT_NAME, reportName,
                QUERY_VAR_REPORT_TYPE, reportType,
                QUERY_VAR_REPORT_FORMAT, QUERY_VAL_REPORT_FORMAT,
                QUERY_VAR_REPORT_INCREMENTAL, false
        ));
        if (Objects.nonNull(dataPullTask.getProjectId())) {
            reportParams.put(QUERY_VAR_PROJECT_ID, dataPullTask.getProjectId());
        }
        if (dataPullTask.getFindingType().equals(FindingType.ISSUE)) {
            reportParams.put(QUERY_VAR_REPORT_ISSUE_PARAMS,
                    Collections.singletonMap(QUERY_VAR_REPORT_TYPE, QUERY_VAL_REPORT_ISSUE_PARAMS_TYPE));
        } else if (dataPullTask.getFindingType().equals(FindingType.VULNERABILITY)) {
            reportParams.put(QUERY_VAR_REPORT_COLUMNS, WIZ_VULNERABILITY_REPORT_COLUMNS);
        }
        return Collections.singletonMap(QUERY_VAR_REPORT_PARAMS, reportParams);
    }

    private Optional<String> extractErrorsFromResponse(final WizCreateReportResponse response) {
        if (Optional.ofNullable(response.getErrors()).isEmpty()) {
            return Optional.empty();
        }
        return Optional.of("Wiz error(s): " + response.getErrors().stream().
                                                      map(wizError -> Optional.ofNullable(wizError)
                                                                              .map(WizError::getMessage)
                                                                              .orElse(null))
                                                      .collect(Collectors.joining(", ")));
    }

    private WizReportIdentifier reportIdentifierFromWizResponse(final WizCreateReportResponse response,
                                                                final FindingType findingType) {
        return WizReportIdentifier.builder()
                                  .reportId(Optional.ofNullable(response)
                                                    .map(WizCreateReportResponse::getData)
                                                    .map(WizCreateReportResponseData::getCreateReport)
                                                    .map(WizCreateReportResponseCreatedReport::getReport)
                                                    .map(WizCreateReportResponseReport::getId)
                                          .orElse(null))
                                  .findingType(findingType)
                                  .build();
    }

}
