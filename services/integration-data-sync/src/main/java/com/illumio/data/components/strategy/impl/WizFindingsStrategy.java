package com.illumio.data.components.strategy.impl;

import com.illumio.data.components.strategy.FetchIntegrationsDataStrategy;
import com.illumio.data.model.Integration;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
public class WizFindingsStrategy implements FetchIntegrationsDataStrategy {
    @Override
    public Flux<String> fetchFindings(Integration integration) {
        return null;
    }
}
