package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.producer.TaskStatusSenderService;
import com.illumio.data.logging.LogReactiveExecutionTime;
import com.illumio.data.model.SyncTask;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.Token;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@RequiredArgsConstructor
public abstract class DataSyncer<IntegrationDataPoint, MappedInternalDataPoint, SyncSuccessStatsContainer> {

    private final TokenService tokenService;
    private final TaskStatusSenderService taskStatusSenderService;
    private final DataSyncFailureHandler dataSyncFailureHandler;
    private final ObjectMapper objectMapper;
    private final Supplier<SyncSuccessStatsContainer> successSummaryStatsContainerSupplier;
    private final BiConsumer<SyncSuccessStatsContainer, MappedInternalDataPoint> successSummaryStatsCollector;
    private final BiFunction<SyncSuccessStatsContainer, ObjectMapper, String> successStatusMessageGenerator;

    public abstract Flux<IntegrationDataPoint> getIntegrationData(SyncTask syncTask, Token token);

    public abstract MappedInternalDataPoint mapDataPoint(IntegrationDataPoint integrationDataPoint, SyncTask syncTask);

    public abstract Mono<Void> consumeData(Flux<MappedInternalDataPoint> dataFlux);

    @LogReactiveExecutionTime
    public Mono<Void> handleSyncTask(SyncTask syncTask, Runnable onTaskStartedUpdateSent) {
        return taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.STARTED, "")
                .doOnSuccess(v -> onTaskStartedUpdateSent.run())
                .thenMany(getDataFlux(syncTask))
                .transform(dataFlux -> propagateDataToConsumer(dataFlux, syncTask))
                .retryWhen(dataSyncFailureHandler.getRetrySpec(syncTask))
                .then();
    }

    public Flux<MappedInternalDataPoint> getDataFlux(final SyncTask syncTask) {
        return tokenService.getAuthToken(syncTask)
                .flatMapMany(token -> getIntegrationData(syncTask, token))
                .map(dataPoint -> mapDataPoint(dataPoint, syncTask));
    }

    private Mono<Void> propagateDataToConsumer(final Flux<MappedInternalDataPoint> dataFlux, final SyncTask syncTask) {
        SyncSuccessStatsContainer syncSuccessStatsContainer = successSummaryStatsContainerSupplier.get();

        return dataFlux
                .doOnNext(dataPoint -> successSummaryStatsCollector.accept(syncSuccessStatsContainer, dataPoint))
                .transform(this::consumeData)
                .doOnComplete(() -> handleDataSyncSuccess(syncTask, syncSuccessStatsContainer))
                .then();
    }

    private void handleDataSyncSuccess(final SyncTask syncTask, final SyncSuccessStatsContainer syncSuccessStatsContainer) {
        final String successStatusMessage = successStatusMessageGenerator.apply(syncSuccessStatsContainer, objectMapper);
        taskStatusSenderService.sendTaskStatusMessage(syncTask, TaskStatusType.SUCCESS, successStatusMessage)
                .subscribe();
        log.info("Successfully completed data sync for taskId={}, tenantId={}, integration={} with successStatusMessage={}",
                syncTask.getTaskId(), syncTask.getTenantId(), syncTask.getIntegration(), successStatusMessage);
    }

}
