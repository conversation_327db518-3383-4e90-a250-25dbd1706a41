package com.illumio.data.model;

import java.util.Optional;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder(toBuilder = true)
public class DataPullTask {

    private Integration integration;
    private String apiUrl;
    private String authToken;
    private TaskBasicMetadata priorSuccessfulSync;

    public String getApiUrl() {
        return Optional.ofNullable(apiUrl)
                       .orElseThrow(() -> new IllegalArgumentException(String.format(
                               "No API URL specified for data pull for integration=%s.",
                               getIntegration())));
    }

}
