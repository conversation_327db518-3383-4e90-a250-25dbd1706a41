  query IssuesPage($filterBy: IssueFilters, $first: Int, $after: String, $orderBy: IssueOrder) {
    issues : issuesV2(
        filterBy: $filterBy
        first: $first
        after: $after
        orderBy: $orderBy
    ) {
      nodes {
        id
        status
        severity
        sourceRules {
          name
          risks
        }
        resource : entitySnapshot {
          name
          type
          providerUniqueId : providerId
          subscriptionExternalId
          cloudPlatform
          cloudProviderURL
        }
     }
     pageInfo {
       hasNextPage
       endCursor
     }
   }
 }