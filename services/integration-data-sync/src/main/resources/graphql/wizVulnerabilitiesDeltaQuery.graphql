  query VulnerabilityFindingsPage($filterBy: VulnerabilityFindingFilters, $first: Int, $after: String, $orderBy: VulnerabilityFindingOrder) {
    vulnerabilityFindings(
      filterBy: $filterBy
      first: $first
      after: $after
      orderBy: $orderBy
    ) {
      nodes {
        status
        name
        detailedName
        CVEDescription
        CVSSSeverity
        score
        severity
        exploitabilityScore
        hasExploit
        hasCisaKevExploit
        impactScore
        firstDetectedAt
        lastDetectedAt
        vulnerableAsset {
          ... on VulnerableAssetBase {
            name
            type
            providerUniqueId
            subscriptionExternalId
            cloudPlatform
            cloudProviderURL
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }