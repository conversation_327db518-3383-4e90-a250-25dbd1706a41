logging:
  level:
    ROOT: DEBUG
spring:
  application:
    name: integration-data-sync
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
vault:
  uri: "http://127.0.0.1:8200"
  token: DO_NOT_COMMIT

server:
  port: 8082

retry-config:
  min-backoff: 30s
  max-retries: 2

integration-data-sync:
  integrations-config:
    wiz:
      projects-api-max-page-size: 1
      findings-apis-max-page-size: 50
      sync-data-window-length-fallback: 1d
      min-time-between-report-creation-calls: 1s

  kafka-task-status-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
    topic: test-task-status-message
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";

  kafka-findings-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
    topic: test-vulnerabilities
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";

  task-consumer-mode: HEAVY # options: LIGHT, HEAVY; determines whether to use light-task-consumer-config or heavy-task-consumer-config

  kafka-light-task-consumer-config:
    topic: task_queue
    isConnectionString: true
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    groupId: integration-data-sync
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";

  kafka-heavy-task-consumer-config:
    topic: test-integrations-heavy-tasks
    isConnectionString: true
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    groupId: integration-data-sync
    autoOffsetReset: earliest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";