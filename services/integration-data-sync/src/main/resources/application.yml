testingyumikeycommit: test
logging:
  level:
    ROOT: INFO

spring:
  application:
    name: integration-data-sync
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
vault:
  uri: "http://127.0.0.1:8200"
  token: DO_NOT_COMMIT
  namespace: admin

retry-config:
  user-operations:
    min-backoff: 2s
    max-retries: 2
  system-operations:
    min-backoff: 30s
    max-retries: 2

web-client-config:
  max-in-memory-size: 6000000

integration-data-sync:
  integrations-config:
    wiz:
      findings-apis-max-page-size: 50
      onboard-sync-data-window-length: 1d

  kafka-task-status-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
    topic: task-status-update-queue
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";

  kafka-findings-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
    topic: test-vulnerabilities
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
  kafka-light-task-consumer-config:
    topic: light-task-queue
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    groupId: integration-data-sync
  kafka-heavy-task-consumer-config:
    topic: heavy-task-queue
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    groupId: integration-data-sync