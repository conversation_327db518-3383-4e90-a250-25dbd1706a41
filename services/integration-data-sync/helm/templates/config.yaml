apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "IntegrationDataSync.fullname" . }}-env-configmap
  labels:
    {{- include "IntegrationDataSync.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "integration-data-sync"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    server:
      port: {{.Values.server.port}}
    vault:
      uri: "{{.Values.vault.uri}}"
      namespace: "{{.Values.vault.namespace}}"
    retry-config:
      user-operations:
        min-backoff: "{{.Values.retryConfig.userOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.userOperations.maxRetries}}
      system-operations:
        min-backoff: "{{.Values.retryConfig.systemOperations.minBackoff}}"
        max-retries: {{.Values.retryConfig.systemOperations.maxRetries}}
    web-client-config:
      max-in-memory-size: {{.Values.webClientConfig.maxInMemorySize}}
    
    integration-data-sync:
      integrations-config:
        wiz:
          projects-api-max-page-size: {{.Values.integrationDataSync.integrationsConfig.wiz.projectsApiMaxPageSize}}
          findings-apis-max-page-size: {{.Values.integrationDataSync.integrationsConfig.wiz.findingsApisMaxPageSize}}
          sync-data-window-length-fallback: "{{.Values.integrationDataSync.integrationsConfig.wiz.syncDataWindowLengthFallback}}"
          min-time-between-report-creation-calls: "{{.Values.integrationDataSync.integrationsConfig.wiz.minTimeBetweenReportCreationCalls}}"
    
      kafka-light-task-consumer-config:
        bootstrapServers: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.topic}}"
        groupId: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.groupId}}"
        isConnectionString: {{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.isConnectionString}}
        autoOffsetReset: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.integrationDataSync.kafkaLightTaskConsumerConfig.maxPartitionFetchBytes}}"
    
      kafka-heavy-task-consumer-config:
        bootstrapServers: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.topic}}"
        groupId: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.groupId}}"
        isConnectionString: {{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.isConnectionString}}
        autoOffsetReset: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.integrationDataSync.kafkaHeavyTaskConsumerConfig.maxPartitionFetchBytes}}"
    
      kafka-task-status-producer-config:
        bootstrapServers: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.lingerMs}}"
        batchSize: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.integrationDataSync.kafkaTaskStatusProducerConfig.bufferMemory}}"

      kafka-findings-producer-config:
        bootstrapServers: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.bootstrapServers}}"
        topic: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.topic}}"
        isConnectionString: {{.Values.integrationDataSync.kafkaFindingsProducerConfig.isConnectionString}}
        requestTimeoutMs: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.lingerMs}}"
        batchSize: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.integrationDataSync.kafkaFindingsProducerConfig.bufferMemory}}"