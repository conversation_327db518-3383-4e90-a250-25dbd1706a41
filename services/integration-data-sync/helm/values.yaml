# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

deploymentGroups:
- name: light-task-consumer-group # must adhere to RFC 1123 naming convention (Kubernetes constraint)
  taskConsumerMode: LIGHT # options: LIGHT, HEAVY; determines whether to use light-task-consumer-config or heavy-task-consumer-config
  replicaCount: 2
- name: heavy-task-handler-group # must adhere to RFC 1123 naming convention (Kubernetes constraint)
  taskConsumerMode: HEAVY # options: LIGHT, HEAVY; determines whether to use light-task-consumer-config or heavy-task-consumer-config
  replicaCount: 1

logging:
  level:
    root: DEBUG
    kafka: DEBUG
server:
  port: 8080

ports:
- name: admin
  port: 8084
- name: rest
  port: 8080

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8080

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/integrations
  repositoryName: integration-data-sync
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS

vault:
  uri: "http://127.0.0.1:8200"
  token:    # should give at deployment time
  namespace: admin

retryConfig:
  userOperations:
    minBackoff: 2s
    maxRetries: 2
  systemOperations:
    minBackoff: 30s
    maxRetries: 2

webClientConfig:
  maxInMemorySize: 6000000

integrationDataSync:
  integrationsConfig:
    wiz:
      projectsApiMaxPageSize: 500
      findingsApisMaxPageSize: 50
      syncDataWindowLengthFallback: 1d
      minTimeBetweenReportCreationCalls: 1s

  kafkaLightTaskConsumerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "light-task-queue"
    isConnectionString: true
    connectionString:    # should give at deployment time

  kafkaHeavyTaskConsumerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "heavy-task-queue"
    isConnectionString: true
    connectionString:    # should give at deployment time

  kafkaTaskStatusProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "task-status-update-queue"
    isConnectionString: true
    connectionString:    # should give at deployment time

  kafkaFindingsProducerConfig:
    bootstrapServers: "test-arch-eventhub.servicebus.windows.net:9093"
    topic: "test-vulnerabilities"
    isConnectionString: true
    connectionString:    # should give at deployment time

extraLabels: {}