#
# Java/Gradle build image.
#
# We want the same base image until we don't. Specify image by sha instead of tag (which changes often)
FROM alpine:3

ENV LANG en_US.UTF-8
ENV LANGUAGE en_US:en
ENV LC_ALL en_US.UTF-8
ENV TZ=Etc/UTC
ARG ZULU_KEY_SHA256=6c6393d4755818a15cf055a5216cffa599f038cd508433faed2226925956509a
RUN wget --quiet https://cdn.azul.com/public_keys/<EMAIL> -P /etc/apk/keys/ && \
    echo "${ZULU_KEY_SHA256}  /etc/apk/keys/<EMAIL>" | sha256sum -c - && \
    apk --repository https://repos.azul.com/zulu/alpine --no-cache add zulu17-jre~=17.0.12 tzdata

ENV JAVA_HOME=/usr/lib/jvm/zulu17
ENV PATH=$JAVA_HOME/bin:$PATH

# These args are needed for gradle to run properly
ARG WORKSPACE
ENV XDG_CONFIG_HOME=${WORKSPACE}
ENV XDG_CACHE_HOME=${WORKSPACE}
ENV GRADLE_USER_HOME=${WORKSPACE}
ENV USER_HOME=${WORKSPACE}

RUN apk add docker git gradle shadow

# This is to set the right permissions for the jenkins user to run docker
USER root
ENV USER=jenkins
ENV GROUPNAME=jenkins
ENV UID=1002
ENV GID=1003

RUN addgroup \
    --gid "$GID" \
    "$GROUPNAME" \
&&  adduser \
    --disabled-password \
    --gecos "" \
    --home "$WORKSPACE" \
    --ingroup "$GROUPNAME" \
    --no-create-home \
    --uid "$UID" \
    $USER


RUN groupmod -g 1002 docker


RUN addgroup jenkins docker

