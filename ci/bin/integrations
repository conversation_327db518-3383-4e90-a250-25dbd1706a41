#!/usr/bin/env bash

set -u # Uninitialized variables are errors
set -o pipefail # ANY error in a pipeline makes the pipeline return error
set -e # Errors are fatal

function ::_facts() {
  local rp="$(realpath "$0")"
  local -r proj="$(basename "$rp")"

  # Jenkins runs will set these variables. Reference:
  #
  #     https://www.jenkins.io/doc/book/pipeline/jenkinsfile/#using-environment-variables
  #
  if [ -n "${WORKSPACE:-}" ] && [ -n "${JENKINS_URL:-}" ]; then
    echo "jenkins;${WORKSPACE};${proj}"
    return
  fi

  local -r root="$(dirname "$(dirname "$(dirname "$rp")")")"
  echo "user;${root};${proj}"
}

function preflight() {
  # Set globals
  IFS=';' read -r OTTO_INVOKER OTTO_REPO_ROOT OTTO_PROJ <<< "$(::_facts)"
  readonly OTTO_INVOKER OTTO_REPO_ROOT OTTO_PROJ

  # Source all libraries
  readonly OTTO_BASHLIB="${OTTO_REPO_ROOT}/ci/bash_lib"
  local lib
  for lib in $(find "${OTTO_BASHLIB}" -name "*.bash" | sort); do
    . "${lib}"
  done
  for lib in $(find "${OTTO_BASHLIB}" -path "*/after/*.bash" | sort); do
    . "${lib}"
  done

  # Make sure we have all dependencies installed
  dependencies::check
}

function main() {
  # Set globals, import shell libraries
  preflight

  if (( $# == 0 )); then
    log::warn "No arguments given. Dumping help"
    set -- help
  fi

  # Ensure that the command exists
  local cmd="${1:?}"
  shift
  arg::_required::type "command" "cmd"

  # Do work
  pushd "${OTTO_REPO_ROOT}" &>/dev/null || exit 1
  log::debug "Function is being invoked | cmd='${cmd}' OTTO_REPO_ROOT='${OTTO_REPO_ROOT}' OTTO_INVOKER='${OTTO_INVOKER}'"
  set +e; (
    set -e
    "command::${cmd}" "$@"
  )
  rc=$?
  set -e
  case ${rc} in
    0) log::debug "Command succeeded | cmd='${cmd}'" ;;
    *)
      if grep -q "^is_" <<< "${cmd}"; then
        # Special-case commands that start with 'is_*'. It is completely fine if
        # they return false
        log::debug "Command returned false | rc='${rc}' cmd='${cmd}' args='$*'"
      else
        log::err "Command failed | rc='${rc}' cmd='${cmd}' args='$*'"
        [ ${OTTO_ERR_LOGFILE:-} ] && echo >> "${OTTO_ERR_LOGFILE}"
      fi
      ;;
  esac
  popd &>/dev/null || true
  return ${rc}
}
main "$@"
