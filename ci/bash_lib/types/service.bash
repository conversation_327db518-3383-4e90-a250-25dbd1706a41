function servicedir() {
  echo "service"
}

function _svc::dir() {
  echo "${OTTO_REPO_ROOT}/$(servicedir)/${svc}"
}

function _svc::dockerfile() {
  echo "$(_svc::dir)/Dockerfile"
}

function _svc::helm::dir() {
  echo "$(_svc::dir)/helm"
}

function _svc::helm::value::read() {
  local svc="${1:?}"
  local key="${2:?}"
  arg::_required::type "service_helm" "svc"
  log::debug "Getting service value | svc='${svc}' key='${key}'"

  # Get the value from the helm chart
  local helm_chart="$(_svc::helm::dir)/Chart.yaml"
  local value
  if ! value="$(yq "${key}" "${helm_chart}")"; then
    log::err "Failed to get key from helm chart | key='${key}' helm_chart='${helm_chart}'"
    return 1
  fi

  echo "${value}"
}

function _svc::helm::value::write() {
  local svc="${1:?}"
  local key="${2:?}"
  local value="${3:?}"
  arg::_required::type "service_helm" "svc"
  log::debug "Writing service value | svc='${svc}' key='${key}' value='${value}'"

  local helm_chart="$(_svc::helm::dir)/Chart.yaml"
  local yq_expr="$(cat - << EOF
.${key} = "${value}"
EOF
)"
  if ! yq -i "${yq_expr}" "${helm_chart}"; then
    log::err "Failed to write key to helm chart | key='${key}' helm_chart='${helm_chart}'"
    return 1
  fi
}

function _svc::version::write() {
  local svc="${1:?}"
  local vers_helm="${2:?}"
  arg::_required::type "service" "svc"
  arg::_required::type "version_helm" "vers_helm"

  # TODO: I thought... But perhaps I am wrong:
  #
  # We cannot put the hash into the '.version' - or else 'helm lint' fails.
  # 'helm lint' gates 'helm package'.
  #
  # However, we can run 'helm package' with a '--version' argument containing a
  # hash, and that succeeds
  local version
  IFS='-' read -r version _ <<< "${vers_helm}"

  _svc::helm::value::write "${svc}" 'version' "${version}"
  _svc::helm::value::write "${svc}" 'appVersion' "${vers_helm}"
}

function validate::service() {
  local svc="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_SERVICE:-}" && VALID_SERVICE+=( "${svc}" )
  arr::contains "${svc}" "${VALID_SERVICE[@]}" && return 0

  # Make sure there is a dir
  local svc_dir
  svc_dir="$(_svc::dir)"
  if ! [ -d "${svc_dir}" ]; then
    log::err "Service dir does not exist | svc_dir='${svc_dir}'"
    return 1
  fi

  # And that it's a helm service
  if ! validate::service_helm "${svc}"; then
    log::err "Service is not a helm service | svc='${svc}'"
    return 1
  fi
}

# There can be only one (implemented with a pidfile)
function service::ensure_down() {
  local svc="${1:?}"
  arg::_required::type "service" "svc"
  log::info "Ensuring service is down | svc='${svc}'"

  # Exit early if we're done
  local pf="${OTTO_REPO_ROOT}/local/pids/${svc}.pid"
  test -f "${pf}" || return 0

  # Recover the pid, clean up the file
  local pid
  pid=$(cat "${pf}")
  rm -f "${pf}"

  # Kill the running service
  kill -9 "${pid}" &>/dev/null || true
}

function service::version::get() {
  local maybe_vers_helm="$(_svc::helm::value::read "${1:?}" '.appVersion')"

  if [ "${OTTO_PUBLISH_NONPROD:-}" == "true" ]; then
    maybe_vers_helm+="-nonprod"
    log::warn "Getting the name for a nonprod image | svc='${svc}' maybe_vers_helm='${maybe_vers_helm}'"
  fi

  local vers_helm
  if (validate::version_helm "${maybe_vers_helm}" &>/dev/null); then
    echo "${maybe_vers_helm}"
    return
  fi

  # Attempt to massage, if validation failure is understandable
  if grep -q '^\d\+\.\d\+\.\d\+$' <<< "${maybe_vers_helm}"; then
    _log::debug::service_hash::missing
    if [ -n "${OTTO_SERVICE_HASH_DEFAULT:-}" ]; then
      _log::warn::service_hash::using_default
      echo "${maybe_vers_helm}-${OTTO_SERVICE_HASH_DEFAULT}"
      return
    fi
  fi

  log::err "Service version is not valid | maybe_vers_helm='${maybe_vers_helm}'"
  validate::version_helm "${maybe_vers_helm}"
}

function service::helm::tgz() {
  local svc="${1:?}"
  local vers_helm="${2:?}"
  arg::_required::type "service_helm" "svc"
  arg::_required::type "version_helm" "vers_helm"
  log::debug "Getting helm dest | svc='${svc}' vers_helm='${vers_helm}'"

  local dir
  dir="$(_svc::helm::dir)"
  name=$(_svc::helm::value::read "${svc}" '.name')

  echo "${dir}/${name}-${vers_helm}.tgz"
}

function service::image_name() {
  local svc="${1:?}"
  shift

  # Set defaults
  local acr="illum.azurecr.io"
  local maybe_proj
  maybe_proj="$(_svc::dispatch "acr_image_proj" "${svc}")"
  local vers_helm
  vers_helm=$(service::version::get "${svc}")

  # Allow CLI override
  while (( $# > 0 )); do
    case "${1:?}" in
      --acr) acr="${2:?}"; shift 2;;
      --proj) maybe_proj="${2:?}"; shift 2;;
      --vers_helm) vers_helm="${2:?}"; shift 2;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Compute the image name
  local image_name="${acr}/${maybe_proj}/${svc}:${vers_helm}"
  image_name="$(tr -s '/' <<< "${image_name}")"
  echo "${image_name}"
}

# Bump the service version
#
# Output:
#   the new version
function service::version::bump() {
  local svc="${1:?}"
  local bump_type="${2:?}"
  arg::_required::type "service" "svc"
  arg::_required::enum "version_bump" "bump_type"

  local vers_helm_old vers_helm_new
  if ! vers_helm_old="$(OTTO_SERVICE_HASH_DEFAULT='fffffff' service::version::get "${svc}")"; then
    log::err "Failed to get version | svc='${svc}'"
    exit 1
  fi
  if ! vers_helm_new="$(version_helm::bump "${vers_helm_old}" "${bump_type}")"; then
    log::err "Failed to bump version | svc='${svc}' bump_type='${bump_type}'"
    exit 1
  fi

  _svc::version::write "${svc}" "${vers_helm_new}"
  echo "${vers_helm_new}"
}

function service::reflection::list() {
  ls -1 "${OTTO_REPO_ROOT}/$(servicedir)"
}

# For each component that has changed since last commit to 'main' (excluding
# THIS commit), print its name on a newline
#
# Example:
#   service::reflection::list::changed
#
# Output:
#   mmdb
function service::reflection::list::changed() {
  local trunk="main"
  local different_files
  if ! different_files="$(git::changed_files "${trunk}")"; then
    log::err "Failed to find which files differ since the trunk | trunk='${trunk}'"
    return 1
  fi
  log::DEBUG "Different files:"
  log::DEBUG "${different_files}"

  # Check if these changed files are in any of the relevant components
  while IFS= read -r svc; do
    if ! grep -q "^$(servicedir)/${svc}" <<< "${different_files}"; then
      log::debug "Component remains unchanged since this branch diverged from the trunk | trunk='${trunk}' svc='${svc}'"
      continue
    fi
    log::debug "We have noticed changes in the svc since this branch diverged from the trunk | trunk='${trunk}' svc='${svc}'"
    echo "${svc}"
  done < <(service::reflection::list)
}

function _log::debug::service_hash::missing() {
    log::DEBUG "Service version is not valid because it is missing a hash. If we
were given a default hash, we could fall back to that

  svc='${svc}'
  maybe_vers_helm='${maybe_vers_helm}'
  OTTO_SERVICE_HASH_DEFAULT='${OTTO_SERVICE_HASH_DEFAULT:-}'
"
}

function _log::warn::service_hash::using_default() {
  log::WARN "Service version is not valid because it is missing a hash. But we
were given a default hash

  svc='${svc}'
  maybe_vers_helm='${maybe_vers_helm}'
  OTTO_SERVICE_HASH_DEFAULT='${OTTO_SERVICE_HASH_DEFAULT}'
"
}
