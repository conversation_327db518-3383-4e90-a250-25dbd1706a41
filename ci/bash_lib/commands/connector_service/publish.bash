function command::publish() {
  publish::docker "$@"
  publish::helm "$@"
}

function publish::helm() {
  # Parse args
  local acr svc
  while (( $# > 0 )); do
    case "${1}" in
      --platform) shift 2 ;; # silently ignore :)
      --acr) acr="${2:?}"; shift 2 ;;
      --svc) svc="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Validate args
  arg::_required::type "service_helm" "svc"
  arg::_required "acr"
  arg::_env "ACR_TOKEN_USERNAME" "ACR_TOKEN_PASSWORD"

  # Compute values
  local vers_helm
  if ! vers_helm="$(service::version::get "${svc}")"; then
    log::err "Failed to get service version | svc='${svc}'"
    exit 1
  fi

  local helm_tgz
  if ! helm_tgz="${helm_tgz:-$(service::helm::tgz "${svc}" "${vers_helm}")}"; then
    log::err "Failed to get helm dest"
    exit 1
  fi

  ### Do work

  # Log in to the ACR
  local helm_login_args=(
    "${acr:?}"
    --username "${ACR_TOKEN_USERNAME:?}"
    --password "${ACR_TOKEN_PASSWORD:?}"
  )
  run_cmd_cap helm registry login "${helm_login_args[@]}"

  # Package up the helm chart (build)
  local -r helm_dest="$(dirname "${helm_tgz}")"
  local helm_package_args=(
    "$(_svc::helm::dir)"
    --version "${vers_helm}"
    --app-version "${vers_helm}"
    --destination "${helm_dest}"
  )
  run_cmd_cap helm package "${helm_package_args[@]}"
  if ! [ -f "${helm_tgz}" ]; then
    log::err "Failed to package helm chart | helm_tgz='${helm_tgz}'"
    exit 1
  fi

  # Find where to push the helm chart to
  local acr_helm_proj
  acr_helm_proj="$(_svc::dispatch "acr_helm_proj" "${svc}")"
  local remote_path="${acr}/helm/${acr_helm_proj}/"
  remote_path="$(tr -s '/' <<< "${remote_path}")"

  # Push the helm chart
  local helm_push_args=(
    "${helm_tgz}"
    "oci://${remote_path}"
  )
  run_cmd helm push "${helm_push_args[@]}"
}

function publish::docker() {
  local svc acr platform
  while (( $# > 0 )); do
    case "${1:?}" in
      --svc) svc="${2:?}"; shift 2 ;;
      --acr) acr="${2:?}"; shift 2 ;;
      --platform) platform="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  arg::_required::type "service" "svc"
  arg::_required "acr"
  # arg::_required "platform"
  arg::_env "ACR_TOKEN_USERNAME" "ACR_TOKEN_PASSWORD"

  # Log in to the ACR
  local docker_login_args=(
    --username "${ACR_TOKEN_USERNAME:?}"
    --password "${ACR_TOKEN_PASSWORD:?}"
    "${acr:?}"
  )
  run_cmd docker login "${docker_login_args[@]}"

  # Push the image
  local image_name
  image_name="$(service::image_name "${svc}" --acr "${acr:?}")"
  run_cmd docker push "${image_name}"
}

function _log::_err_helm_lint() {
  log::ERR "
Helm lint failed

    helm_dir='${helm_dir}'

Helm lint output:

$(helm lint "${helm_dir}" | str::indent 4)
"
}
