function command::manual_deploy_commit::help() {
cat << EOF
Create a deploy_commit (manually).

ARGS:
  --all
    Make a deploy commit that version bumps every component

  --svc <svc>
    Bump the version of the given service(s). Can be used multiple times

  --bump_type patch|minor|major
    The type of version bump to make. Defaults to patch

EOF
}

function command::manual_deploy_commit() {
  # Parse args
  local services=() bump_type="patch"
  while (( $# > 0 )); do
    case "$1" in
      --all) mapfile -t services < <(service::reflection::list); shift ;;
      --svc) services+=("${2:?}"); shift 2 ;;
      --bump_type) bump_type="${2:?}"; shift 2 ;;
      --help) command::manual_deploy_commit::help; return 0 ;;
      *) log::err "Unknown argument in ${FUNCNAME[0]}: '$1'"; exit 1 ;;
    esac
  done

  # Massage args
  mapfile -t services < <(arr::dedup "${services[@]}")

  # Validate args
  arg::_required "services" # 0-length arr is unacceptable
  for svc in "${services[@]}"; do
    arg::_required::type "service" "svc"
  done
  arg::_required::enum "version_bump" "bump_type"

  # Ensure good repo state
  git::is_clean || _fatal::git_unclean

  # Do work
  # Make a tmp file - this will be the commit message
  local commit_msg_file="$(otto::mktemp)"

  # Bump the version for each svc
  local vers_new_helm
  for svc in "${services[@]}"; do
    vers_new_helm="$(service::version::bump "${svc}" "${bump_type}")"
    otto::git add .

    # Update our commit message
    local yq_expr="$(cat << EOF
.updates += [{"service": "${svc}", "value": "${vers_new_helm}"}]
EOF
)"
    yq -i "${yq_expr}" "${commit_msg_file}"
  done

  # The commit message must adhere to a specific grammar so we can parse it in
  # the 'is_deploy_commit' command
  TRIP_BACKTICKS='```'
  local -r msg="$(cat << EOF
Deploy commit (${bump_type})

${TRIP_BACKTICKS}
${DEPLOY_COMMIT_START}
$(cat "${commit_msg_file}")
${DEPLOY_COMMIT_END}
${TRIP_BACKTICKS}
EOF
)"

  verify_deploy_can_succeed || _fatal::deploy_would_fail

  # Make the commit
  if ! git commit --message "${msg}"; then
    log::err "Failed to commit"
    return 1
  fi
}

# Rely on OTTO_ARGOCDCONFIGREPO_ROOT. Try to make the deploy changes on local
# working tree, to validate the config.yaml.
#
# Skip this check by setting OTTO_NOVALIDATE_ARGOCDCONFIGREPO
function verify_deploy_can_succeed() (
  otto::is_true "${OTTO_NOVALIDATE_ARGOCDCONFIGREPO:-}" && return 0
  [ -d "${OTTO_ARGOCDCONFIGREPO_ROOT:-}" ] || _fatal::missing_argocdconfigrepo_root

  # Set up call to deploy::_body
  cd "${OTTO_ARGOCDCONFIGREPO_ROOT}"
  local deployable_services deploy_commit_yaml
  deployable_services=("${services[@]}")
  deploy_commit_yaml="$(cat "${commit_msg_file}")"

  # Determine if the OTTO_ARGOCDCONFIGREPO_ROOT's git tree is clean. If it is
  # dirty, then we will need to stash & unstash to avoid clobbering the user's
  # changes
  function otto::git() { git -C "${OTTO_ARGOCDCONFIGREPO_ROOT}" "$@"; }
  local needs_stash=false
  git::is_clean || needs_stash=true

  # stash if necessary
  if otto::is_true "${needs_stash}"; then otto::git stash; fi

  # Attempt the changes to the config repo. Log the diff. Undo the changes
  if ! deploy::_body; then
    log::err "Deploying would fail! Fix the config repo and try again. OTTO_ARGOCDCONFIGREPO_ROOT='${OTTO_ARGOCDCONFIGREPO_ROOT}'"
    return 1
  fi
  log::info "Deploying would succeed. Here's what the diff would look like | OTTO_ARGOCDCONFIGREPO_ROOT='${OTTO_ARGOCDCONFIGREPO_ROOT}'"
  run_cmd otto::git diff
  run_cmd otto::git restore .

  # unstash if necessary
  if otto::is_true "${needs_stash}"; then otto::git stash pop; fi
)

function _fatal::git_unclean() {
  log::ERR "
Have changed or staged files

Git status:
$(otto::git status)
"
  exit 1
}

function _fatal::missing_argocdconfigrepo_root() {
  log::preamble() { :; }
  log::WARN "
If you wish to skip this check, you can avoid it by setting the following env
variable:

OTTO_NOVALIDATE_ARGOCDCONFIGREPO=true
"

  log::ERR "
Environment variable OTTO_ARGOCDCONFIGREPO_ROOT is not set to a dir, so we
cannot validate that the deploy attempted by this deploy commit will go through
properly. Please set this environment variable to the path where we can find the
argocd config repo.

    Currently: OTTO_ARGOCDCONFIGREPO_ROOT="${OTTO_ARGOCDCONFIGREPO_ROOT:-}"

    Example: OTTO_ARGOCDCONFIGREPO_ROOT=/Users/<USER>/code/argocd-apps-cloudsecure-dev
"
  exit 1
}

function _fatal::deploy_would_fail() {
  log::preamble() { :; }
  log::ERR "
The deploy commit would fail. Please fix the config repo and try again.

    OTTO_ARGOCDCONFIGREPO_ROOT="${OTTO_ARGOCDCONFIGREPO_ROOT:-}"
"
  exit 1
}
