function command::manual_deploy_commit::help() {
cat << EOF
Create a deploy_commit (manually).

ARGS:
  --all
    Make a deploy commit that version bumps every component

  --svc <svc>
    Bump the version of the given service(s). Can be used multiple times

  --bump_type patch|minor|major
    The type of version bump to make. Defaults to patch

EOF
}

function command::manual_deploy_commit() {
  # Parse args
  local services=() bump_type="patch"
  while (( $# > 0 )); do
    case "$1" in
      --all) mapfile -t services < <(service::reflection::list); shift ;;
      --svc) services+=("${2:?}"); shift 2 ;;
      --bump_type) bump_type="${2:?}"; shift 2 ;;
      --help) command::manual_deploy_commit::help; return 0 ;;
      *) log::err "Unknown argument in ${FUNCNAME[0]}: '$1'"; exit 1 ;;
    esac
  done

  # Massage args
  mapfile -t services < <(arr::dedup "${services[@]}")

  # Validate args
  arg::_required "services" # 0-length arr is unacceptable
  for svc in "${services[@]}"; do
    arg::_required::type "service" "svc"
  done
  arg::_required::enum "version_bump" "bump_type"

  # Ensure good repo state
  git::is_clean || _fatal::git_unclean

  # Do work
  # Make a tmp file - this will be the commit message
  local commit_msg_file="$(otto::mktemp)"

  # Bump the version for each svc
  local vers_new_helm
  for svc in "${services[@]}"; do
    vers_new_helm="$(service::version::bump "${svc}" "${bump_type}")"
    otto::git add .

    # Update our commit message
    local yq_expr="$(cat << EOF
.updates += [{"service": "${svc}", "value": "${vers_new_helm}"}]
EOF
)"
    yq -i "${yq_expr}" "${commit_msg_file}"
  done

  # The commit message must adhere to a specific grammar so we can parse it in
  # the 'is_deploy_commit' command
  TRIP_BACKTICKS='```'
  local -r msg="$(cat << EOF
Deploy commit (${bump_type})

${TRIP_BACKTICKS}
${DEPLOY_COMMIT_START}
$(cat "${commit_msg_file}")
${DEPLOY_COMMIT_END}
${TRIP_BACKTICKS}
EOF
)"

  # Make the commit
  if ! git commit -m "${msg}"; then
    log::err "Failed to commit"
    return 1
  fi
}

function _fatal::git_unclean() {
  log::ERR "
Have changed or staged files

Git status:
$(otto::git status)
"
  exit 1
}
