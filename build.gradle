import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
	id 'org.springframework.boot' version '3.2.5' apply false
	id 'io.spring.dependency-management' version '1.1.4' apply false
	id 'com.google.cloud.tools.jib' version '3.4.2' apply false
	id "com.github.johnrengelman.shadow" version "8.1.1" apply false
	id 'jacoco' // Apply the JaCoCo plugin
}

subprojects {
	apply plugin: 'java'
	apply plugin: "io.spring.dependency-management"
	apply plugin: 'jacoco' // Apply JaCoCo plugin to all subprojects

	ext.jacocoEnabled = System.properties.getProperty("jacocoEnabled") ?: "false"
	if (jacocoEnabled.toBoolean()) {
		apply from: rootProject.file("jacoco.gradle")
	}

	sourceCompatibility = JavaVersion.VERSION_17

	dependencyManagement {
		imports {
			mavenBom SpringBootPlugin.BOM_COORDINATES
		}
	}

	repositories {
		// Define repositories for dependencies
		mavenCentral()
	}

	dependencies {
		// Define common dependencies for all subprojects
		// ECS logging
		implementation platform('co.elastic.logging:logback-ecs-encoder:1.6.0')

		// azure
		implementation platform('com.azure:azure-sdk-bom:1.2.23')

		// azure spring
		implementation platform('com.azure.spring:spring-cloud-azure-dependencies:5.19.0')

		// aws bom
		implementation platform('software.amazon.awssdk:bom:2.20.123')

		// reactor-bom
		implementation platform('io.projectreactor:reactor-bom:2023.0.6')

		// slf4j
		implementation platform('org.slf4j:slf4j-bom:2.0.17')

		// otel
		implementation(platform("io.opentelemetry:opentelemetry-bom:1.39.0"))

		// Reactive Relational Database Connectivity
		implementation platform('io.r2dbc:r2dbc-bom:Arabba-SR8')

		// vault
		implementation(platform("org.springframework.cloud:spring-cloud-vault-dependencies:4.1.3"))

		// SpringDoc / Swagger
		implementation platform('org.springdoc:springdoc-openapi:2.6.0')

		// lombok
		compileOnly 'org.projectlombok:lombok'
		annotationProcessor 'org.projectlombok:lombok'
		testCompileOnly 'org.projectlombok:lombok'
		testAnnotationProcessor 'org.projectlombok:lombok'

		// test
		testImplementation platform('org.junit:junit-bom:5.10.0')
		testImplementation 'org.junit.jupiter:junit-jupiter'
		testImplementation 'org.testcontainers:testcontainers-bom:1.20.4'

		testImplementation 'org.springframework.boot:spring-boot-starter-test'
		testImplementation 'org.mockito:mockito-core:4.0.0'
		testImplementation 'org.mockito:mockito-inline:5.2.0'
		testImplementation 'io.projectreactor:reactor-test:3.4.10'
	}

	test {
		useJUnitPlatform()
		finalizedBy jacocoTestReport // Generate JaCoCo report after tests
	}

	jacoco {
		toolVersion = "0.8.11" // Use the latest JaCoCo version
	}

	jacocoTestReport {
		dependsOn test // Ensure tests are run before generating the report
		reports {
			xml.required = true // Generate XML report for CI/CD integration
			csv.required = true // Disable CSV report
			html.required = true // Generate HTML report for local viewing
		}
	}
}