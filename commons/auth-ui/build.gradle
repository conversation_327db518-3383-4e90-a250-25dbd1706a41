plugins {
    id 'java-library'
    id 'com.google.protobuf' version '0.9.4'
}


dependencies {
    // logging
    implementation 'org.slf4j:slf4j-api'

    // implementation
    implementation project(":commons:data-commons")
    implementation project(":commons:utility-commons")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'io.projectreactor:reactor-core'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'

    // grpc
    implementation 'io.grpc:grpc-netty-shaded:1.70.0'
    implementation 'io.grpc:grpc-protobuf:1.70.0'
    implementation 'io.grpc:grpc-stub:1.70.0'
    implementation 'net.devh:grpc-spring-boot-starter:3.1.0.RELEASE'
    implementation 'com.salesforce.servicelibs:reactor-grpc-stub:1.2.4'
    compileOnly 'org.apache.tomcat:annotations-api:6.0.53'
}

sourceSets {
    main {
        java {
            srcDir 'build/generated/source/proto/main/grpc'
            srcDir 'build/generated/source/proto/main/reactor'
            srcDir 'build/generated/source/proto/main/java'
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.25.5"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:1.70.0"
        }
        reactor {
            artifact = "com.salesforce.servicelibs:reactor-grpc:1.2.4"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
            reactor {}
        }
    }
}

repositories {
    mavenCentral()
}