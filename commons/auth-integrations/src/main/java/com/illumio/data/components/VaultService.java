package com.illumio.data.components;

import com.illumio.data.exception.RetryReactiveOnError;
import com.illumio.data.logging.LogReactiveExecutionTime;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.vault.core.ReactiveVaultTemplate;
import org.springframework.vault.core.VaultKeyValueOperationsSupport;
import org.springframework.vault.support.VaultResponseSupport;
import reactor.core.publisher.Mono;
import java.util.HashMap;
import java.util.Map;
import static com.illumio.data.util.AuthCommonConstants.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class VaultService {

    private final ReactiveVaultTemplate reactiveVaultTemplate;

    @SneakyThrows
    @LogReactiveExecutionTime
    public Mono<Map<String, Object>> getSecret(final String secretPath) {
        log.debug("Retrieving Vault secret from Vault at vaultPath={}", secretPath);
        if (reactiveVaultTemplate == null) {
            throw new IllegalStateException("VaultTemplate is not configured. Please ensure a valid vault.uri and vault.token are configured.");
        }
        return reactiveVaultTemplate.read(secretPath)
                .map(VaultResponseSupport::getData)
                .switchIfEmpty(Mono.error(new RuntimeException(String.format("Failed to get secret at secretPath=%s from Vault. Please double-check authentication and path.", secretPath))));
    }

    @LogReactiveExecutionTime
    public Mono<String> writeSecret(final String vaultPath, final Map<String, Object> value) {

        Map<String, Object> kvv2Payload = new HashMap<>();
        kvv2Payload.put(DATA, value);
        return reactiveVaultTemplate.write(vaultPath, kvv2Payload)
                .then(Mono.just(vaultPath))
                .doOnSuccess(success -> log.debug("Successfully wrote Vault secret to vaultPath={}", vaultPath))
                .doOnError(error -> log.error("Error occurred while writing secret to vaultPath={}", vaultPath, error));
    }

    @LogReactiveExecutionTime
    public Mono<Void> deleteSecret(final String vaultPath) {
        final String secretSubPath = vaultPath.replaceFirst("^" + VAULT_PATH_PREFIX, "");
        return reactiveVaultTemplate.opsForKeyValue(SECRET, VaultKeyValueOperationsSupport.KeyValueBackend.KV_2)
                .delete(secretSubPath)
                .doOnSuccess(success -> log.debug("Successfully deleted Vault secret at vaultPath={}", vaultPath))
                .doOnError(error -> log.error("Error occurred while deleting secret at vaultPath={}", vaultPath, error));
    }

}
