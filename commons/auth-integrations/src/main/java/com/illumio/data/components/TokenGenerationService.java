package com.illumio.data.components;

import com.illumio.data.components.armis.ArmisTokenGenerator;
import com.illumio.data.components.wiz.WizTokenGenerator;
import com.illumio.data.model.Token;
import com.illumio.data.model.TokenRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class TokenGenerationService {

    private final WizTokenGenerator wizTokenGenerator;
    private final ArmisTokenGenerator armisTokenGenerator;

    public Mono<Token> getToken(final TokenRequest request) {
        return getTokenGenerator(request).generateToken(request);
    }

    private TokenGenerator getTokenGenerator(final TokenRequest request) {
        return switch (request.getIntegration()) {
            case WIZ -> wizTokenGenerator;
            case ARMIS -> armisTokenGenerator;
        };
    }

}