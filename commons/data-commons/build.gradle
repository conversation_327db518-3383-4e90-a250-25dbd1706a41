plugins {
    id 'java-library'
}

dependencies {
    // azure
    implementation platform("com.azure:azure-sdk-bom")
    implementation 'com.azure:azure-cosmos'
    implementation 'com.azure.spring:spring-cloud-azure-starter-data-cosmos:5.19.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'io.r2dbc:r2dbc-postgresql'
    implementation 'org.postgresql:postgresql'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.data:spring-data-relational'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation project(":commons:utility-commons")



    // jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}