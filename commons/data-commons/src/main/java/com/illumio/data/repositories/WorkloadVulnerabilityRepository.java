package com.illumio.data.repositories;

import com.illumio.data.entities.VulnerabilityWorkloadEntity;
import com.illumio.data.metrics.QueryExecutionTimerMetric;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@QueryExecutionTimerMetric
@Repository
public interface WorkloadVulnerabilityRepository extends ReactiveCrudRepository<VulnerabilityWorkloadEntity, String> {
    @Query("""
        INSERT INTO workload_vulnerability (workload_id, vulnerability_id, tenant_id, integration,
                                          status, first_detected_at, last_detected_at,
                                          impact_score, exploitability_score, severity, vulnerable_component_name, subscription_id)
        VALUES (:#{#entity.workloadId}, :#{#entity.vulnerabilityId}, :#{#entity.tenantId},
                :#{#entity.integration}, :#{#entity.status}, :#{#entity.firstDetectedAt},
                :#{#entity.lastDetectedAt}, :#{#entity.impactScore}, 
                :#{#entity.exploitabilityScore}, :#{#entity.severity}, :#{#entity.vulnerableComponentName},
                :#{#entity.subscriptionId})
        ON CONFLICT(workload_id, vulnerability_id) DO UPDATE SET
            tenant_id = :#{#entity.tenantId},
            integration = :#{#entity.integration},
            status = :#{#entity.status},
            first_detected_at = :#{#entity.firstDetectedAt},
            last_detected_at = :#{#entity.lastDetectedAt},
            impact_score = :#{#entity.impactScore},
            exploitability_score = :#{#entity.exploitabilityScore},
            severity = :#{#entity.severity},
            vulnerable_component_name = :#{#entity.vulnerableComponentName},
            subscription_id = :#{#entity.subscriptionId}
        RETURNING *
    """)
    Mono<VulnerabilityWorkloadEntity> upsertWorkloadVulnerability(@Param("entity") VulnerabilityWorkloadEntity entity);
}