package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatus {

    private String taskId;
    private Integration integration;
    private String tenantId;
    private TaskStatusType statusType;
    private TaskType taskType;
    private String statusUpdateTime;
    private String statusMessage;

}


