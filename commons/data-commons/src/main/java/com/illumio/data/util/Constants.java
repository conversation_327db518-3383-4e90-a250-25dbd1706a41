package com.illumio.data.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {

    public static final String DATABASE_ONBOARDING = "onboarding";
    public static final String DATABASE_TASK_STATUS = "task_status";
    public static final String DATABASE_RETRY_MESSAGES = "retry_messages";
    public static final String WORKLOAD_VULNERABILITY = "workload_vulnerability";
    public static final String VULNERABILITY_METADATA = "vulnerability";
    public static final String TASK_SCHEMA = "task_status_schema.sql";
    public static final String VULNERABILITY_SCHEMA = "vulnerability_schema.sql";
    public static final String ISSUES_SCHEMA = "issues_schema.sql";
    public static final String FINDING_TYPE = "findings_type";
    public static final String ISSUES = "issues";

}
