package com.illumio.data.util;

import com.illumio.data.model.Integration;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;


import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HexFormat;

@Slf4j
@UtilityClass
public class Util {

    @SneakyThrows
    public String generateNewTaskId(final String tenantId, final Integration integration, final String scheduledTime) {
        String combinedKey = tenantId + "-" + integration + "-" + scheduledTime;
        return generateSha256Hash(combinedKey);
    }

    @SneakyThrows
    public String generateSha256Hash(final String input) {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
        return HexFormat.of().formatHex(hashBytes);
    }

}