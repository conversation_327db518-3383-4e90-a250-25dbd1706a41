package com.illumio.data.entities;

import com.azure.spring.data.cosmos.core.mapping.Container;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.illumio.data.model.Integration;
import com.illumio.data.model.TenantConfigurations;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Map;

import static com.illumio.data.util.Constants.DATABASE_ONBOARDING;

@Data
@Container(containerName = DATABASE_ONBOARDING)
public class TenantOnboardingEntity {

    @Id
    private String id;
    private String tenantId;
    private Integration integration;
    private String credentialsVaultPath;
    private TenantConfigurations configurations;
    private Map<String, Object> tenantMetadata;

    @JsonCreator
    public TenantOnboardingEntity(@JsonProperty("tenantId") String tenantId,
            @JsonProperty("integration") Integration integration,
            @JsonProperty("credentialsVaultPath") String credentialsVaultPath,
            @JsonProperty("configurations") TenantConfigurations configurations,
            @JsonProperty("tenantMetadata") Map<String, Object> tenantMetadata){
        this.id = tenantId + integration.name();
        this.tenantId = tenantId;
        this.integration = integration;
        this.credentialsVaultPath = credentialsVaultPath;
        this.configurations = configurations;
        this.tenantMetadata = tenantMetadata;
    }

}
