package com.illumio.data.model;

public enum TaskStatusType {

    SCHEDULED,
    STARTE<PERSON>,
    S<PERSON><PERSON><PERSON>,
    RETR<PERSON>,
    <PERSON><PERSON>,

    // for usage associated with async tasks only
    SCHEDULED_ASYNC, // state in which async task is first submitted to data sync to trigger external async task
    STARTED_ASYNC, // state in which data sync has begun submitting external async task to integration
    AWAITING_ASYNC, // state in which data sync has already submitted external async task to integration and waiting for completion
    CHECKING_STATUS_ASYNC, // state in which async task is sent back to data sync to check on status with integration
    READY_ASYNC; // state in which data sync has determined the integration has completed the external async task

    public static TaskStatusType getInitialTaskStatus(final Integration integration) {
        return switch (integration) {
            case WIZ -> SCHEDULED_ASYNC;
            default -> SCHEDULED;
        };
    }

}