package com.illumio.data.repositories;


import com.illumio.data.entities.TaskStatusEntity;
import com.illumio.data.metrics.QueryExecutionTimerMetric;
import com.illumio.data.model.Integration;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;

@QueryExecutionTimerMetric
@Repository
public interface TaskStatusRepository extends ReactiveCrudRepository<TaskStatusEntity, String> {

    @Query("SELECT * FROM task_status WHERE tenant_id = :tenantId AND integration = :integration ORDER BY COALESCE(schedule_time, start_time) DESC LIMIT 1")
    Mono<TaskStatusEntity> findLatestByTenantIdAndIntegration(String tenantId, Integration integration);

    @Query("SELECT * FROM task_status WHERE tenant_id = :tenantId AND integration = :integration AND status = 'SUCCESS' ORDER BY end_time DESC LIMIT 1")
    Mono<TaskStatusEntity> findLastSuccessfulTask(String tenantId, Integration integration);

    @Query("SELECT * FROM task_status WHERE tenant_id = :tenantId AND integration = :integration AND status IN ('SUCCESS', 'FAIL') ORDER BY end_time DESC LIMIT 1")
    Mono<TaskStatusEntity> findLastCompletedTask(String tenantId, Integration integration);

    @Query("""
        INSERT INTO task_status (
            task_id, status, tenant_id, integration, schedule_time, start_time, end_time, last_update_time,
            task_type, status_message
        )
        VALUES (
            :#{#entity.taskId}, :#{#entity.status}, :#{#entity.tenantId}, :#{#entity.integration},
            :#{#entity.scheduleTime}, :#{#entity.startTime}, :#{#entity.endTime}, :#{#entity.lastUpdateTime},
            :#{#entity.taskType}, :#{#entity.statusMessage}
        )
        ON CONFLICT (task_id) DO UPDATE SET
            status = EXCLUDED.status,
            tenant_id = EXCLUDED.tenant_id,
            integration = EXCLUDED.integration,
            schedule_time = CASE WHEN EXCLUDED.schedule_time IS NOT NULL THEN EXCLUDED.schedule_time ELSE task_status.schedule_time END,
            start_time = CASE WHEN EXCLUDED.start_time IS NOT NULL THEN EXCLUDED.start_time ELSE task_status.start_time END,
            end_time = CASE WHEN EXCLUDED.end_time IS NOT NULL THEN EXCLUDED.end_time ELSE task_status.end_time END,
            last_update_time = EXCLUDED.last_update_time,
            task_type = EXCLUDED.task_type,
            status_message = EXCLUDED.status_message
        RETURNING *
    """)
    Mono<TaskStatusEntity> upsertTaskStatusEntity(@Param("entity") TaskStatusEntity entity);

    @Query("""
    SELECT * FROM task_status
    WHERE status = :initialStatus
      AND end_time IS NULL
      AND task_type = :taskType
      AND NOW() - start_time > :longRunningWindow::interval
""")
    Flux<TaskStatusEntity> findLongRunningTasks(@Param("initialStatus") String initialStatus, @Param("taskType") String taskType, @Param("longRunningWindow") Duration longRunningWindow);

}




