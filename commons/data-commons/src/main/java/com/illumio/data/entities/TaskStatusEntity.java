package com.illumio.data.entities;

import com.illumio.data.model.Integration;
import com.illumio.data.model.TaskStatusType;
import com.illumio.data.model.TaskType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.OffsetDateTime;

import static com.illumio.data.util.Constants.DATABASE_TASK_STATUS;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(DATABASE_TASK_STATUS)
public class TaskStatusEntity {

    @Id
    private String taskId;
    private String tenantId;
    private Integration integration;
    private OffsetDateTime scheduleTime;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;
    private OffsetDateTime lastUpdateTime;
    private TaskStatusType status;
    private String statusMessage;
    private TaskType taskType;

}


