package com.illumio.data.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import static com.illumio.data.util.Constants.VULNERABILITY_METADATA;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(VULNERABILITY_METADATA)
public class VulnerabilityMetaDataEntity {
    @Id
    private String cveId;
    private String cveDescription;
    private String cvssSeverity;
    private Double score;
    private Boolean hasExploit;
    private Boolean hasCisaKevExploit;
}