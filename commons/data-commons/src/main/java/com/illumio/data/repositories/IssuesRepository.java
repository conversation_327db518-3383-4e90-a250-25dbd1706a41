package com.illumio.data.repositories;

import com.illumio.data.entities.IssueEntity;
import com.illumio.data.metrics.QueryExecutionTimerMetric;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@QueryExecutionTimerMetric
@Repository
public interface IssuesRepository extends ReactiveCrudRepository<IssueEntity, String> {
    @Query("""
        INSERT INTO issues (id, source_rule_names, subscription_id, severity, status, risks, tenant_id, integration, workload_id)
        VALUES (:#{#entity.id}, :#{#entity.sourceRuleNames},
                :#{#entity.subscriptionId}, :#{#entity.severity}, :#{#entity.status}, :#{#entity.risks}, :#{#entity.tenantId}, :#{#entity.integration}, :#{#entity.workloadId}  )
        ON CONFLICT(id) DO UPDATE SET
            source_rule_names = :#{#entity.sourceRuleNames},
            subscription_id = :#{#entity.subscriptionId},
            severity = :#{#entity.severity},
            status = :#{#entity.status},
            risks = :#{#entity.risks},
            tenant_id = :#{#entity.tenantId},
            integration = :#{#entity.integration},
            workload_id = :#{#entity.workloadId}
        RETURNING *
    """)
    Mono<IssueEntity> upsertIssue(@Param("entity") IssueEntity entity);
}