package com.illumio.data.repositories;


import com.illumio.data.entities.RetryMessageEntity;

import com.illumio.data.metrics.QueryExecutionTimerMetric;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@QueryExecutionTimerMetric
@Repository
public interface RetryMessagesRepository extends ReactiveCrudRepository<RetryMessageEntity, String> {
    @Query("""
        INSERT INTO retry_messages (task_id, message, end_time)
        VALUES (:#{#entity.taskId}, :#{#entity.message}, :#{#entity.endTime})
        ON CONFLICT(tasK_id, end_time) DO UPDATE SET
            task_id = :#{#entity.taskId},
            message = :#{#entity.message},
            end_time = :#{#entity.endTime}
        RETURNING *
    """)
    Mono<RetryMessageEntity> upsertRetryMessages(@Param("entity") RetryMessageEntity entity);

}




