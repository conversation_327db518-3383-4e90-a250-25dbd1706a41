package com.illumio.data.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.OffsetDateTime;

import static com.illumio.data.util.Constants.DATABASE_RETRY_MESSAGES;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(DATABASE_RETRY_MESSAGES)
public class RetryMessageEntity {

    @Id
    private String taskId;
    private String message;
    private OffsetDateTime endTime;

}


