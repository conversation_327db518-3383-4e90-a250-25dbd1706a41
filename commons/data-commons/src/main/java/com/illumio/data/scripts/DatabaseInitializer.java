package com.illumio.data.scripts;

import io.r2dbc.spi.ConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Lazy
public class DatabaseInitializer {
    private final ConnectionFactory connectionFactory;

    public DatabaseInitializer(ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public void createSchema(String schemaFile){

        log.info("Initializing database after application startup");
        try {
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(new ClassPathResource(schemaFile));
            populator.setContinueOnError(false);
            populator.populate(connectionFactory)
                     .doOnSuccess(v -> log.info("Database initialized successfully"))
                     .doOnError(e -> {
                         log.error("Error initializing database. Application will now terminate", e);
                         System.exit(1);
                     })
                     .block(); // Block to ensure completion before continuing

        } catch (Exception e) {
            log.error("Error configuring database initializer. Application will now terminate", e);
            System.exit(1);
            throw e;
        }
    }

}

