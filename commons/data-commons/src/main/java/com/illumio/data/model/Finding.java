package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public abstract class Finding {

    private String status;
    private String tenantId;
    private Integration integration;
    private FindingResource resource;

    @JsonIgnore
    public abstract FindingType getFindingsType();
}