package com.illumio.data.model.wiz;

import com.illumio.data.model.TenantConfigurations;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WizTenantConfigurations extends TenantConfigurations {

    @NotNull
    private String apiUrl;

    @NotNull
    private String authUrl;
}
