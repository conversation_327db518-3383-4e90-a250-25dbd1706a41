package com.illumio.data.entities;

import com.illumio.data.model.Integration;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import static com.illumio.data.util.Constants.ISSUES;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(ISSUES)
public class IssueEntity {

    @Id
    private String id;
    private String sourceRuleNames;
    private String subscriptionId;
    private String severity;
    private String status;
    private String[] risks;
    private String tenantId;
    private Integration integration;
    private String workloadId;
}
