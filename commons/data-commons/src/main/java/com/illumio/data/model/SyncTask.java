package com.illumio.data.model;

import com.illumio.data.util.Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncTask {

    private String taskId;
    private String tenantId;
    private Integration integration;
    private String scheduleTime;
    private String credentialsVaultPath;
    private TenantConfigurations tenantConfigurations;
    private TaskType taskType;
    private TaskStatusType taskStatus;
    private String statusMessage;

    @Builder.Default
    private TaskBasicMetadata priorSuccessfulSync = TaskBasicMetadata.empty();

}