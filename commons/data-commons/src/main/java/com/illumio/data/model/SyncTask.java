package com.illumio.data.model;


import com.illumio.data.util.DateTimeUtil;
import com.illumio.data.util.Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncTask {

    private String taskId;
    private String tenantId;
    private Integration integration;
    private String scheduleTime;
    private String credentialsVaultPath;
    private TenantConfigurations tenantConfigurations;
    private TaskType taskType;

    @Builder.Default
    private TaskBasicMetadata priorSuccessfulSync = TaskBasicMetadata.empty();

    public static SyncTask.SyncTaskBuilder newTaskBuilder(final String tenantId,
                                                          final Integration integration) {
        final String scheduleTime = DateTimeUtil.generateCurrentTimestamp();
        final String taskId = Util.generateNewTaskId(
                tenantId,
                integration,
                scheduleTime);

        return SyncTask.builder()
                .taskId(taskId)
                .tenantId(tenantId)
                .integration(integration)
                .scheduleTime(scheduleTime);
    }


}