package com.illumio.data.entities;

import com.illumio.data.model.Integration;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import static com.illumio.data.util.Constants.WORKLOAD_VULNERABILITY;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(WORKLOAD_VULNERABILITY)
public class VulnerabilityWorkloadEntity {
    @Id
    private Long id;
    private String workloadId;
    private String vulnerabilityId;
    private String tenantId;
    private Integration integration;
    private String status;
    private String firstDetectedAt;
    private String lastDetectedAt;
    private Double impactScore;
    private Double exploitabilityScore;
    private String severity;
    private String vulnerableComponentName;
    private String subscriptionId;
}