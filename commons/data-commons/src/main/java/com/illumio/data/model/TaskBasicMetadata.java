package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskBasicMetadata {

    private String startTime;
    private String endTime;
    private String endMessage;

    public static TaskBasicMetadata empty(){
        return TaskBasicMetadata.builder().build();
    }
}