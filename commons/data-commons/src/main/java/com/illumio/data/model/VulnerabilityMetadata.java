package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class VulnerabilityMetadata {

    private String cveName;
    private String cveDescription;
    private String severity;
    private String cvssSeverity;
    private Double score;
    private Double impactScore;
    private Double exploitabilityScore;
    private Boolean hasCisaKevExploit;
    private Boolean hasExploit;
    private String vulnerableComponentName;
    private String firstDetectedAt;
    private String lastDetectedAt;
}