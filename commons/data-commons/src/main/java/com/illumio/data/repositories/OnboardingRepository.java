package com.illumio.data.repositories;

import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.metrics.QueryExecutionTimerMetric;
import com.illumio.data.model.Integration;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@QueryExecutionTimerMetric
@Repository
public interface OnboardingRepository extends ReactiveCosmosRepository<TenantOnboardingEntity, String> {

    @Query("SELECT * FROM c WHERE c.tenantId = @tenantId")
    Flux<TenantOnboardingEntity> findByTenantId(@Param("tenantId") String tenantId);

    @Query("SELECT * FROM c WHERE c.tenantId = @tenantId AND c.integration = @integration")
    Mono<TenantOnboardingEntity> findByTenantIdAndIntegration(@Param("tenantId") String tenantId,
                                                              @Param("integration") String integration);

    default Mono<Boolean> isTenantOnboarded(String tenantId, Integration integration) {
        return findByTenantIdAndIntegration(tenantId, integration.name())
                .map(entity -> true)
                .defaultIfEmpty(false);
    }
}