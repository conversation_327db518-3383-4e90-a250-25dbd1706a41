package com.illumio.data.model.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import lombok.experimental.UtilityClass;

import java.util.*;

@UtilityClass
public class TenantOnboardingMapper {

    public static TenantOnboardingEntity toTenantOnboardingEntity(final TenantOnboarding tenantOnboarding) {
        return new TenantOnboardingEntity(
                tenantOnboarding.getTenantId(),
                tenantOnboarding.getIntegration(),
                tenantOnboarding.getCredentialsVaultPath(),
                tenantOnboarding.getConfigurations(),
                toObjectMap(tenantOnboarding.getTenantMetadata()));
    }

    public static TenantOnboarding toTenantOnboarding(final TenantOnboardingEntity tenantOnboardingEntity) {
        final Integration integration = tenantOnboardingEntity.getIntegration();

        TenantMetadata tenantMetadata;
        final ObjectMapper objectMapper = new ObjectMapper();
        tenantMetadata = objectMapper.convertValue(
                tenantOnboardingEntity.getTenantMetadata(),
                TenantMetadata.getTenantMetadataClass(integration));

        return TenantOnboarding.builder()
                               .tenantId(tenantOnboardingEntity.getTenantId())
                               .integration(integration)
                               .credentialsVaultPath(tenantOnboardingEntity.getCredentialsVaultPath())
                               .configurations(objectMapper.convertValue(tenantOnboardingEntity.getConfigurations(), TenantConfigurations.class))
                               .tenantMetadata(tenantMetadata)
                               .build();
    }

    private static Map<String, Object> toObjectMap(final Object o) {
        return new ObjectMapper().convertValue(o, new TypeReference<>() {});
    }

}