package com.illumio.data.model.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.entities.TenantOnboardingEntity;
import com.illumio.data.model.*;
import lombok.experimental.UtilityClass;

@UtilityClass
public class TenantOnboardingMapper {

    public static TenantOnboardingEntity toTenantOnboardingEntity(final TenantOnboarding tenantOnboarding,
                                                                  final ObjectMapper objectMapper) {
        return new TenantOnboardingEntity(
                tenantOnboarding.getTenantId(),
                tenantOnboarding.getIntegration(),
                tenantOnboarding.getCredentialsVaultPath(),
                tenantOnboarding.getConfigurations(),
                objectMapper.convertValue(tenantOnboarding.getTenantMetadata(), new TypeReference<>() {}));
    }

    public static TenantOnboarding toTenantOnboarding(final TenantOnboardingEntity tenantOnboardingEntity,
                                                      final ObjectMapper objectMapper) {
        final Integration integration = tenantOnboardingEntity.getIntegration();

        TenantMetadata tenantMetadata;
        tenantMetadata = objectMapper.convertValue(
                tenantOnboardingEntity.getTenantMetadata(),
                TenantMetadata.getTenantMetadataClass(integration));

        return TenantOnboarding.builder()
                               .tenantId(tenantOnboardingEntity.getTenantId())
                               .integration(integration)
                               .credentialsVaultPath(tenantOnboardingEntity.getCredentialsVaultPath())
                               .configurations(objectMapper.convertValue(tenantOnboardingEntity.getConfigurations(), TenantConfigurations.class))
                               .tenantMetadata(tenantMetadata)
                               .build();
    }

}