package com.illumio.data.repositories;
import com.illumio.data.entities.VulnerabilityMetaDataEntity;
import com.illumio.data.metrics.QueryExecutionTimerMetric;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@QueryExecutionTimerMetric
@Repository
public interface VulnerabilityMetaDataRepository extends ReactiveCrudRepository<VulnerabilityMetaDataEntity, String> {
    @Query("""
        INSERT INTO vulnerability (cve_id, cve_description, cvss_severity, score, has_exploit, has_cisa_kev_exploit)
        VALUES (:#{#entity.cveId}, :#{#entity.cveDescription}, :#{#entity.cvssSeverity}, 
                :#{#entity.score}, :#{#entity.hasExploit}, :#{#entity.hasCisaKevExploit})
        ON CONFLICT(cve_id) DO UPDATE SET
            cve_description = :#{#entity.cveDescription},
            cvss_severity = :#{#entity.cvssSeverity},
            score = :#{#entity.score},
            has_exploit = :#{#entity.hasExploit},
            has_cisa_kev_exploit = :#{#entity.hasCisaKevExploit}
        RETURNING *
    """)
    Mono<VulnerabilityMetaDataEntity> upsertVulnerabilityMetadata(@Param("entity") VulnerabilityMetaDataEntity entity);
}


