package com.illumio.data.model;

import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class Vulnerability extends Finding {

    private VulnerabilityMetadata metadata;

    @Override
    public FindingType getFindingsType() {
        return FindingType.VULNERABILITY;
    }
}