CREATE TABLE IF NOT EXISTS task_status (
    task_id VARCHAR(1000) PRIMARY KEY,
    status VARCHAR(100),
    tenant_id VARCHAR(100),
    integration VARCHAR(100),
    schedule_time TIMESTAMP WITH TIME ZONE,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    last_update_time TIMESTAMP WITH TIME ZONE,
    task_type VARCHAR(100),
    status_message TEXT
    );

CREATE INDEX IF NOT EXISTS idx_task_status_tenant_id ON task_status(tenant_id);

CREATE TABLE IF NOT EXISTS retry_messages (
    task_id VARCHAR(1000),
    message TEXT,
    end_time TIMESTAMP WITH TIME ZONE,PRIMARY KEY (task_id, end_time),
    FOREIGN KEY (task_id) REFERENCES task_status(task_id)
    );

CREATE INDEX IF NOT EXISTS idx_retry_messages_task_id_end_time ON retry_messages(task_id, end_time);