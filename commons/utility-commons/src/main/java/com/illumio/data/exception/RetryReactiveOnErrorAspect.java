package com.illumio.data.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.RetryConfig;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RetryReactiveOnErrorAspect {

    private final RetryConfig retryConfig;
    private final ObjectMapper objectMapper;

    @Around("@annotation(retryAnnotation)")
    public Object retryReactiveOnError(ProceedingJoinPoint joinPoint, RetryReactiveOnError retryAnnotation) throws Throwable {
        // Proceed with method execution
        Object result = joinPoint.proceed();

        if (result instanceof Mono<?>) {
            return ((Mono<?>) result)
                    .retryWhen(getRetrySpec(joinPoint, retryAnnotation.userOperation()));
        } else if (result instanceof Flux<?>) {
            return ((Flux<?>) result)
                    .retryWhen(getRetrySpec(joinPoint, retryAnnotation.userOperation()));
        }

        // Handle non-reactive case gracefully
        return result;
    }

    private RetryConfig.RetryParameters getRetryParameters(boolean userOperation) {
        return userOperation ? retryConfig.getUserOperations() : retryConfig.getSystemOperations();
    }

    private Retry getRetrySpec(ProceedingJoinPoint joinPoint, boolean userOperation) {
        RetryConfig.RetryParameters retryParameters = getRetryParameters(userOperation);
        return Retry.backoff(retryParameters.getMaxRetries(),
                     retryParameters.getMinBackoff())
             .doBeforeRetry(retrySignal -> {
                 logRetryError(joinPoint,false, retrySignal);
             })
             .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                 logRetryError(joinPoint, true, retrySignal);
                 return retrySignal.failure();
             });
    }

    @SneakyThrows
    private void logRetryError(ProceedingJoinPoint joinPoint, boolean retriesExhausted, Retry.RetrySignal retrySignal) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String serializedParams = objectMapper.writeValueAsString(joinPoint.getArgs());
        String logRetryPostfix = retriesExhausted ? "retries exhausted." : "retrying..." ;
        log.error("Error occurred during process to {} within class {} with params {}. retryNumber={}, {}",
                methodName, className, serializedParams, retrySignal.totalRetries(), logRetryPostfix, retrySignal.failure());
    }
}