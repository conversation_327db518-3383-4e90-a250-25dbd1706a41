package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "retry-config")
@Getter
@Setter
public class RetryConfig {

    private RetryParameters userOperations = new RetryParameters();
    private RetryParameters systemOperations = new RetryParameters();

    @Configuration
    @Getter
    @Setter
    public static class RetryParameters {

        private Integer maxRetries = 2;
        private Duration minBackoff = Duration.ofSeconds(30);

    }

}