package com.illumio.data.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

@Data
public class ApiResponse {
    private final boolean success;
    private final Object data;
    private final Integer statusCode;
    private final String errorMessage;
    private final String errorBody;
    private final ObjectMapper objectMapper;

    private ApiResponse(boolean success, Object data, Integer statusCode, String errorMessage, String errorBody, ObjectMapper objectMapper) {
        this.success = success;
        this.data = data;
        this.statusCode = statusCode;
        this.errorMessage = errorMessage;
        this.errorBody = errorBody;
        this.objectMapper = objectMapper;
    }

    public static ApiResponse success(Object data, ObjectMapper objectMapper) {
        return new ApiResponse(true, data, 200, null, null, objectMapper);
    }

    public static ApiResponse error(Integer statusCode, String errorBody, String errorMessage) {
        return new ApiResponse(false, null, statusCode, errorMessage, errorBody, null);
    }

    public JsonNode getDataAsJsonNode() {
        if (data == null || objectMapper == null) return null;
        return objectMapper.valueToTree(data);
    }
}
