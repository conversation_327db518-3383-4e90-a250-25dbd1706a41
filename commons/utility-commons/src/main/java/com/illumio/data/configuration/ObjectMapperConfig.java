package com.illumio.data.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Jackson ObjectMapper bean.
 *
 * <p>Creating a Spring Bean for ObjectMapper provides several benefits:
 * <ul>
 *   <li><strong>Consistent Configuration:</strong> Ensures the same JSON processing settings
 *       are used across the entire application</li>
 *   <li><strong>Dependency Injection:</strong> Enables easy injection into components and
 *       simplifies unit testing with mocks</li>
 *   <li><strong>Single Point of Customization:</strong> Provides a centralized location for
 *       configuring JSON processing behavior (date formats, null handling, property naming, etc.)</li>
 *   <li><strong>Performance:</strong> Reuses the same ObjectMapper instance rather than creating
 *       new instances throughout the application</li>
 * </ul>
 */
@Configuration
public class ObjectMapperConfig {

    /**
     * Creates and configures the Jackson ObjectMapper bean.
     *
     * @return a configured ObjectMapper instance for JSON processing
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper();
    }
}
