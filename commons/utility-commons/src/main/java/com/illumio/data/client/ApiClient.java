package com.illumio.data.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.ApiRequest;
import com.illumio.data.model.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;


import java.util.Objects;

@Component
@RequiredArgsConstructor
public class ApiClient {

    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public Mono<ApiResponse> execute(ApiRequest request) {
        WebClient.RequestBodySpec requestSpec = webClient
                .method(request.getHttpMethod())
                .uri(request.getUrl(), uriBuilder -> {
                    if (request.getQueryParams() != null) {
                        request.getQueryParams().forEach(uriBuilder::queryParam);
                    }
                    return uriBuilder.build();
                });

        if (request.getHeaders() != null) {
            request.getHeaders().forEach(requestSpec::header);
        }

        if (request.getBody() != null) {
            requestSpec.bodyValue(request.getBody());
        }

        return requestSpec
                .retrieve()
                .bodyToMono(Object.class)
                .map(body -> ApiResponse.success(body, objectMapper))
                .onErrorResume(
                        WebClientResponseException.class,
                        ex -> {
                            if (ex.getStatusCode() == HttpStatus.OK) {
                                final String errorMessage = String.format("Error observed in WebClient call: %s",
                                        Objects.requireNonNull(ex.getRootCause()).getMessage());
                                return Mono.error(new Exception(errorMessage));
                            }
                            return Mono.error(ex);
                        });
    }


}
