package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import static com.illumio.data.util.UtilityCommonsConstants.DEFAULT_MAX_IN_MEMORY;

@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "web-client-config")
public class WebClientConfig{

    private Integer maxInMemorySize = DEFAULT_MAX_IN_MEMORY;

    @Bean
    public WebClient webClient(WebClient.Builder webClientBuilder, WebClientConfig webClientConfig) {
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                                                          .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(webClientConfig.getMaxInMemorySize()))
                                                          .build();

        return webClientBuilder
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .exchangeStrategies(strategies).build();
    }
}
