package com.illumio.data.exception;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.MissingRequestValueException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.UtilityCommonsConstants.INTERNAL_SERVER_ERROR_MESSAGE;
import static com.illumio.data.util.UtilityCommonsConstants.UNAUTHORIZED_ERROR_MESSAGE;

@ControllerAdvice
public class GlobalExceptionHandler {
    /**
     * Handles the {@link ResourceNotFoundException} by returning an HTTP 200 OK status with an empty body.
     *
     * @param ex the exception that was thrown
     * @param exchange the current server web exchange
     * @return a {@link Mono} emitting a {@link ResponseEntity} with HTTP status 200 OK and an empty body
     *
     * The UI picks up error response HTTP codes and retries the call, after which it displays the error to the UI.
     * Since we do not want to show the error for not found scenarios, we are responding with an HTTP status 200 OK and an empty body.
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public Mono<ResponseEntity<?>> handleResourceNotFoundException(ResourceNotFoundException ex, ServerWebExchange exchange) {
        return Mono.just(ResponseEntity.status(HttpStatus.OK).body(""));
    }

    @ExceptionHandler({BadRequestException.class, MissingRequestValueException.class})
    public Mono<ResponseEntity<ErrorResponse>> handleBadRequestException(BadRequestException ex, ServerWebExchange exchange) {
        ErrorResponse errorResponse = new ErrorResponse(ex.getMessage());
        return Mono.just(ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse));
    }

    @ExceptionHandler(ExternalServiceException.class)
    public Mono<ResponseEntity<ErrorResponse>> handleExternalException(ExternalServiceException ex, ServerWebExchange exchange) {
        ErrorResponse errorResponse = new ErrorResponse(ex.getMessage());
        return Mono.just(ResponseEntity.status(HttpStatus.FAILED_DEPENDENCY).body(errorResponse));
    }

    @ExceptionHandler(UnauthorizedException.class)
    public Mono<ResponseEntity<ErrorResponse>> handleUnauthorizedException(UnauthorizedException ex, ServerWebExchange exchange) {
        ErrorResponse errorResponse = new ErrorResponse(UNAUTHORIZED_ERROR_MESSAGE);
        return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse));
    }

    @ExceptionHandler(Exception.class)
    public Mono<ResponseEntity<ErrorResponse>> handleGenericException(Exception ex, ServerWebExchange exchange) {
        ErrorResponse errorResponse = new ErrorResponse(INTERNAL_SERVER_ERROR_MESSAGE);
        return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse));
    }
}