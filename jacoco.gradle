jacoco {
    toolVersion = "0.8.11"
}

jacocoTestReport {
    reports {
        xml.required = true
        csv.required = true
        html.required = true
        csv.outputLocation = layout.buildDirectory.file("reports/jacoco/test/jacocoTestReport.csv")
        html.outputLocation = layout.buildDirectory.dir("reports/jacoco/html")
    }

    afterEvaluate {
        classDirectories.setFrom(classDirectories.files.collect { dir ->
            fileTree(dir: dir).include('com/illumio/data/components/**')
                    .exclude('com/illumio/data/components/helper/**',
                            'com/illumio/data/components/model/**',
                            'com/illumio/data/components/visitor/**',
                            'com/illumio/data/components/scripts/**',
                            'com/illumio/data/components/exception/**')
        })
    }
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.8 // Set your desired coverage threshold
            }
        }
    }

    afterEvaluate {
        classDirectories.setFrom(classDirectories.files.collect { dir ->
            fileTree(dir: dir).include('com/illumio/data/components/**')
                    .exclude('com/illumio/data/components/helper/**',
                            'com/illumio/data/components/model/**',
                            'com/illumio/data/components/scripts/**',
                            'com/illumio/data/components/visitor/**',
                            'com/illumio/data/components/exception/**')
        })
    }
}

check.dependsOn jacocoTestCoverageVerification
